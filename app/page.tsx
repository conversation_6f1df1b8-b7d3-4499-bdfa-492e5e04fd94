'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Phone, Clock, Home, Calendar, Heart, Shield, Star, ArrowRight, Stethoscope, Scissors, Zap, Activity, Siren, Umbrella, Bug, Plane, Sparkles } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAppointment } from '@/components/AppointmentProvider';
import { smoothScrollToElement } from '@/lib/utils';

export default function HomePage() {
  const { openAppointmentModal } = useAppointment();

  // Handle smooth scrolling when page loads with hash
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const elementId = hash.substring(1); // Remove the # symbol
      setTimeout(() => {
        smoothScrollToElement(elementId);
      }, 100);
    }
  }, []);

  const services = [
    {
      title: "Vaccinations",
      description: "Essential vaccination programs to protect your pet from common diseases and maintain their immunity.",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?auto=format&fit=crop&w=400&h=300",
      icon: Shield
    },
    {
      title: "Health Check-ups",
      description: "Comprehensive health examinations to ensure your pet's optimal wellbeing and early detection of health issues.",
      image: "https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=400&h=300",
      icon: Stethoscope
    },
    {
      title: "Surgical Procedures",
      description: "Professional surgical services including spaying, neutering, and other necessary procedures.",
      image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=300",
      icon: Scissors
    },
    {
      title: "Dental Care",
      description: "Complete dental health services including cleaning, extractions, and oral health maintenance.",
      image: "https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=400&h=300",
      icon: Sparkles
    },
    {
      title: "Diagnostic Testing",
      description: "Advanced diagnostic services including blood work, X-rays, and laboratory testing.",
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?auto=format&fit=crop&w=400&h=300",
      icon: Activity
    },
    {
      title: "Emergency Care",
      description: "24/7 emergency veterinary services for urgent medical situations and critical care.",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?auto=format&fit=crop&w=400&h=300",
      icon: Siren
    },
    {
      title: "Preventative Care",
      description: "Comprehensive preventive treatments to keep your pets healthy and prevent future health issues.",
      image: "https://images.unsplash.com/photo-**********-03cce0bbc87b?auto=format&fit=crop&w=400&h=300",
      icon: Shield
    },
    {
      title: "Parasite Control",
      description: "Comprehensive parasite prevention and treatment programs for fleas, ticks, and worms.",
      image: "https://images.unsplash.com/photo-1583337130417-3346a1be7dee?auto=format&fit=crop&w=400&h=300",
      icon: Bug
    },
    {
      title: "Local and International Travel",
      description: "Health certificates and travel documentation for pets traveling domestically or internationally.",
      image: "https://images.unsplash.com/photo-1436491865332-7a61a109cc05?auto=format&fit=crop&w=400&h=300",
      icon: Plane
    },
    {
      title: "Wash & Grooming",
      description: "Professional grooming services including bathing, nail trimming, and coat care.",
      image: "https://images.unsplash.com/photo-**********-8cc77767d783?auto=format&fit=crop&w=400&h=300",
      icon: Sparkles
    }
  ];

  const features = [
    {
      icon: Home,
      title: "Home Visits",
      description: "No stressful car rides or waiting rooms. We come to you.",
      gradient: "from-blue-100 to-blue-200"
    },
    {
      icon: Calendar,
      title: "Flexible Scheduling",
      description: "Book appointments that fit your busy schedule.",
      gradient: "from-pink-100 to-pink-200"
    },
    {
      icon: Phone,
      title: "24/7 Support",
      description: "Emergency care available around the clock.",
      gradient: "from-gray-100 to-gray-200"
    },
    {
      icon: Clock,
      title: "Quick Response",
      description: "Fast response times for urgent situations.",
      gradient: "from-blue-100 to-pink-100"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20"></div>
        <div className="relative max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
                  <Heart className="w-4 h-4" />
                  Professional Veterinary Care
                </div>
                <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                  Your Pet's Health is Our
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-pink-500"> Priority</span>
                </h1>
                <p className="text-xl text-gray-600 leading-relaxed">
                  CVETS Veterinary Services provides comprehensive, compassionate care for your beloved pets.
                  Led by Dr Cynthia, we offer mobile veterinary services bringing professional care directly to your home.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-6">
                <Button
                  onClick={openAppointmentModal}
                  size="lg"
                  className="bg-gradient-to-r from-blue-500 to-pink-500 hover:from-blue-600 hover:to-pink-600 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
                >
                  <span>Book Appointment</span>
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-2 border-gray-200 bg-white/70 backdrop-blur-sm text-gray-700 hover:bg-gray-50 hover:border-gray-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                >
                  <Link href="/services">View Services</Link>
                </Button>
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-pink-400 rounded-3xl transform rotate-3 opacity-20"></div>
              <div className="relative bg-white rounded-3xl shadow-2xl overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=600&h=400"
                  alt="Veterinarian examining a pet"
                  className="w-full h-96 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-pink-50/30"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20 space-y-6">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-pink-100 rounded-2xl px-8 py-4">
              <Shield className="w-6 h-6 text-blue-600" />
              <span className="text-lg font-semibold text-gray-700">Why Choose Us</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 bg-clip-text text-transparent leading-tight">
              Why Choose Mobile
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-pink-600 bg-clip-text text-transparent">
                Veterinary Care?
              </span>
            </h2>

            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Experience the convenience and comfort of professional veterinary services
              delivered directly to your home with unprecedented care and attention.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <CardContent className="p-8 text-center space-y-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${feature.gradient} rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="w-10 h-10 text-slate-700" />
                  </div>

                  <h3 className="text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                    {feature.title}
                  </h3>

                  <p className="text-slate-600 leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-100/50 via-blue-50/50 to-pink-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20 space-y-6">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-pink-100 to-blue-100 rounded-2xl px-8 py-4">
              <Heart className="w-6 h-6 text-pink-600" />
              <span className="text-lg font-semibold text-gray-700">Our Services</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-pink-900 to-gray-900 bg-clip-text text-transparent leading-tight">
              Comprehensive Care
              <br />
              <span className="bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent">
                Tailored to Your Pet
              </span>
            </h2>

            <p className="text-xl text-gray-600 leading-relaxed">
              Professional veterinary services designed with your pet's comfort and health in mind
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <Card
                key={index}
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <div className="relative h-48 overflow-hidden rounded-t-3xl">
                  <img
                    src={service.image}
                    alt={service.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute top-4 right-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                    <service.icon className="w-6 h-6 text-blue-600" />
                  </div>
                </div>

                <CardContent className="p-8 space-y-4">
                  <h3 className="text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                    {service.title}
                  </h3>

                  <p className="text-slate-600 leading-relaxed">
                    {service.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-16">
            <Button
              asChild
              size="lg"
              className="bg-gradient-to-r from-pink-600 to-blue-700 hover:from-pink-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              <Link href="/services" className="flex items-center space-x-2">
                <span>View All Services</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
              Ready to Schedule Your Pet's
              <br />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                Appointment?
              </span>
            </h2>

            <p className="text-xl text-blue-100 leading-relaxed">
              Book now for convenient, stress-free veterinary care that puts your pet's comfort first
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                onClick={openAppointmentModal}
                size="lg"
                className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <span>Book Online</span>
                <Calendar className="w-5 h-5 ml-2" />
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <a href="tel:0718376311" className="flex items-center space-x-2">
                  <Phone className="w-5 h-5" />
                  <span>Call 0718376311</span>
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
