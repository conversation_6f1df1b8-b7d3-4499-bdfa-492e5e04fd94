'use client';

import { useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAppointment } from '@/components/AppointmentProvider';
import { smoothScrollToElement } from '@/lib/utils';
import { GraduationCap, Award, Heart, Users, Calendar, MapPin, Phone, Star, Shield, Stethoscope } from 'lucide-react';

export default function AboutPage() {
  const { openAppointmentModal } = useAppointment();

  // Handle smooth scrolling when page loads with hash
  useEffect(() => {
    const hash = window.location.hash;
    if (hash) {
      const elementId = hash.substring(1); // Remove the # symbol
      setTimeout(() => {
        smoothScrollToElement(elementId);
      }, 100);
    }
  }, []);

  const qualifications = [
    {
      title: "Doctor of Veterinary Medicine",
      institution: "University of Veterinary Sciences",
      year: "2012",
      icon: GraduationCap,
      color: "from-blue-100 to-cyan-100"
    },
    {
      title: "Mobile Veterinary Certification",
      institution: "Kenya Veterinary Association",
      year: "2014",
      icon: Award,
      color: "from-green-100 to-emerald-100"
    },
    {
      title: "Emergency Care Specialist",
      institution: "Veterinary Emergency Board",
      year: "2016",
      icon: Heart,
      color: "from-red-100 to-rose-100"
    }
  ];

  const achievements = [
    {
      title: "500+ Happy Pets Treated",
      description: "Successfully provided care to over 500 pets in the community",
      icon: Heart,
      color: "text-red-600"
    },
    {
      title: "10+ Years Experience",
      description: "Over a decade of dedicated veterinary practice",
      icon: Award,
      color: "text-blue-600"
    },
    {
      title: "24/7 Emergency Care",
      description: "Round-the-clock availability for emergency situations",
      icon: Shield,
      color: "text-green-600"
    },
    {
      title: "Mobile Service Pioneer",
      description: "Leading mobile veterinary services in the region",
      icon: Stethoscope,
      color: "text-purple-600"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50">
      <Header />

      {/* Hero Section */}
      <section className="relative pt-20 pb-16 px-4 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-cyan-500/5 to-slate-100/20"></div>
        <div className="relative max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium">
                  <Heart className="w-4 h-4" />
                  Meet Our Veterinarian
                </div>
                <h1 className="text-5xl lg:text-6xl font-bold text-slate-900 leading-tight">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600">Dr Cynthia</span>
                </h1>
                <p className="text-xl text-slate-600 leading-relaxed">
                  A passionate veterinarian dedicated to providing exceptional mobile veterinary care.
                  With over 10 years of experience, Dr Cynthia brings professional, compassionate care directly to your home.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-6">
                <Button
                  onClick={openAppointmentModal}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
                >
                  <span>Book Appointment</span>
                  <Calendar className="w-5 h-5 ml-2" />
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-2 border-slate-200 bg-white/70 backdrop-blur-sm text-slate-700 hover:bg-slate-50 hover:border-slate-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                >
                  <a href="tel:0718376311" className="flex items-center space-x-2">
                    <Phone className="w-5 h-5" />
                    <span>Call Now</span>
                  </a>
                </Button>
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-3xl transform rotate-3 opacity-20"></div>
              <div className="relative bg-white rounded-3xl shadow-2xl overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=600&h=400"
                  alt="Dr Cynthia"
                  className="w-full h-96 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Qualifications Section */}
      <section className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-green-50/30"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20 space-y-6">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-green-100 rounded-2xl px-8 py-4">
              <GraduationCap className="w-6 h-6 text-blue-600" />
              <span className="text-lg font-semibold text-slate-700">Qualifications</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent leading-tight">
              Professional
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                Credentials
              </span>
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {qualifications.map((qualification, index) => (
              <Card
                key={index}
                className="group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl"
              >
                <CardContent className="p-8 text-center space-y-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${qualification.color} rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <qualification.icon className="w-10 h-10 text-slate-700" />
                  </div>

                  <div className="space-y-2">
                    <h3 className="text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300">
                      {qualification.title}
                    </h3>
                    <p className="text-slate-600">{qualification.institution}</p>
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      {qualification.year}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Achievements Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <Card
                key={index}
                className="text-center bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl hover:-translate-y-1"
              >
                <CardContent className="p-8 space-y-4">
                  <achievement.icon className={`w-12 h-12 mx-auto ${achievement.color}`} />
                  <div className="space-y-2">
                    <h3 className="text-lg font-bold text-slate-800">{achievement.title}</h3>
                    <p className="text-slate-600 text-sm">{achievement.description}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section id="about-story" className="py-32 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16 space-y-6">
              <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent">
                My Journey to
                <br />
                <span className="bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                  Mobile Veterinary Care
                </span>
              </h2>
            </div>

            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-2">
                <div className="relative h-64 lg:h-full">
                  <img
                    src="https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=600&h=400"
                    alt="Veterinarian with pets"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-green-600/20"></div>
                </div>

                <CardContent className="p-12 space-y-6">
                  <h3 className="text-2xl font-bold text-slate-800">A Passion Born from Love</h3>

                  <div className="space-y-4 text-slate-600 leading-relaxed">
                    <p>
                      My journey into veterinary medicine began with a simple love for animals and a desire to make a difference in their lives.
                      After completing my Doctor of Veterinary Medicine degree in 2012, I quickly realized that traditional clinic visits could be stressful for both pets and their families.
                    </p>

                    <p>
                      This realization led me to pursue mobile veterinary services, bringing professional care directly to the comfort of your home.
                      Over the past decade, I've had the privilege of caring for over 500 beloved pets, building lasting relationships with families throughout our community.
                    </p>

                    <p>
                      My commitment to excellence drives me to stay current with the latest veterinary practices and technologies,
                      ensuring that every pet receives the highest standard of care in the familiar environment of their own home.
                    </p>
                  </div>

                  <div className="flex items-center space-x-4 pt-4">
                    <div className="flex space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <span className="text-slate-600 font-medium">Trusted by 500+ pet families</span>
                  </div>
                </CardContent>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto space-y-8">
            <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
              Ready to Experience
              <br />
              <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                Compassionate Care?
              </span>
            </h2>

            <p className="text-xl text-blue-100 leading-relaxed">
              Schedule your pet's appointment today and discover the convenience of professional veterinary care at home
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <Button
                onClick={openAppointmentModal}
                size="lg"
                className="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <span>Book Appointment</span>
                <Calendar className="w-5 h-5 ml-2" />
              </Button>
              <Button
                asChild
                size="lg"
                variant="outline"
                className="text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
              >
                <a href="tel:0718376311" className="flex items-center space-x-2">
                  <Phone className="w-5 h-5" />
                  <span>Call 0718376311</span>
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
