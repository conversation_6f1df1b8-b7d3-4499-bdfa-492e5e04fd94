import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import { Providers } from './providers'
import { AppointmentProvider } from '@/components/AppointmentProvider'
import '@/index.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  metadataBase: new URL('https://cvets-veterinary.vercel.app'),
  title: {
    default: 'cvets',
    template: '%s | cvets'
  },
  description: 'CVETS Veterinary Services provides comprehensive pet care including health check-ups, surgical procedures, dental care, and emergency services. Led by <PERSON>.',
  keywords: 'veterinary, pet care, animal health, Dr Cynthia, CVETS, veterinary clinic',
  authors: [{ name: 'CVETS Veterinary Services' }],
  creator: 'CVETS Veterinary Services',
  publisher: 'CVETS Veterinary Services',
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    title: 'CVETS - Professional Veterinary Services',
    description: 'CVETS Veterinary Services provides comprehensive pet care including health check-ups, surgical procedures, dental care, and emergency services. Led by <PERSON>.',
    type: 'website',
    locale: 'en_KE',
    url: 'https://cvets-veterinary.vercel.app',
    siteName: 'CVETS Veterinary Services',
    images: [
      {
        url: 'https://lovable.dev/opengraph-image-p98pqg.png',
        width: 1200,
        height: 630,
        alt: 'CVETS Veterinary Services - Professional Pet Care',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CVETS - Professional Veterinary Services',
    description: 'CVETS Veterinary Services provides comprehensive pet care including health check-ups, surgical procedures, dental care, and emergency services.',
    site: '@cvets',
    creator: '@cvets',
    images: ['https://lovable.dev/opengraph-image-p98pqg.png'],
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
}



export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <Providers>
          <AppointmentProvider>
            {children}
          </AppointmentProvider>
        </Providers>
      </body>
    </html>
  )
}
