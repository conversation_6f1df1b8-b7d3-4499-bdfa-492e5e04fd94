exports.id=918,exports.ids=[918],exports.modules={371:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=a(6158),s=i.__importStar(a(9885)),u=a(3157),d=i.__importDefault(a(3394)),f=s.forwardRef(function(e,n){return s.createElement(u.RemoveScroll,i.__assign({},e,{ref:n,sideCar:d.default}))});f.classNames=u.RemoveScroll.classNames,n.default=f},670:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveScrollSideCar=n.getDeltaXY=n.getTouchXY=void 0;var i=a(6158),s=i.__importStar(a(9885)),u=a(2786),d=a(7034),f=a(3881),p=a(8997);n.getTouchXY=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},n.getDeltaXY=function(e){return[e.deltaX,e.deltaY]};var extractRef=function(e){return e&&"current"in e?e.current:e},h=0,m=[];n.RemoveScrollSideCar=function(e){var a=s.useRef([]),g=s.useRef([0,0]),v=s.useRef(),y=s.useState(h++)[0],b=s.useState(d.styleSingleton)[0],_=s.useRef(e);s.useEffect(function(){_.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(y));var n=i.__spreadArray([e.lockRef.current],(e.shards||[]).map(extractRef),!0).filter(Boolean);return n.forEach(function(e){return e.classList.add("allow-interactivity-".concat(y))}),function(){document.body.classList.remove("block-interactivity-".concat(y)),n.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(y))})}}},[e.inert,e.lockRef.current,e.shards]);var w=s.useCallback(function(e,a){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!_.current.allowPinchZoom;var i,s=(0,n.getTouchXY)(e),u=g.current,d="deltaX"in e?e.deltaX:u[0]-s[0],f="deltaY"in e?e.deltaY:u[1]-s[1],h=e.target,m=Math.abs(d)>Math.abs(f)?"h":"v";if("touches"in e&&"h"===m&&"range"===h.type)return!1;var y=(0,p.locationCouldBeScrolled)(m,h);if(!y)return!0;if(y?i=m:(i="v"===m?"h":"v",y=(0,p.locationCouldBeScrolled)(m,h)),!y)return!1;if(!v.current&&"changedTouches"in e&&(d||f)&&(v.current=i),!i)return!0;var b=v.current||i;return(0,p.handleScroll)(b,a,e,"h"===b?d:f,!0)},[]),x=s.useCallback(function(e){if(m.length&&m[m.length-1]===b){var i="deltaY"in e?(0,n.getDeltaXY)(e):(0,n.getTouchXY)(e),s=a.current.filter(function(n){var a;return n.name===e.type&&(n.target===e.target||e.target===n.shadowParent)&&(a=n.delta)[0]===i[0]&&a[1]===i[1]})[0];if(s&&s.should){e.cancelable&&e.preventDefault();return}if(!s){var u=(_.current.shards||[]).map(extractRef).filter(Boolean).filter(function(n){return n.contains(e.target)});(u.length>0?w(e,u[0]):!_.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),E=s.useCallback(function(e,n,i,s){var u={name:e,delta:n,target:i,should:s,shadowParent:function(e){for(var n=null;null!==e;)e instanceof ShadowRoot&&(n=e.host,e=e.host),e=e.parentNode;return n}(i)};a.current.push(u),setTimeout(function(){a.current=a.current.filter(function(e){return e!==u})},1)},[]),S=s.useCallback(function(e){g.current=(0,n.getTouchXY)(e),v.current=void 0},[]),P=s.useCallback(function(a){E(a.type,(0,n.getDeltaXY)(a),a.target,w(a,e.lockRef.current))},[]),R=s.useCallback(function(a){E(a.type,(0,n.getTouchXY)(a),a.target,w(a,e.lockRef.current))},[]);s.useEffect(function(){return m.push(b),e.setCallbacks({onScrollCapture:P,onWheelCapture:P,onTouchMoveCapture:R}),document.addEventListener("wheel",x,f.nonPassive),document.addEventListener("touchmove",x,f.nonPassive),document.addEventListener("touchstart",S,f.nonPassive),function(){m=m.filter(function(e){return e!==b}),document.removeEventListener("wheel",x,f.nonPassive),document.removeEventListener("touchmove",x,f.nonPassive),document.removeEventListener("touchstart",S,f.nonPassive)}},[]);var C=e.removeScrollBar,O=e.inert;return s.createElement(s.Fragment,null,O?s.createElement(b,{styles:"\n  .block-interactivity-".concat(y," {pointer-events: none;}\n  .allow-interactivity-").concat(y," {pointer-events: all;}\n")}):null,C?s.createElement(u.RemoveScrollBar,{gapMode:e.gapMode}):null)}},3157:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveScroll=void 0;var i=a(6158),s=i.__importStar(a(9885)),u=a(9461),d=a(3609),f=a(7638),nothing=function(){},p=s.forwardRef(function(e,n){var a=s.useRef(null),u=s.useState({onScrollCapture:nothing,onWheelCapture:nothing,onTouchMoveCapture:nothing}),p=u[0],h=u[1],m=e.forwardProps,g=e.children,v=e.className,y=e.removeScrollBar,b=e.enabled,_=e.shards,w=e.sideCar,x=e.noIsolation,E=e.inert,S=e.allowPinchZoom,P=e.as,R=void 0===P?"div":P,C=e.gapMode,O=i.__rest(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=(0,d.useMergeRefs)([a,n]),M=i.__assign(i.__assign({},O),p);return s.createElement(s.Fragment,null,b&&s.createElement(w,{sideCar:f.effectCar,removeScrollBar:y,shards:_,noIsolation:x,inert:E,setCallbacks:h,allowPinchZoom:!!S,lockRef:a,gapMode:C}),m?s.cloneElement(s.Children.only(g),i.__assign(i.__assign({},M),{ref:j})):s.createElement(R,i.__assign({},M,{className:v,ref:j}),g))});n.RemoveScroll=p,p.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},p.classNames={fullWidth:u.fullWidthClassName,zeroRight:u.zeroRightClassName}},3881:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.nonPassive=void 0;var a=!1;if("undefined"!=typeof window)try{var i=Object.defineProperty({},"passive",{get:function(){return a=!0,!0}});window.addEventListener("test",i,i),window.removeEventListener("test",i,i)}catch(e){a=!1}n.nonPassive=!!a&&{passive:!1}},8997:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.handleScroll=n.locationCouldBeScrolled=void 0;var elementCanBeScrolled=function(e,n){if(!(e instanceof Element))return!1;var a=window.getComputedStyle(e);return"hidden"!==a[n]&&!(a.overflowY===a.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===a[n])};n.locationCouldBeScrolled=function(e,n){var a=n.ownerDocument,i=n;do{if("undefined"!=typeof ShadowRoot&&i instanceof ShadowRoot&&(i=i.host),elementCouldBeScrolled(e,i)){var s=getScrollVariables(e,i);if(s[1]>s[2])return!0}i=i.parentNode}while(i&&i!==a.body);return!1};var elementCouldBeScrolled=function(e,n){return"v"===e?elementCanBeScrolled(n,"overflowY"):elementCanBeScrolled(n,"overflowX")},getScrollVariables=function(e,n){return"v"===e?[n.scrollTop,n.scrollHeight,n.clientHeight]:[n.scrollLeft,n.scrollWidth,n.clientWidth]};n.handleScroll=function(e,n,a,i,s){var u,d=(u=window.getComputedStyle(n).direction,"h"===e&&"rtl"===u?-1:1),f=d*i,p=a.target,h=n.contains(p),m=!1,g=f>0,v=0,y=0;do{var b=getScrollVariables(e,p),_=b[0],w=b[1]-b[2]-d*_;(_||w)&&elementCouldBeScrolled(e,p)&&(v+=w,y+=_),p=p instanceof ShadowRoot?p.host:p.parentNode}while(!h&&p!==document.body||h&&(n.contains(p)||n===p));return g&&(s&&1>Math.abs(v)||!s&&f>v)?m=!0:!g&&(s&&1>Math.abs(y)||!s&&-f>y)&&(m=!0),m}},5286:(e,n,a)=>{"use strict";n.f=void 0;var i=a(6158).__importDefault(a(371));n.f=i.default},7638:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.effectCar=void 0;var i=a(8221);n.effectCar=(0,i.createSidecarMedium)()},3394:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=a(8221),s=a(670),u=a(7638);n.default=(0,i.exportSidecar)(u.effectCar,s.RemoveScrollSideCar)},4348:(e,n)=>{"use strict";n.J_=n.zJ=n.Ry=void 0;var getDefaultParent=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},a=new WeakMap,i=new WeakMap,s={},u=0,unwrapHost=function(e){return e&&(e.host||unwrapHost(e.parentNode))},applyAttributeToOthers=function(e,n,d,f){var p=(Array.isArray(e)?e:[e]).map(function(e){if(n.contains(e))return e;var a=unwrapHost(e);return a&&n.contains(a)?a:(console.error("aria-hidden",e,"in not contained inside",n,". Doing nothing"),null)}).filter(function(e){return!!e});s[d]||(s[d]=new WeakMap);var h=s[d],m=[],g=new Set,v=new Set(p),keep=function(e){!e||g.has(e)||(g.add(e),keep(e.parentNode))};p.forEach(keep);var deep=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(g.has(e))deep(e);else try{var n=e.getAttribute(f),s=null!==n&&"false"!==n,u=(a.get(e)||0)+1,p=(h.get(e)||0)+1;a.set(e,u),h.set(e,p),m.push(e),1===u&&s&&i.set(e,!0),1===p&&e.setAttribute(d,"true"),s||e.setAttribute(f,"true")}catch(n){console.error("aria-hidden: cannot operate on ",e,n)}})};return deep(n),g.clear(),u++,function(){m.forEach(function(e){var n=a.get(e)-1,s=h.get(e)-1;a.set(e,n),h.set(e,s),n||(i.has(e)||e.removeAttribute(f),i.delete(e)),s||e.removeAttribute(d)}),--u||(a=new WeakMap,a=new WeakMap,i=new WeakMap,s={})}};n.Ry=function(e,n,a){void 0===a&&(a="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),s=n||getDefaultParent(e);return s?(i.push.apply(i,Array.from(s.querySelectorAll("[aria-live]"))),applyAttributeToOthers(i,s,a,"aria-hidden")):function(){return null}},n.zJ=function(e,n,a){void 0===a&&(a="data-inert-ed");var i=n||getDefaultParent(e);return i?applyAttributeToOthers(e,i,a,"inert"):function(){return null}},n.J_=function(){return"undefined"!=typeof HTMLElement&&HTMLElement.prototype.hasOwnProperty("inert")}},882:e=>{e.exports.isNode="[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0)},7643:(e,n,a)=>{"use strict";var i;Object.defineProperty(n,"__esModule",{value:!0}),n.setNonce=function(e){i=e},n.getNonce=function(){return i||a.nc}},2125:(e,n,a)=>{"use strict";a.d(n,{Z:()=>createLucideIcon});var i=a(9885);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),mergeClasses=(...e)=>e.filter((e,n,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,i.forwardRef)(({color:e="currentColor",size:n=24,strokeWidth:a=2,absoluteStrokeWidth:u,className:d="",children:f,iconNode:p,...h},m)=>(0,i.createElement)("svg",{ref:m,...s,width:n,height:n,stroke:e,strokeWidth:u?24*Number(a)/Number(n):a,className:mergeClasses("lucide",d),...h},[...p.map(([e,n])=>(0,i.createElement)(e,n)),...Array.isArray(f)?f:[f]])),createLucideIcon=(e,n)=>{let a=(0,i.forwardRef)(({className:a,...s},d)=>(0,i.createElement)(u,{ref:d,iconNode:n,className:mergeClasses(`lucide-${toKebabCase(e)}`,a),...s}));return a.displayName=`${e}`,a}},1264:(e,n,a)=>{"use strict";a.d(n,{Z:()=>s});var i=a(2125);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},9458:(e,n,a)=>{"use strict";a.d(n,{Z:()=>s});var i=a(2125);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},4678:(e,n,a)=>{"use strict";a.d(n,{Z:()=>s});var i=a(2125);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},5441:(e,n,a)=>{"use strict";a.d(n,{Z:()=>s});var i=a(2125);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7371:(e,n,a)=>{"use strict";a.d(n,{Z:()=>s});var i=a(2125);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},169:(e,n,a)=>{"use strict";a.d(n,{Z:()=>s});var i=a(2125);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.Z)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},4571:(e,n,a)=>{"use strict";a.d(n,{Z:()=>s});var i=a(2125);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},3680:(e,n,a)=>{"use strict";a.d(n,{Z:()=>s});var i=a(2125);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},6206:(e,n,a)=>{"use strict";a.d(n,{Z:()=>s});var i=a(2125);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,i.Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},1072:(e,n,a)=>{var i,s,u=Object.create,d=Object.defineProperty,f=Object.getOwnPropertyDescriptor,p=Object.getOwnPropertyNames,h=Object.getPrototypeOf,m=Object.prototype.hasOwnProperty,T=(e,n,a,i)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let s of p(n))m.call(e,s)||s===a||d(e,s,{get:()=>n[s],enumerable:!(i=f(n,s))||i.enumerable});return e},g={};((e,n)=>{for(var a in n)d(e,a,{get:n[a],enumerable:!0})})(g,{ThemeProvider:()=>B,useTheme:()=>q}),e.exports=T(d({},"__esModule",{value:!0}),g);var v=(s=null!=(i=a(9885))?u(h(i)):{},T(i&&i.__esModule?s:d(s,"default",{value:i,enumerable:!0}),i)),y=["light","dark"],b="(prefers-color-scheme: dark)",_="undefined"==typeof window,w=v.createContext(void 0),x={setTheme:e=>{},themes:[]},q=()=>{var e;return null!=(e=v.useContext(w))?e:x},B=e=>v.useContext(w)?e.children:v.createElement(G,{...e}),E=["light","dark"],G=({forcedTheme:e,disableTransitionOnChange:n=!1,enableSystem:a=!0,enableColorScheme:i=!0,storageKey:s="theme",themes:u=E,defaultTheme:d=a?"system":"light",attribute:f="data-theme",value:p,children:h,nonce:m})=>{let[g,_]=v.useState(()=>I(s,d)),[x,P]=v.useState(()=>I(s)),R=p?Object.values(p):u,C=v.useCallback(e=>{let s=e;if(!s)return;"system"===e&&a&&(s=A());let u=p?p[s]:s,h=n?X():null,m=document.documentElement;if("class"===f?(m.classList.remove(...R),u&&m.classList.add(u)):u?m.setAttribute(f,u):m.removeAttribute(f),i){let e=y.includes(d)?d:null,n=y.includes(s)?s:e;m.style.colorScheme=n}null==h||h()},[]),O=v.useCallback(e=>{let n="function"==typeof e?e(e):e;_(n);try{localStorage.setItem(s,n)}catch(e){}},[e]),j=v.useCallback(n=>{P(A(n)),"system"===g&&a&&!e&&C("system")},[g,e]);v.useEffect(()=>{let e=window.matchMedia(b);return e.addListener(j),j(e),()=>e.removeListener(j)},[j]),v.useEffect(()=>{let o=e=>{e.key===s&&O(e.newValue||d)};return window.addEventListener("storage",o),()=>window.removeEventListener("storage",o)},[O]),v.useEffect(()=>{C(null!=e?e:g)},[e,g]);let M=v.useMemo(()=>({theme:g,setTheme:O,forcedTheme:e,resolvedTheme:"system"===g?x:g,themes:a?[...u,"system"]:u,systemTheme:a?x:void 0}),[g,O,e,x,a,u]);return v.createElement(w.Provider,{value:M},v.createElement(S,{forcedTheme:e,disableTransitionOnChange:n,enableSystem:a,enableColorScheme:i,storageKey:s,themes:u,defaultTheme:d,attribute:f,value:p,children:h,attrs:R,nonce:m}),h)},S=v.memo(({forcedTheme:e,storageKey:n,attribute:a,enableSystem:i,enableColorScheme:s,defaultTheme:u,value:d,attrs:f,nonce:p})=>{let h="system"===u,m="class"===a?`var d=document.documentElement,c=d.classList;c.remove(${f.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${a}',s='setAttribute';`,g=s?(y.includes(u)?u:null)?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${u}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",l=(e,n=!1,i=!0)=>{let u=d?d[e]:e,f=n?e+"|| ''":`'${u}'`,p="";return s&&i&&!n&&y.includes(e)&&(p+=`d.style.colorScheme = '${e}';`),"class"===a?n||u?p+=`c.add(${f})`:p+="null":u&&(p+=`d[s](n,${f})`),p},_=e?`!function(){${m}${l(e)}}()`:i?`!function(){try{${m}var e=localStorage.getItem('${n}');if('system'===e||(!e&&${h})){var t='${b}',m=window.matchMedia(t);if(m.media!==t||m.matches){${l("dark")}}else{${l("light")}}}else if(e){${d?`var x=${JSON.stringify(d)};`:""}${l(d?"x[e]":"e",!0)}}${h?"":"else{"+l(u,!1,!1)+"}"}${g}}catch(e){}}()`:`!function(){try{${m}var e=localStorage.getItem('${n}');if(e){${d?`var x=${JSON.stringify(d)};`:""}${l(d?"x[e]":"e",!0)}}else{${l(u,!1,!1)};}${g}}catch(t){}}();`;return v.createElement("script",{nonce:p,dangerouslySetInnerHTML:{__html:_}})}),I=(e,n)=>{let a;if(!_){try{a=localStorage.getItem(e)||void 0}catch(e){}return a||n}},X=()=>{let e=document.createElement("style");return e.appendChild(document.createTextNode("*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},A=e=>(e||(e=window.matchMedia(b)),e.matches?"dark":"light")},1326:e=>{e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},6879:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"addBasePath",{enumerable:!0,get:function(){return addBasePath}});let i=a(8549),s=a(6945);function addBasePath(e,n){return(0,s.normalizePathTrailingSlash)((0,i.addPathPrefix)(e,""))}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},5422:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"callServer",{enumerable:!0,get:function(){return callServer}});let i=a(3724);async function callServer(e,n){let a=(0,i.getServerActionDispatcher)();if(!a)throw Error("Invariant: missing action dispatcher.");return new Promise((i,s)=>{a({actionId:e,actionArgs:n,resolve:i,reject:s})})}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},3204:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"AppRouterAnnouncer",{enumerable:!0,get:function(){return AppRouterAnnouncer}});let i=a(9885),s=a(8908),u="next-route-announcer";function AppRouterAnnouncer(e){let{tree:n}=e,[a,d]=(0,i.useState)(null);(0,i.useEffect)(()=>{let e=function(){var e;let n=document.getElementsByName(u)[0];if(null==n?void 0:null==(e=n.shadowRoot)?void 0:e.childNodes[0])return n.shadowRoot.childNodes[0];{let e=document.createElement(u);e.style.cssText="position:absolute";let n=document.createElement("div");n.ariaLive="assertive",n.id="__next-route-announcer__",n.role="alert",n.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal";let a=e.attachShadow({mode:"open"});return a.appendChild(n),document.body.appendChild(e),n}}();return d(e),()=>{let e=document.getElementsByTagName(u)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}},[]);let[f,p]=(0,i.useState)(""),h=(0,i.useRef)();return(0,i.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let n=document.querySelector("h1");n&&(e=n.innerText||n.textContent||"")}void 0!==h.current&&h.current!==e&&p(e),h.current=e},[n]),a?(0,s.createPortal)(f,a):null}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4361:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{RSC:function(){return a},ACTION:function(){return i},NEXT_ROUTER_STATE_TREE:function(){return s},NEXT_ROUTER_PREFETCH:function(){return u},NEXT_URL:function(){return d},RSC_CONTENT_TYPE_HEADER:function(){return f},RSC_VARY_HEADER:function(){return p},FLIGHT_PARAMETERS:function(){return h},NEXT_RSC_UNION_QUERY:function(){return m}});let a="RSC",i="Next-Action",s="Next-Router-State-Tree",u="Next-Router-Prefetch",d="Next-Url",f="text/x-component",p=a+", "+s+", "+u+", "+d,h=[[a],[s],[u]],m="_rsc";("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},3724:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{getServerActionDispatcher:function(){return getServerActionDispatcher},urlToUrlWithoutFlightMarker:function(){return urlToUrlWithoutFlightMarker},default:function(){return AppRouter}});let i=a(7795),s=i._(a(9885)),u=a(2428),d=a(7986),f=a(3678),p=a(1706),h=a(1736),m=a(9236),g=a(5365),v=a(9624),y=a(4692),b=a(6879),_=a(3204),w=a(7502),x=a(2226),E=a(9880),S=a(4361),P=a(4978),R=a(9760),C=null,O=null;function getServerActionDispatcher(){return O}let j={refresh:()=>{}};function urlToUrlWithoutFlightMarker(e){let n=new URL(e,location.origin);return n.searchParams.delete(S.NEXT_RSC_UNION_QUERY),n}function isExternalURL(e){return e.origin!==window.location.origin}function HistoryUpdater(e){let{tree:n,pushRef:a,canonicalUrl:i,sync:u}=e;return(0,s.useInsertionEffect)(()=>{let e={__NA:!0,tree:n};a.pendingPush&&(0,p.createHrefFromUrl)(new URL(window.location.href))!==i?(a.pendingPush=!1,window.history.pushState(e,"",i)):window.history.replaceState(e,"",i),u()},[n,a,i,u]),null}let createEmptyCacheNode=()=>({status:u.CacheStates.LAZY_INITIALIZED,data:null,subTreeData:null,parallelRoutes:new Map});function Router(e){let{buildId:n,initialHead:a,initialTree:i,initialCanonicalUrl:g,children:S,assetPrefix:M}=e,N=(0,s.useMemo)(()=>(0,v.createInitialRouterState)({buildId:n,children:S,initialCanonicalUrl:g,initialTree:i,initialParallelRoutes:C,isServer:!0,location:null,initialHead:a}),[n,S,g,i,a]),[{tree:D,cache:k,prefetchCache:L,pushRef:F,focusAndScrollRef:W,canonicalUrl:H,nextUrl:V},z,K]=(0,m.useReducerWithReduxDevtools)(d.reducer,N);(0,s.useEffect)(()=>{C=null},[]);let{searchParams:$,pathname:Y}=(0,s.useMemo)(()=>{let e=new URL(H,"http://n");return{searchParams:e.searchParams,pathname:(0,R.hasBasePath)(e.pathname)?(0,P.removeBasePath)(e.pathname):e.pathname}},[H]),Q=(0,s.useCallback)((e,n,a)=>{(0,s.startTransition)(()=>{z({type:f.ACTION_SERVER_PATCH,flightData:n,previousTree:e,overrideCanonicalUrl:a,cache:createEmptyCacheNode(),mutable:{globalMutable:j}})})},[z]),Z=(0,s.useCallback)((e,n,a,i)=>{let s=new URL((0,b.addBasePath)(e),location.href);return j.pendingNavigatePath=(0,p.createHrefFromUrl)(s),z({type:f.ACTION_NAVIGATE,url:s,isExternalUrl:isExternalURL(s),locationSearch:location.search,forceOptimisticNavigation:a,shouldScroll:null==i||i,navigateType:n,cache:createEmptyCacheNode(),mutable:{globalMutable:j}})},[z]);!function(e){let n=(0,s.useCallback)(n=>{(0,s.startTransition)(()=>{e({...n,type:f.ACTION_SERVER_ACTION,mutable:{globalMutable:j},cache:createEmptyCacheNode()})})},[e]);O=n}(z);let J=(0,s.useMemo)(()=>{let e={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,n)=>{if((0,y.isBot)(window.navigator.userAgent))return;let a=new URL((0,b.addBasePath)(e),location.href);isExternalURL(a)||(0,s.startTransition)(()=>{var e;z({type:f.ACTION_PREFETCH,url:a,kind:null!=(e=null==n?void 0:n.kind)?e:f.PrefetchKind.FULL})})},replace:(e,n)=>{void 0===n&&(n={}),(0,s.startTransition)(()=>{var a;Z(e,"replace",!!n.forceOptimisticNavigation,null==(a=n.scroll)||a)})},push:(e,n)=>{void 0===n&&(n={}),(0,s.startTransition)(()=>{var a;Z(e,"push",!!n.forceOptimisticNavigation,null==(a=n.scroll)||a)})},refresh:()=>{(0,s.startTransition)(()=>{z({type:f.ACTION_REFRESH,cache:createEmptyCacheNode(),mutable:{globalMutable:j},origin:window.location.origin})})},fastRefresh:()=>{throw Error("fastRefresh can only be used in development mode. Please use refresh instead.")}};return e},[z,Z]);if((0,s.useEffect)(()=>{window.next&&(window.next.router=J)},[J]),(0,s.useEffect)(()=>{j.refresh=J.refresh},[J.refresh]),(0,s.useEffect)(()=>{function handlePageShow(e){var n;e.persisted&&(null==(n=window.history.state)?void 0:n.tree)&&z({type:f.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.tree})}return window.addEventListener("pageshow",handlePageShow),()=>{window.removeEventListener("pageshow",handlePageShow)}},[z]),F.mpaNavigation){if(j.pendingMpaPath!==H){let e=window.location;F.pendingPush?e.assign(H):e.replace(H),j.pendingMpaPath=H}(0,s.use)((0,E.createInfinitePromise)())}let ee=(0,s.useCallback)(e=>{let{state:n}=e;if(n){if(!n.__NA){window.location.reload();return}(0,s.startTransition)(()=>{z({type:f.ACTION_RESTORE,url:new URL(window.location.href),tree:n.tree})})}},[z]);(0,s.useEffect)(()=>(window.addEventListener("popstate",ee),()=>{window.removeEventListener("popstate",ee)}),[ee]);let et=(0,s.useMemo)(()=>(0,x.findHeadInCache)(k,D[1]),[k,D]),er=s.default.createElement(w.RedirectBoundary,null,et,k.subTreeData,s.default.createElement(_.AppRouterAnnouncer,{tree:D}));return s.default.createElement(s.default.Fragment,null,s.default.createElement(HistoryUpdater,{tree:D,pushRef:F,canonicalUrl:H,sync:K}),s.default.createElement(h.PathnameContext.Provider,{value:Y},s.default.createElement(h.SearchParamsContext.Provider,{value:$},s.default.createElement(u.GlobalLayoutRouterContext.Provider,{value:{buildId:n,changeByServerResponse:Q,tree:D,focusAndScrollRef:W,nextUrl:V}},s.default.createElement(u.AppRouterContext.Provider,{value:J},s.default.createElement(u.LayoutRouterContext.Provider,{value:{childNodes:k.parallelRoutes,tree:D,url:H}},er))))))}function AppRouter(e){let{globalErrorComponent:n,...a}=e;return s.default.createElement(g.ErrorBoundary,{errorComponent:n},s.default.createElement(Router,a))}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4954:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"bailoutToClientRendering",{enumerable:!0,get:function(){return bailoutToClientRendering}});let i=a(6800),s=a(4749);function bailoutToClientRendering(){let e=s.staticGenerationAsyncStorage.getStore();return null!=e&&!!e.forceStatic||((null==e?void 0:e.isStaticGeneration)&&(0,i.suspense)(),!1)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},3402:(e,n,a)=>{"use strict";function clientHookInServerComponentError(e){}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"clientHookInServerComponentError",{enumerable:!0,get:function(){return clientHookInServerComponentError}}),a(2147),a(9885),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},5365:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{ErrorBoundaryHandler:function(){return ErrorBoundaryHandler},GlobalError:function(){return GlobalError},default:function(){return f},ErrorBoundary:function(){return ErrorBoundary}});let i=a(2147),s=i._(a(9885)),u=a(4979),d={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};let ErrorBoundaryHandler=class ErrorBoundaryHandler extends s.default.Component{static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,n){return e.pathname!==n.previousPathname&&n.error?{error:null,previousPathname:e.pathname}:{error:n.error,previousPathname:e.pathname}}render(){return this.state.error?s.default.createElement(s.default.Fragment,null,this.props.errorStyles,s.default.createElement(this.props.errorComponent,{error:this.state.error,reset:this.reset})):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}};function GlobalError(e){let{error:n}=e,a=null==n?void 0:n.digest;return s.default.createElement("html",{id:"__next_error__"},s.default.createElement("head",null),s.default.createElement("body",null,s.default.createElement("div",{style:d.error},s.default.createElement("div",null,s.default.createElement("h2",{style:d.text},"Application error: a "+(a?"server":"client")+"-side exception has occurred (see the "+(a?"server logs":"browser console")+" for more information)."),a?s.default.createElement("p",{style:d.text},"Digest: "+a):null))))}let f=GlobalError;function ErrorBoundary(e){let{errorComponent:n,errorStyles:a,children:i}=e,d=(0,u.usePathname)();return n?s.default.createElement(ErrorBoundaryHandler,{pathname:d,errorComponent:n,errorStyles:a},i):s.default.createElement(s.default.Fragment,null,i)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},5171:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{DYNAMIC_ERROR_CODE:function(){return a},DynamicServerError:function(){return DynamicServerError}});let a="DYNAMIC_SERVER_USAGE";let DynamicServerError=class DynamicServerError extends Error{constructor(e){super("Dynamic server usage: "+e),this.digest=a}};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},9880:(e,n)=>{"use strict";let a;function createInfinitePromise(){return a||(a=new Promise(()=>{})),a}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"createInfinitePromise",{enumerable:!0,get:function(){return createInfinitePromise}}),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4900:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return OuterLayoutRouter}}),a(2147);let i=a(7795),s=i._(a(9885));a(8908);let u=a(2428),d=a(9102),f=a(9880),p=a(5365),h=a(4538),m=a(4448),g=a(7502),v=a(4714),y=a(1275),b=a(4701),_=a(8026),w=["bottom","height","left","right","top","width","x","y"];function topOfElementInViewport(e,n){let a=e.getBoundingClientRect();return a.top>=0&&a.top<=n}let InnerScrollAndFocusHandler=class InnerScrollAndFocusHandler extends s.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:n}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>n.every((n,a)=>(0,h.matchSegment)(n,e[a]))))return;let a=null,i=e.hashFragment;if(i&&(a=function(e){var n;return"top"===e?document.body:null!=(n=document.getElementById(e))?n:document.getElementsByName(e)[0]}(i)),!a&&(a=null),!(a instanceof Element))return;for(;!(a instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let n=e.getBoundingClientRect();return w.every(e=>0===n[e])}(a);){if(null===a.nextElementSibling)return;a=a.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,m.handleSmoothScroll)(()=>{if(i){a.scrollIntoView();return}let e=document.documentElement,n=e.clientHeight;!topOfElementInViewport(a,n)&&(e.scrollTop=0,topOfElementInViewport(a,n)||a.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,a.focus()}}}};function ScrollAndFocusHandler(e){let{segmentPath:n,children:a}=e,i=(0,s.useContext)(u.GlobalLayoutRouterContext);if(!i)throw Error("invariant global layout router not mounted");return s.default.createElement(InnerScrollAndFocusHandler,{segmentPath:n,focusAndScrollRef:i.focusAndScrollRef},a)}function InnerLayoutRouter(e){let{parallelRouterKey:n,url:a,childNodes:i,childProp:p,segmentPath:m,tree:g,cacheKey:v}=e,y=(0,s.useContext)(u.GlobalLayoutRouterContext);if(!y)throw Error("invariant global layout router not mounted");let{buildId:b,changeByServerResponse:w,tree:x}=y,E=i.get(v);if(p&&null!==p.current&&(E?E.status===u.CacheStates.LAZY_INITIALIZED&&(E.status=u.CacheStates.READY,E.subTreeData=p.current):(E={status:u.CacheStates.READY,data:null,subTreeData:p.current,parallelRoutes:new Map},i.set(v,E))),!E||E.status===u.CacheStates.LAZY_INITIALIZED){let e=function walkAddRefetch(e,n){if(e){let[a,i]=e,s=2===e.length;if((0,h.matchSegment)(n[0],a)&&n[1].hasOwnProperty(i)){if(s){let e=walkAddRefetch(void 0,n[1][i]);return[n[0],{...n[1],[i]:[e[0],e[1],e[2],"refetch"]}]}return[n[0],{...n[1],[i]:walkAddRefetch(e.slice(2),n[1][i])}]}}return n}(["",...m],x);E={status:u.CacheStates.DATA_FETCH,data:(0,_.createRecordFromThenable)((0,d.fetchServerResponse)(new URL(a,location.origin),e,y.nextUrl,b)),subTreeData:null,head:E&&E.status===u.CacheStates.LAZY_INITIALIZED?E.head:void 0,parallelRoutes:E&&E.status===u.CacheStates.LAZY_INITIALIZED?E.parallelRoutes:new Map},i.set(v,E)}if(!E)throw Error("Child node should always exist");if(E.subTreeData&&E.data)throw Error("Child node should not have both subTreeData and data");if(E.data){let[e,n]=(0,s.use)(E.data);E.data=null,setTimeout(()=>{(0,s.startTransition)(()=>{w(x,e,n)})}),(0,s.use)((0,f.createInfinitePromise)())}E.subTreeData||(0,s.use)((0,f.createInfinitePromise)());let S=s.default.createElement(u.LayoutRouterContext.Provider,{value:{tree:g[1][n],childNodes:E.parallelRoutes,url:a}},E.subTreeData);return S}function LoadingBoundary(e){let{children:n,loading:a,loadingStyles:i,hasLoading:u}=e;return u?s.default.createElement(s.Suspense,{fallback:s.default.createElement(s.default.Fragment,null,i,a)},n):s.default.createElement(s.default.Fragment,null,n)}function OuterLayoutRouter(e){let{parallelRouterKey:n,segmentPath:a,childProp:i,error:d,errorStyles:f,templateStyles:m,loading:_,loadingStyles:w,hasLoading:x,template:E,notFound:S,notFoundStyles:P,styles:R}=e,C=(0,s.useContext)(u.LayoutRouterContext);if(!C)throw Error("invariant expected layout router to be mounted");let{childNodes:O,tree:j,url:M}=C,N=O.get(n);N||(N=new Map,O.set(n,N));let D=j[1][n][0],k=i.segment,L=(0,y.getSegmentValue)(D),F=[D];return s.default.createElement(s.default.Fragment,null,R,F.map(e=>{let R=(0,h.matchSegment)(e,k),C=(0,y.getSegmentValue)(e),O=(0,b.createRouterCacheKey)(e);return s.default.createElement(u.TemplateContext.Provider,{key:(0,b.createRouterCacheKey)(e,!0),value:s.default.createElement(ScrollAndFocusHandler,{segmentPath:a},s.default.createElement(p.ErrorBoundary,{errorComponent:d,errorStyles:f},s.default.createElement(LoadingBoundary,{hasLoading:x,loading:_,loadingStyles:w},s.default.createElement(v.NotFoundBoundary,{notFound:S,notFoundStyles:P},s.default.createElement(g.RedirectBoundary,null,s.default.createElement(InnerLayoutRouter,{parallelRouterKey:n,url:M,tree:j,childNodes:N,childProp:R?i:null,segmentPath:a,cacheKey:O,isActive:L===C}))))))},m,E)}))}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4538:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{matchSegment:function(){return matchSegment},canSegmentBeOverridden:function(){return canSegmentBeOverridden}});let i=a(2290),matchSegment=(e,n)=>"string"==typeof e?"string"==typeof n&&e===n:"string"!=typeof n&&e[0]===n[0]&&e[1]===n[1],canSegmentBeOverridden=(e,n)=>{var a;return!Array.isArray(e)&&!!Array.isArray(n)&&(null==(a=(0,i.getSegmentParam)(e))?void 0:a.param)===n[0]};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4979:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{ReadonlyURLSearchParams:function(){return ReadonlyURLSearchParams},useSearchParams:function(){return useSearchParams},usePathname:function(){return usePathname},ServerInsertedHTMLContext:function(){return p.ServerInsertedHTMLContext},useServerInsertedHTML:function(){return p.useServerInsertedHTML},useRouter:function(){return useRouter},useParams:function(){return useParams},useSelectedLayoutSegments:function(){return useSelectedLayoutSegments},useSelectedLayoutSegment:function(){return useSelectedLayoutSegment},redirect:function(){return h.redirect},permanentRedirect:function(){return h.permanentRedirect},RedirectType:function(){return h.RedirectType},notFound:function(){return m.notFound}});let i=a(9885),s=a(2428),u=a(1736),d=a(3402),f=a(1275),p=a(5753),h=a(1612),m=a(1103),g=Symbol("internal for urlsearchparams readonly");function readonlyURLSearchParamsError(){return Error("ReadonlyURLSearchParams cannot be modified")}let ReadonlyURLSearchParams=class ReadonlyURLSearchParams{[Symbol.iterator](){return this[g][Symbol.iterator]()}append(){throw readonlyURLSearchParamsError()}delete(){throw readonlyURLSearchParamsError()}set(){throw readonlyURLSearchParamsError()}sort(){throw readonlyURLSearchParamsError()}constructor(e){this[g]=e,this.entries=e.entries.bind(e),this.forEach=e.forEach.bind(e),this.get=e.get.bind(e),this.getAll=e.getAll.bind(e),this.has=e.has.bind(e),this.keys=e.keys.bind(e),this.values=e.values.bind(e),this.toString=e.toString.bind(e),this.size=e.size}};function useSearchParams(){(0,d.clientHookInServerComponentError)("useSearchParams");let e=(0,i.useContext)(u.SearchParamsContext),n=(0,i.useMemo)(()=>e?new ReadonlyURLSearchParams(e):null,[e]);{let{bailoutToClientRendering:e}=a(4954);e()}return n}function usePathname(){return(0,d.clientHookInServerComponentError)("usePathname"),(0,i.useContext)(u.PathnameContext)}function useRouter(){(0,d.clientHookInServerComponentError)("useRouter");let e=(0,i.useContext)(s.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function useParams(){(0,d.clientHookInServerComponentError)("useParams");let e=(0,i.useContext)(s.GlobalLayoutRouterContext),n=(0,i.useContext)(u.PathParamsContext);return(0,i.useMemo)(()=>(null==e?void 0:e.tree)?function getSelectedParams(e,n){void 0===n&&(n={});let a=e[1];for(let e of Object.values(a)){let a=e[0],i=Array.isArray(a),s=i?a[1]:a;if(!s||s.startsWith("__PAGE__"))continue;let u=i&&("c"===a[2]||"oc"===a[2]);u?n[a[0]]=a[1].split("/"):i&&(n[a[0]]=a[1]),n=getSelectedParams(e,n)}return n}(e.tree):n,[null==e?void 0:e.tree,n])}function useSelectedLayoutSegments(e){void 0===e&&(e="children"),(0,d.clientHookInServerComponentError)("useSelectedLayoutSegments");let{tree:n}=(0,i.useContext)(s.LayoutRouterContext);return function getSelectedLayoutSegmentPath(e,n,a,i){let s;if(void 0===a&&(a=!0),void 0===i&&(i=[]),a)s=e[1][n];else{var u;let n=e[1];s=null!=(u=n.children)?u:Object.values(n)[0]}if(!s)return i;let d=s[0],p=(0,f.getSegmentValue)(d);return!p||p.startsWith("__PAGE__")?i:(i.push(p),getSelectedLayoutSegmentPath(s,n,!1,i))}(n,e)}function useSelectedLayoutSegment(e){void 0===e&&(e="children"),(0,d.clientHookInServerComponentError)("useSelectedLayoutSegment");let n=useSelectedLayoutSegments(e);return 0===n.length?null:n[0]}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4714:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"NotFoundBoundary",{enumerable:!0,get:function(){return NotFoundBoundary}});let i=a(2147),s=i._(a(9885)),u=a(4979);let NotFoundErrorBoundary=class NotFoundErrorBoundary extends s.default.Component{static getDerivedStateFromError(e){if((null==e?void 0:e.digest)==="NEXT_NOT_FOUND")return{notFoundTriggered:!0};throw e}static getDerivedStateFromProps(e,n){return e.pathname!==n.previousPathname&&n.notFoundTriggered?{notFoundTriggered:!1,previousPathname:e.pathname}:{notFoundTriggered:n.notFoundTriggered,previousPathname:e.pathname}}render(){return this.state.notFoundTriggered?s.default.createElement(s.default.Fragment,null,s.default.createElement("meta",{name:"robots",content:"noindex"}),!1,this.props.notFoundStyles,this.props.notFound):this.props.children}constructor(e){super(e),this.state={notFoundTriggered:!!e.asNotFound,previousPathname:e.pathname}}};function NotFoundBoundary(e){let{notFound:n,notFoundStyles:a,asNotFound:i,children:d}=e,f=(0,u.usePathname)();return n?s.default.createElement(NotFoundErrorBoundary,{pathname:f,notFound:n,notFoundStyles:a,asNotFound:i},d):s.default.createElement(s.default.Fragment,null,d)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},1103:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{notFound:function(){return notFound},isNotFoundError:function(){return isNotFoundError}});let a="NEXT_NOT_FOUND";function notFound(){let e=Error(a);throw e.digest=a,e}function isNotFoundError(e){return(null==e?void 0:e.digest)===a}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},8862:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"PromiseQueue",{enumerable:!0,get:function(){return PromiseQueue}});let i=a(8324),s=a(4567);var u=s._("_maxConcurrency"),d=s._("_runningCount"),f=s._("_queue"),p=s._("_processNext");let PromiseQueue=class PromiseQueue{enqueue(e){let n,a;let s=new Promise((e,i)=>{n=e,a=i}),task=async()=>{try{i._(this,d)[d]++;let a=await e();n(a)}catch(e){a(e)}finally{i._(this,d)[d]--,i._(this,p)[p]()}};return i._(this,f)[f].push({promiseFn:s,task}),i._(this,p)[p](),s}bump(e){let n=i._(this,f)[f].findIndex(n=>n.promiseFn===e);if(n>-1){let e=i._(this,f)[f].splice(n,1)[0];i._(this,f)[f].unshift(e),i._(this,p)[p](!0)}}constructor(e=5){Object.defineProperty(this,p,{value:processNext}),Object.defineProperty(this,u,{writable:!0,value:void 0}),Object.defineProperty(this,d,{writable:!0,value:void 0}),Object.defineProperty(this,f,{writable:!0,value:void 0}),i._(this,u)[u]=e,i._(this,d)[d]=0,i._(this,f)[f]=[]}};function processNext(e){if(void 0===e&&(e=!1),(i._(this,d)[d]<i._(this,u)[u]||e)&&i._(this,f)[f].length>0){var n;null==(n=i._(this,f)[f].shift())||n.task()}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},7502:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{RedirectErrorBoundary:function(){return RedirectErrorBoundary},RedirectBoundary:function(){return RedirectBoundary}});let i=a(7795),s=i._(a(9885)),u=a(4979),d=a(1612);function HandleRedirect(e){let{redirect:n,reset:a,redirectType:i}=e,f=(0,u.useRouter)();return(0,s.useEffect)(()=>{s.default.startTransition(()=>{i===d.RedirectType.push?f.push(n,{}):f.replace(n,{}),a()})},[n,i,a,f]),null}let RedirectErrorBoundary=class RedirectErrorBoundary extends s.default.Component{static getDerivedStateFromError(e){if((0,d.isRedirectError)(e)){let n=(0,d.getURLFromRedirectError)(e),a=(0,d.getRedirectTypeFromError)(e);return{redirect:n,redirectType:a}}throw e}render(){let{redirect:e,redirectType:n}=this.state;return null!==e&&null!==n?s.default.createElement(HandleRedirect,{redirect:e,redirectType:n,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(e){super(e),this.state={redirect:null,redirectType:null}}};function RedirectBoundary(e){let{children:n}=e,a=(0,u.useRouter)();return s.default.createElement(RedirectErrorBoundary,{router:a},n)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},1612:(e,n,a)=>{"use strict";var i;Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{RedirectType:function(){return i},getRedirectError:function(){return getRedirectError},redirect:function(){return redirect},permanentRedirect:function(){return permanentRedirect},isRedirectError:function(){return isRedirectError},getURLFromRedirectError:function(){return getURLFromRedirectError},getRedirectTypeFromError:function(){return getRedirectTypeFromError}});let s=a(5403),u="NEXT_REDIRECT";function getRedirectError(e,n,a){void 0===a&&(a=!1);let i=Error(u);i.digest=u+";"+n+";"+e+";"+a;let d=s.requestAsyncStorage.getStore();return d&&(i.mutableCookies=d.mutableCookies),i}function redirect(e,n){throw void 0===n&&(n="replace"),getRedirectError(e,n,!1)}function permanentRedirect(e,n){throw void 0===n&&(n="replace"),getRedirectError(e,n,!0)}function isRedirectError(e){if("string"!=typeof(null==e?void 0:e.digest))return!1;let[n,a,i,s]=e.digest.split(";",4);return n===u&&("replace"===a||"push"===a)&&"string"==typeof i&&("true"===s||"false"===s)}function getURLFromRedirectError(e){return isRedirectError(e)?e.digest.split(";",3)[2]:null}function getRedirectTypeFromError(e){if(!isRedirectError(e))throw Error("Not a redirect error");return e.digest.split(";",3)[1]}(function(e){e.push="push",e.replace="replace"})(i||(i={})),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},5392:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return RenderFromTemplateContext}});let i=a(7795),s=i._(a(9885)),u=a(2428);function RenderFromTemplateContext(){let e=(0,s.useContext)(u.TemplateContext);return s.default.createElement(s.default.Fragment,null,e)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},1847:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"applyFlightData",{enumerable:!0,get:function(){return applyFlightData}});let i=a(2428),s=a(5929),u=a(4059);function applyFlightData(e,n,a,d){void 0===d&&(d=!1);let[f,p,h]=a.slice(-3);return null!==p&&(3===a.length?(n.status=i.CacheStates.READY,n.subTreeData=p,(0,s.fillLazyItemsTillLeafWithHead)(n,e,f,h,d)):(n.status=i.CacheStates.READY,n.subTreeData=e.subTreeData,n.parallelRoutes=new Map(e.parallelRoutes),(0,u.fillCacheWithNewSubTreeData)(n,e,a,d)),!0)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},9605:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function applyRouterStatePatchToTree(e,n,a){let s;let[u,d,,,f]=n;if(1===e.length){let e=applyPatch(n,a);return e}let[p,h]=e;if(!(0,i.matchSegment)(p,u))return null;let m=2===e.length;if(m)s=applyPatch(d[h],a);else if(null===(s=applyRouterStatePatchToTree(e.slice(2),d[h],a)))return null;let g=[e[0],{...d,[h]:s}];return f&&(g[4]=!0),g}}});let i=a(4538);function applyPatch(e,n){let[a,s]=e,[u,d]=n;if("__DEFAULT__"===u&&"__DEFAULT__"!==a)return e;if((0,i.matchSegment)(a,u)){let n={};for(let e in s){let a=void 0!==d[e];a?n[e]=applyPatch(s[e],d[e]):n[e]=s[e]}for(let e in d)n[e]||(n[e]=d[e]);let i=[a,n];return e[2]&&(i[2]=e[2]),e[3]&&(i[3]=e[3]),e[4]&&(i[4]=e[4]),i}return n}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},6663:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{extractPathFromFlightRouterState:function(){return extractPathFromFlightRouterState},computeChangedPath:function(){return computeChangedPath}});let i=a(4265),s=a(392),u=a(4538),removeLeadingSlash=e=>"/"===e[0]?e.slice(1):e,segmentToPathname=e=>"string"==typeof e?e:e[1];function normalizeSegments(e){return e.reduce((e,n)=>""===(n=removeLeadingSlash(n))||(0,s.isGroupSegment)(n)?e:e+"/"+n,"")||"/"}function extractPathFromFlightRouterState(e){var n;let a=Array.isArray(e[0])?e[0][1]:e[0];if("__DEFAULT__"===a||i.INTERCEPTION_ROUTE_MARKERS.some(e=>a.startsWith(e)))return;if(a.startsWith("__PAGE__"))return"";let s=[a],u=null!=(n=e[1])?n:{},d=u.children?extractPathFromFlightRouterState(u.children):void 0;if(void 0!==d)s.push(d);else for(let[e,n]of Object.entries(u)){if("children"===e)continue;let a=extractPathFromFlightRouterState(n);void 0!==a&&s.push(a)}return normalizeSegments(s)}function computeChangedPath(e,n){let a=function computeChangedPathImpl(e,n){let[a,s]=e,[d,f]=n,p=segmentToPathname(a),h=segmentToPathname(d);if(i.INTERCEPTION_ROUTE_MARKERS.some(e=>p.startsWith(e)||h.startsWith(e)))return"";if(!(0,u.matchSegment)(a,d)){var m;return null!=(m=extractPathFromFlightRouterState(n))?m:""}for(let e in s)if(f[e]){let n=computeChangedPathImpl(s[e],f[e]);if(null!==n)return segmentToPathname(d)+"/"+n}return null}(e,n);return null==a||"/"===a?a:normalizeSegments(a.split("/"))}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},1706:(e,n)=>{"use strict";function createHrefFromUrl(e,n){return void 0===n&&(n=!0),e.pathname+e.search+(n?e.hash:"")}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"createHrefFromUrl",{enumerable:!0,get:function(){return createHrefFromUrl}}),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},9624:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"createInitialRouterState",{enumerable:!0,get:function(){return createInitialRouterState}});let i=a(2428),s=a(1706),u=a(5929),d=a(6663);function createInitialRouterState(e){var n;let{buildId:a,initialTree:f,children:p,initialCanonicalUrl:h,initialParallelRoutes:m,isServer:g,location:v,initialHead:y}=e,b={status:i.CacheStates.READY,data:null,subTreeData:p,parallelRoutes:g?new Map:m};return(null===m||0===m.size)&&(0,u.fillLazyItemsTillLeafWithHead)(b,void 0,f,y),{buildId:a,tree:f,cache:b,prefetchCache:new Map,pushRef:{pendingPush:!1,mpaNavigation:!1},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:v?(0,s.createHrefFromUrl)(v):h,nextUrl:null!=(n=(0,d.extractPathFromFlightRouterState)(f)||(null==v?void 0:v.pathname))?n:null}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},8775:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"createOptimisticTree",{enumerable:!0,get:function(){return function createOptimisticTree(e,n,a){let s;let[u,d,f,p,h]=n||[null,{}],m=e[0],g=1===e.length,v=null!==u&&(0,i.matchSegment)(u,m),y=Object.keys(d).length>1,b=!n||!v||y,_={};if(null!==u&&v&&(_=d),!g&&!y){let n=createOptimisticTree(e.slice(1),_?_.children:null,a||b);s=n}let w=[m,{..._,...s?{children:s}:{}}];return f&&(w[2]=f),!a&&b?w[3]="refetch":v&&p&&(w[3]=p),v&&h&&(w[4]=h),w}}});let i=a(4538);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},8026:(e,n)=>{"use strict";function createRecordFromThenable(e){return e.status="pending",e.then(n=>{"pending"===e.status&&(e.status="fulfilled",e.value=n)},n=>{"pending"===e.status&&(e.status="rejected",e.reason=n)}),e}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"createRecordFromThenable",{enumerable:!0,get:function(){return createRecordFromThenable}}),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4701:(e,n)=>{"use strict";function createRouterCacheKey(e,n){return void 0===n&&(n=!1),Array.isArray(e)?(e[0]+"|"+e[1]+"|"+e[2]).toLowerCase():n&&e.startsWith("__PAGE__")?"__PAGE__":e}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"createRouterCacheKey",{enumerable:!0,get:function(){return createRouterCacheKey}}),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},9102:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"fetchServerResponse",{enumerable:!0,get:function(){return fetchServerResponse}});let i=a(4361),s=a(3724),u=a(5422),d=a(3678),f=a(755),{createFromFetch:p}=a(2623);function doMpaNavigation(e){return[(0,s.urlToUrlWithoutFlightMarker)(e).toString(),void 0]}async function fetchServerResponse(e,n,a,h,m){let g={[i.RSC]:"1",[i.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(n))};m===d.PrefetchKind.AUTO&&(g[i.NEXT_ROUTER_PREFETCH]="1"),a&&(g[i.NEXT_URL]=a);let v=(0,f.hexHash)([g[i.NEXT_ROUTER_PREFETCH]||"0",g[i.NEXT_ROUTER_STATE_TREE],g[i.NEXT_URL]].join(","));try{let n=new URL(e);n.searchParams.set(i.NEXT_RSC_UNION_QUERY,v);let a=await fetch(n,{credentials:"same-origin",headers:g}),d=(0,s.urlToUrlWithoutFlightMarker)(a.url),f=a.redirected?d:void 0,m=a.headers.get("content-type")||"";if(m!==i.RSC_CONTENT_TYPE_HEADER||!a.ok)return e.hash&&(d.hash=e.hash),doMpaNavigation(d.toString());let[y,b]=await p(Promise.resolve(a),{callServer:u.callServer});if(h!==y)return doMpaNavigation(a.url);return[b,f]}catch(n){return console.error("Failed to fetch RSC payload for "+e+". Falling back to browser navigation.",n),[e.toString(),void 0]}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},1924:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"fillCacheWithDataProperty",{enumerable:!0,get:function(){return function fillCacheWithDataProperty(e,n,a,u,d){void 0===d&&(d=!1);let f=a.length<=2,[p,h]=a,m=(0,s.createRouterCacheKey)(h),g=n.parallelRoutes.get(p);if(!g||d&&n.parallelRoutes.size>1)return{bailOptimistic:!0};let v=e.parallelRoutes.get(p);v&&v!==g||(v=new Map(g),e.parallelRoutes.set(p,v));let y=g.get(m),b=v.get(m);if(f){b&&b.data&&b!==y||v.set(m,{status:i.CacheStates.DATA_FETCH,data:u(),subTreeData:null,parallelRoutes:new Map});return}if(!b||!y){b||v.set(m,{status:i.CacheStates.DATA_FETCH,data:u(),subTreeData:null,parallelRoutes:new Map});return}return b===y&&(b={status:b.status,data:b.data,subTreeData:b.subTreeData,parallelRoutes:new Map(b.parallelRoutes)},v.set(m,b)),fillCacheWithDataProperty(b,y,a.slice(2),u)}}});let i=a(2428),s=a(4701);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4059:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"fillCacheWithNewSubTreeData",{enumerable:!0,get:function(){return function fillCacheWithNewSubTreeData(e,n,a,f){let p=a.length<=5,[h,m]=a,g=(0,d.createRouterCacheKey)(m),v=n.parallelRoutes.get(h);if(!v)return;let y=e.parallelRoutes.get(h);y&&y!==v||(y=new Map(v),e.parallelRoutes.set(h,y));let b=v.get(g),_=y.get(g);if(p){_&&_.data&&_!==b||(_={status:i.CacheStates.READY,data:null,subTreeData:a[3],parallelRoutes:b?new Map(b.parallelRoutes):new Map},b&&(0,s.invalidateCacheByRouterState)(_,b,a[2]),(0,u.fillLazyItemsTillLeafWithHead)(_,b,a[2],a[4],f),y.set(g,_));return}_&&b&&(_===b&&(_={status:_.status,data:_.data,subTreeData:_.subTreeData,parallelRoutes:new Map(_.parallelRoutes)},y.set(g,_)),fillCacheWithNewSubTreeData(_,b,a.slice(2),f))}}});let i=a(2428),s=a(2582),u=a(5929),d=a(4701);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},5929:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function fillLazyItemsTillLeafWithHead(e,n,a,u,d){let f=0===Object.keys(a[1]).length;if(f){e.head=u;return}for(let f in a[1]){let p=a[1][f],h=p[0],m=(0,s.createRouterCacheKey)(h);if(n){let a=n.parallelRoutes.get(f);if(a){let n=new Map(a),s=n.get(m),h=d&&s?{status:s.status,data:s.data,subTreeData:s.subTreeData,parallelRoutes:new Map(s.parallelRoutes)}:{status:i.CacheStates.LAZY_INITIALIZED,data:null,subTreeData:null,parallelRoutes:new Map(null==s?void 0:s.parallelRoutes)};n.set(m,h),fillLazyItemsTillLeafWithHead(h,s,p,u,d),e.parallelRoutes.set(f,n);continue}}let g={status:i.CacheStates.LAZY_INITIALIZED,data:null,subTreeData:null,parallelRoutes:new Map},v=e.parallelRoutes.get(f);v?v.set(m,g):e.parallelRoutes.set(f,new Map([[m,g]])),fillLazyItemsTillLeafWithHead(g,void 0,p,u,d)}}}});let i=a(2428),s=a(4701);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},6699:(e,n)=>{"use strict";var a;function getPrefetchEntryCacheStatus(e){let{kind:n,prefetchTime:a,lastUsedTime:i}=e;return Date.now()<(null!=i?i:a)+3e4?i?"reusable":"fresh":"auto"===n&&Date.now()<a+3e5?"stale":"full"===n&&Date.now()<a+3e5?"reusable":"expired"}Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{PrefetchCacheEntryStatus:function(){return a},getPrefetchEntryCacheStatus:function(){return getPrefetchEntryCacheStatus}}),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(a||(a={})),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},3466:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"handleMutable",{enumerable:!0,get:function(){return handleMutable}});let i=a(6663);function handleMutable(e,n){var a,s,u,d;let f=null==(s=n.shouldScroll)||s;return{buildId:e.buildId,canonicalUrl:null!=n.canonicalUrl?n.canonicalUrl===e.canonicalUrl?e.canonicalUrl:n.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:null!=n.pendingPush?n.pendingPush:e.pushRef.pendingPush,mpaNavigation:null!=n.mpaNavigation?n.mpaNavigation:e.pushRef.mpaNavigation},focusAndScrollRef:{apply:!!f&&((null==n?void 0:n.scrollableSegments)!==void 0||e.focusAndScrollRef.apply),onlyHashChange:!!n.hashFragment&&e.canonicalUrl.split("#")[0]===(null==(a=n.canonicalUrl)?void 0:a.split("#")[0]),hashFragment:f?n.hashFragment&&""!==n.hashFragment?decodeURIComponent(n.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:f?null!=(u=null==n?void 0:n.scrollableSegments)?u:e.focusAndScrollRef.segmentPaths:[]},cache:n.cache?n.cache:e.cache,prefetchCache:n.prefetchCache?n.prefetchCache:e.prefetchCache,tree:void 0!==n.patchedTree?n.patchedTree:e.tree,nextUrl:void 0!==n.patchedTree?null!=(d=(0,i.computeChangedPath)(e.tree,n.patchedTree))?d:e.canonicalUrl:e.nextUrl}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},1986:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function invalidateCacheBelowFlightSegmentPath(e,n,a){let s=a.length<=2,[u,d]=a,f=(0,i.createRouterCacheKey)(d),p=n.parallelRoutes.get(u);if(!p)return;let h=e.parallelRoutes.get(u);if(h&&h!==p||(h=new Map(p),e.parallelRoutes.set(u,h)),s){h.delete(f);return}let m=p.get(f),g=h.get(f);g&&m&&(g===m&&(g={status:g.status,data:g.data,subTreeData:g.subTreeData,parallelRoutes:new Map(g.parallelRoutes)},h.set(f,g)),invalidateCacheBelowFlightSegmentPath(g,m,a.slice(2)))}}});let i=a(4701);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},2582:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return invalidateCacheByRouterState}});let i=a(4701);function invalidateCacheByRouterState(e,n,a){for(let s in a[1]){let u=a[1][s][0],d=(0,i.createRouterCacheKey)(u),f=n.parallelRoutes.get(s);if(f){let n=new Map(f);n.delete(d),e.parallelRoutes.set(s,n)}}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},145:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function isNavigatingToNewRootLayout(e,n){let a=e[0],i=n[0];if(Array.isArray(a)&&Array.isArray(i)){if(a[0]!==i[0]||a[2]!==i[2])return!0}else if(a!==i)return!0;if(e[4])return!n[4];if(n[4])return!0;let s=Object.values(e[1])[0],u=Object.values(n[1])[0];return!s||!u||isNavigatingToNewRootLayout(s,u)}}}),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4879:(e,n)=>{"use strict";function readRecordValue(e){if("fulfilled"===e.status)return e.value;throw e}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"readRecordValue",{enumerable:!0,get:function(){return readRecordValue}}),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},2755:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"fastRefreshReducer",{enumerable:!0,get:function(){return fastRefreshReducer}}),a(9102),a(8026),a(4879),a(1706),a(9605),a(145),a(8237),a(3466),a(1847);let fastRefreshReducer=function(e,n){return e};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},2226:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"findHeadInCache",{enumerable:!0,get:function(){return function findHeadInCache(e,n){let a=0===Object.keys(n).length;if(a)return e.head;for(let a in n){let[s,u]=n[a],d=e.parallelRoutes.get(a);if(!d)continue;let f=(0,i.createRouterCacheKey)(s),p=d.get(f);if(!p)continue;let h=findHeadInCache(p,u);if(h)return h}}}});let i=a(4701);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},1275:(e,n)=>{"use strict";function getSegmentValue(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"getSegmentValue",{enumerable:!0,get:function(){return getSegmentValue}}),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},8237:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{handleExternalUrl:function(){return handleExternalUrl},navigateReducer:function(){return navigateReducer}});let i=a(2428),s=a(9102),u=a(8026),d=a(4879),f=a(1706),p=a(1986),h=a(1924),m=a(8775),g=a(9605),v=a(4320),y=a(145),b=a(3678),_=a(3466),w=a(1847),x=a(6699),E=a(8155),S=a(1196);function handleExternalUrl(e,n,a,i){return n.previousTree=e.tree,n.mpaNavigation=!0,n.canonicalUrl=a,n.pendingPush=i,n.scrollableSegments=void 0,(0,_.handleMutable)(e,n)}function generateSegmentsFromPatch(e){let n=[],[a,i]=e;if(0===Object.keys(i).length)return[[a]];for(let[e,s]of Object.entries(i))for(let i of generateSegmentsFromPatch(s))""===a?n.push([e,...i]):n.push([a,e,...i]);return n}function navigateReducer(e,n){let{url:a,isExternalUrl:P,navigateType:R,cache:C,mutable:O,forceOptimisticNavigation:j,shouldScroll:M}=n,{pathname:N,hash:D}=a,k=(0,f.createHrefFromUrl)(a),L="push"===R;(0,E.prunePrefetchCache)(e.prefetchCache);let F=JSON.stringify(O.previousTree)===JSON.stringify(e.tree);if(F)return(0,_.handleMutable)(e,O);if(P)return handleExternalUrl(e,O,a.toString(),L);let W=e.prefetchCache.get((0,f.createHrefFromUrl)(a,!1));if(j&&(null==W?void 0:W.kind)!==b.PrefetchKind.TEMPORARY){let n=N.split("/");n.push("__PAGE__");let d=(0,m.createOptimisticTree)(n,e.tree,!1),p={...C};p.status=i.CacheStates.READY,p.subTreeData=e.cache.subTreeData,p.parallelRoutes=new Map(e.cache.parallelRoutes);let g=null,v=n.slice(1).map(e=>["children",e]).flat(),y=(0,h.fillCacheWithDataProperty)(p,e.cache,v,()=>(g||(g=(0,u.createRecordFromThenable)((0,s.fetchServerResponse)(a,d,e.nextUrl,e.buildId))),g),!0);if(!(null==y?void 0:y.bailOptimistic))return O.previousTree=e.tree,O.patchedTree=d,O.pendingPush=L,O.hashFragment=D,O.shouldScroll=M,O.scrollableSegments=[],O.cache=p,O.canonicalUrl=k,e.prefetchCache.set((0,f.createHrefFromUrl)(a,!1),{data:g?(0,u.createRecordFromThenable)(Promise.resolve(g)):null,kind:b.PrefetchKind.TEMPORARY,prefetchTime:Date.now(),treeAtTimeOfPrefetch:e.tree,lastUsedTime:Date.now()}),(0,_.handleMutable)(e,O)}if(!W){let n=(0,u.createRecordFromThenable)((0,s.fetchServerResponse)(a,e.tree,e.nextUrl,e.buildId,void 0)),i={data:(0,u.createRecordFromThenable)(Promise.resolve(n)),kind:b.PrefetchKind.TEMPORARY,prefetchTime:Date.now(),treeAtTimeOfPrefetch:e.tree,lastUsedTime:null};e.prefetchCache.set((0,f.createHrefFromUrl)(a,!1),i),W=i}let H=(0,x.getPrefetchEntryCacheStatus)(W),{treeAtTimeOfPrefetch:V,data:z}=W;S.prefetchQueue.bump(z);let[K,$]=(0,d.readRecordValue)(z);if(W.lastUsedTime||(W.lastUsedTime=Date.now()),"string"==typeof K)return handleExternalUrl(e,O,K,L);let Y=e.tree,Q=e.cache,Z=[];for(let n of K){let d=n.slice(0,-4),f=n.slice(-3)[0],m=["",...d],b=(0,g.applyRouterStatePatchToTree)(m,Y,f);if(null===b&&(b=(0,g.applyRouterStatePatchToTree)(m,V,f)),null!==b){if((0,y.isNavigatingToNewRootLayout)(Y,b))return handleExternalUrl(e,O,k,L);let g=(0,w.applyFlightData)(Q,C,n,"auto"===W.kind&&H===x.PrefetchCacheEntryStatus.reusable);g||H!==x.PrefetchCacheEntryStatus.stale||(g=function(e,n,a,s,u){let d=!1;e.status=i.CacheStates.READY,e.subTreeData=n.subTreeData,e.parallelRoutes=new Map(n.parallelRoutes);let f=generateSegmentsFromPatch(s).map(e=>[...a,...e]);for(let a of f){let i=(0,h.fillCacheWithDataProperty)(e,n,a,u);(null==i?void 0:i.bailOptimistic)||(d=!0)}return d}(C,Q,d,f,()=>(0,u.createRecordFromThenable)((0,s.fetchServerResponse)(a,Y,e.nextUrl,e.buildId))));let _=(0,v.shouldHardNavigate)(m,Y);for(let e of(_?(C.status=i.CacheStates.READY,C.subTreeData=Q.subTreeData,(0,p.invalidateCacheBelowFlightSegmentPath)(C,Q,d),O.cache=C):g&&(O.cache=C),Q=C,Y=b,generateSegmentsFromPatch(f))){let n=[...d,...e];"__DEFAULT__"!==n[n.length-1]&&Z.push(n)}}}return O.previousTree=e.tree,O.patchedTree=Y,O.canonicalUrl=$?(0,f.createHrefFromUrl)($):k,O.pendingPush=L,O.scrollableSegments=Z,O.hashFragment=D,O.shouldScroll=M,(0,_.handleMutable)(e,O)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},1196:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{prefetchQueue:function(){return m},prefetchReducer:function(){return prefetchReducer}});let i=a(1706),s=a(9102),u=a(3678),d=a(8026),f=a(8155),p=a(4361),h=a(8862),m=new h.PromiseQueue(5);function prefetchReducer(e,n){(0,f.prunePrefetchCache)(e.prefetchCache);let{url:a}=n;a.searchParams.delete(p.NEXT_RSC_UNION_QUERY);let h=(0,i.createHrefFromUrl)(a,!1),g=e.prefetchCache.get(h);if(g&&(g.kind===u.PrefetchKind.TEMPORARY&&e.prefetchCache.set(h,{...g,kind:n.kind}),!(g.kind===u.PrefetchKind.AUTO&&n.kind===u.PrefetchKind.FULL)))return e;let v=(0,d.createRecordFromThenable)(m.enqueue(()=>(0,s.fetchServerResponse)(a,e.tree,e.nextUrl,e.buildId,n.kind)));return e.prefetchCache.set(h,{treeAtTimeOfPrefetch:e.tree,data:v,kind:n.kind,prefetchTime:Date.now(),lastUsedTime:null}),e}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},8155:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"prunePrefetchCache",{enumerable:!0,get:function(){return prunePrefetchCache}});let i=a(6699);function prunePrefetchCache(e){for(let[n,a]of e)(0,i.getPrefetchEntryCacheStatus)(a)===i.PrefetchCacheEntryStatus.expired&&e.delete(n)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},8038:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"refreshReducer",{enumerable:!0,get:function(){return refreshReducer}});let i=a(9102),s=a(8026),u=a(4879),d=a(1706),f=a(9605),p=a(145),h=a(8237),m=a(3466),g=a(2428),v=a(5929);function refreshReducer(e,n){let{cache:a,mutable:y,origin:b}=n,_=e.canonicalUrl,w=e.tree,x=JSON.stringify(y.previousTree)===JSON.stringify(w);if(x)return(0,m.handleMutable)(e,y);a.data||(a.data=(0,s.createRecordFromThenable)((0,i.fetchServerResponse)(new URL(_,b),[w[0],w[1],w[2],"refetch"],e.nextUrl,e.buildId)));let[E,S]=(0,u.readRecordValue)(a.data);if("string"==typeof E)return(0,h.handleExternalUrl)(e,y,E,e.pushRef.pendingPush);for(let n of(a.data=null,E)){if(3!==n.length)return console.log("REFRESH FAILED"),e;let[i]=n,s=(0,f.applyRouterStatePatchToTree)([""],w,i);if(null===s)throw Error("SEGMENT MISMATCH");if((0,p.isNavigatingToNewRootLayout)(w,s))return(0,h.handleExternalUrl)(e,y,_,e.pushRef.pendingPush);let u=S?(0,d.createHrefFromUrl)(S):void 0;S&&(y.canonicalUrl=u);let[m,b]=n.slice(-2);null!==m&&(a.status=g.CacheStates.READY,a.subTreeData=m,(0,v.fillLazyItemsTillLeafWithHead)(a,void 0,i,b),y.cache=a,y.prefetchCache=new Map),y.previousTree=w,y.patchedTree=s,y.canonicalUrl=_,w=s}return(0,m.handleMutable)(e,y)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},2910:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"restoreReducer",{enumerable:!0,get:function(){return restoreReducer}});let i=a(1706);function restoreReducer(e,n){let{url:a,tree:s}=n,u=(0,i.createHrefFromUrl)(a);return{buildId:e.buildId,canonicalUrl:u,pushRef:e.pushRef,focusAndScrollRef:e.focusAndScrollRef,cache:e.cache,prefetchCache:e.prefetchCache,tree:s,nextUrl:a.pathname}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},9747:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"serverActionReducer",{enumerable:!0,get:function(){return serverActionReducer}});let i=a(5422),s=a(4361),u=a(8026),d=a(4879),f=a(6879),p=a(1706),h=a(8237),m=a(9605),g=a(145),v=a(2428),y=a(3466),b=a(5929),{createFromFetch:_,encodeReply:w}=a(2623);async function fetchServerAction(e,n){let a,{actionId:u,actionArgs:d}=n,p=await w(d),h=await fetch("",{method:"POST",headers:{Accept:s.RSC_CONTENT_TYPE_HEADER,[s.ACTION]:u,[s.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(e.tree)),...e.nextUrl?{[s.NEXT_URL]:e.nextUrl}:{}},body:p}),m=h.headers.get("x-action-redirect");try{let e=JSON.parse(h.headers.get("x-action-revalidated")||"[[],0,0]");a={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){a={paths:[],tag:!1,cookie:!1}}let g=m?new URL((0,f.addBasePath)(m),new URL(e.canonicalUrl,window.location.href)):void 0;if(h.headers.get("content-type")===s.RSC_CONTENT_TYPE_HEADER){let e=await _(Promise.resolve(h),{callServer:i.callServer});if(m){let[,n]=null!=e?e:[];return{actionFlightData:n,redirectLocation:g,revalidatedParts:a}}let[n,[,s]]=null!=e?e:[];return{actionResult:n,actionFlightData:s,redirectLocation:g,revalidatedParts:a}}return{redirectLocation:g,revalidatedParts:a}}function serverActionReducer(e,n){let{mutable:a,cache:i,resolve:s,reject:f}=n,_=e.canonicalUrl,w=e.tree,x=JSON.stringify(a.previousTree)===JSON.stringify(w);if(x)return(0,y.handleMutable)(e,a);if(a.inFlightServerAction){if("fulfilled"!==a.inFlightServerAction.status&&a.globalMutable.pendingNavigatePath&&a.globalMutable.pendingNavigatePath!==_)return a.inFlightServerAction.then(()=>{a.actionResultResolved||(a.inFlightServerAction=null,a.globalMutable.pendingNavigatePath=void 0,a.globalMutable.refresh(),a.actionResultResolved=!0)},()=>{}),e}else a.inFlightServerAction=(0,u.createRecordFromThenable)(fetchServerAction(e,n));try{let{actionResult:n,actionFlightData:u,redirectLocation:f}=(0,d.readRecordValue)(a.inFlightServerAction);if(f&&(e.pushRef.pendingPush=!0,a.pendingPush=!0),a.previousTree=e.tree,!u){if(a.actionResultResolved||(s(n),a.actionResultResolved=!0),f)return(0,h.handleExternalUrl)(e,a,f.href,e.pushRef.pendingPush);return e}if("string"==typeof u)return(0,h.handleExternalUrl)(e,a,u,e.pushRef.pendingPush);for(let n of(a.inFlightServerAction=null,u)){if(3!==n.length)return console.log("SERVER ACTION APPLY FAILED"),e;let[s]=n,u=(0,m.applyRouterStatePatchToTree)([""],w,s);if(null===u)throw Error("SEGMENT MISMATCH");if((0,g.isNavigatingToNewRootLayout)(w,u))return(0,h.handleExternalUrl)(e,a,_,e.pushRef.pendingPush);let[d,f]=n.slice(-2);null!==d&&(i.status=v.CacheStates.READY,i.subTreeData=d,(0,b.fillLazyItemsTillLeafWithHead)(i,void 0,s,f),a.cache=i,a.prefetchCache=new Map),a.previousTree=w,a.patchedTree=u,a.canonicalUrl=_,w=u}if(f){let e=(0,p.createHrefFromUrl)(f,!1);a.canonicalUrl=e}return a.actionResultResolved||(s(n),a.actionResultResolved=!0),(0,y.handleMutable)(e,a)}catch(n){if("rejected"===n.status)return a.actionResultResolved||(f(n.reason),a.actionResultResolved=!0),e;throw n}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},9794:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"serverPatchReducer",{enumerable:!0,get:function(){return serverPatchReducer}});let i=a(1706),s=a(9605),u=a(145),d=a(8237),f=a(1847),p=a(3466);function serverPatchReducer(e,n){let{flightData:a,previousTree:h,overrideCanonicalUrl:m,cache:g,mutable:v}=n,y=JSON.stringify(h)===JSON.stringify(e.tree);if(!y)return console.log("TREE MISMATCH"),e;if(v.previousTree)return(0,p.handleMutable)(e,v);if("string"==typeof a)return(0,d.handleExternalUrl)(e,v,a,e.pushRef.pendingPush);let b=e.tree,_=e.cache;for(let n of a){let a=n.slice(0,-4),[p]=n.slice(-3,-2),h=(0,s.applyRouterStatePatchToTree)(["",...a],b,p);if(null===h)throw Error("SEGMENT MISMATCH");if((0,u.isNavigatingToNewRootLayout)(b,h))return(0,d.handleExternalUrl)(e,v,e.canonicalUrl,e.pushRef.pendingPush);let y=m?(0,i.createHrefFromUrl)(m):void 0;y&&(v.canonicalUrl=y),(0,f.applyFlightData)(_,g,n),v.previousTree=b,v.patchedTree=h,v.cache=g,_=g,b=h}return(0,p.handleMutable)(e,v)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},3678:(e,n)=>{"use strict";var a;Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{PrefetchKind:function(){return a},ACTION_REFRESH:function(){return i},ACTION_NAVIGATE:function(){return s},ACTION_RESTORE:function(){return u},ACTION_SERVER_PATCH:function(){return d},ACTION_PREFETCH:function(){return f},ACTION_FAST_REFRESH:function(){return p},ACTION_SERVER_ACTION:function(){return h}});let i="refresh",s="navigate",u="restore",d="server-patch",f="prefetch",p="fast-refresh",h="server-action";(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(a||(a={})),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},7986:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"reducer",{enumerable:!0,get:function(){return reducer}}),a(3678),a(8237),a(9794),a(2910),a(8038),a(1196),a(2755),a(9747);let reducer=function(e,n){return e};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4320:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"shouldHardNavigate",{enumerable:!0,get:function(){return function shouldHardNavigate(e,n){let[a,s]=n,[u,d]=e;if(!(0,i.matchSegment)(u,a))return!!Array.isArray(u);let f=e.length<=2;return!f&&shouldHardNavigate(e.slice(2),s[d])}}});let i=a(4538);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},3032:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"createSearchParamsBailoutProxy",{enumerable:!0,get:function(){return createSearchParamsBailoutProxy}});let i=a(1492);function createSearchParamsBailoutProxy(){return new Proxy({},{get(e,n){"string"==typeof n&&(0,i.staticGenerationBailout)("searchParams."+n)}})}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},1492:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"staticGenerationBailout",{enumerable:!0,get:function(){return staticGenerationBailout}});let i=a(5171),s=a(4749);let StaticGenBailoutError=class StaticGenBailoutError extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}};function formatErrorMessage(e,n){let{dynamic:a,link:i}=n||{};return"Page"+(a?' with `dynamic = "'+a+'"`':"")+" couldn't be rendered statically because it used `"+e+"`."+(i?" See more info here: "+i:"")}let staticGenerationBailout=(e,n)=>{let a=s.staticGenerationAsyncStorage.getStore();if(null==a?void 0:a.forceStatic)return!0;if(null==a?void 0:a.dynamicShouldError){var u;throw new StaticGenBailoutError(formatErrorMessage(e,{...n,dynamic:null!=(u=null==n?void 0:n.dynamic)?u:"error"}))}if(!a||(a.revalidate=0,(null==n?void 0:n.dynamic)||(a.staticPrefetchBailout=!0)),null==a?void 0:a.isStaticGeneration){let s=new i.DynamicServerError(formatErrorMessage(e,{...n,link:"https://nextjs.org/docs/messages/dynamic-server-error"}));throw a.dynamicUsageDescription=e,a.dynamicUsageStack=s.stack,s}return!1};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},8898:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return StaticGenerationSearchParamsBailoutProvider}});let i=a(2147),s=i._(a(9885)),u=a(3032);function StaticGenerationSearchParamsBailoutProvider(e){let{Component:n,propsForComponent:a,isStaticGeneration:i}=e;if(i){let e=(0,u.createSearchParamsBailoutProxy)();return s.default.createElement(n,{searchParams:e,...a})}return s.default.createElement(n,a)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},9236:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"useReducerWithReduxDevtools",{enumerable:!0,get:function(){return useReducerWithReduxDevtools}});let i=a(9885),useReducerWithReduxDevtools=function(e,n){let[a,s]=(0,i.useReducer)(e,n);return[a,s,()=>{}]};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},9760:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"hasBasePath",{enumerable:!0,get:function(){return hasBasePath}});let i=a(6364);function hasBasePath(e){return(0,i.pathHasPrefix)(e,"")}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},6945:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return normalizePathTrailingSlash}});let i=a(6923),s=a(5525),normalizePathTrailingSlash=e=>{if(!e.startsWith("/"))return e;let{pathname:n,query:a,hash:u}=(0,s.parsePath)(e);return""+(0,i.removeTrailingSlash)(n)+a+u};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},4978:(e,n,a)=>{"use strict";function removeBasePath(e){return e}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"removeBasePath",{enumerable:!0,get:function(){return removeBasePath}}),a(9760),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},755:(e,n)=>{"use strict";function djb2Hash(e){let n=5381;for(let a=0;a<e.length;a++){let i=e.charCodeAt(a);n=(n<<5)+n+i}return Math.abs(n)}function hexHash(e){return djb2Hash(e).toString(36).slice(0,5)}Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{djb2Hash:function(){return djb2Hash},hexHash:function(){return hexHash}})},6800:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{suspense:function(){return suspense},NoSSR:function(){return NoSSR}});let i=a(1118);function suspense(){let e=Error(i.NEXT_DYNAMIC_NO_SSR_CODE);throw e.digest=i.NEXT_DYNAMIC_NO_SSR_CODE,e}function NoSSR(e){let{children:n}=e;return suspense(),n}},1118:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"NEXT_DYNAMIC_NO_SSR_CODE",{enumerable:!0,get:function(){return a}});let a="NEXT_DYNAMIC_NO_SSR_CODE"},1518:(e,n)=>{"use strict";function ensureLeadingSlash(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"ensureLeadingSlash",{enumerable:!0,get:function(){return ensureLeadingSlash}})},8549:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"addPathPrefix",{enumerable:!0,get:function(){return addPathPrefix}});let i=a(5525);function addPathPrefix(e,n){if(!e.startsWith("/")||!n)return e;let{pathname:a,query:s,hash:u}=(0,i.parsePath)(e);return""+n+a+s+u}},8321:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{normalizeAppPath:function(){return normalizeAppPath},normalizeRscPath:function(){return normalizeRscPath}});let i=a(1518),s=a(392);function normalizeAppPath(e){return(0,i.ensureLeadingSlash)(e.split("/").reduce((e,n,a,i)=>!n||(0,s.isGroupSegment)(n)||"@"===n[0]||("page"===n||"route"===n)&&a===i.length-1?e:e+"/"+n,""))}function normalizeRscPath(e,n){return n?e.replace(/\.rsc($|\?)/,"$1"):e}},4448:(e,n)=>{"use strict";function handleSmoothScroll(e,n){if(void 0===n&&(n={}),n.onlyHashChange){e();return}let a=document.documentElement,i=a.style.scrollBehavior;a.style.scrollBehavior="auto",n.dontForceLayout||a.getClientRects(),e(),a.style.scrollBehavior=i}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"handleSmoothScroll",{enumerable:!0,get:function(){return handleSmoothScroll}})},4692:(e,n)=>{"use strict";function isBot(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isBot",{enumerable:!0,get:function(){return isBot}})},5525:(e,n)=>{"use strict";function parsePath(e){let n=e.indexOf("#"),a=e.indexOf("?"),i=a>-1&&(n<0||a<n);return i||n>-1?{pathname:e.substring(0,i?a:n),query:i?e.substring(a,n>-1?n:void 0):"",hash:n>-1?e.slice(n):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"parsePath",{enumerable:!0,get:function(){return parsePath}})},6364:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"pathHasPrefix",{enumerable:!0,get:function(){return pathHasPrefix}});let i=a(5525);function pathHasPrefix(e,n){if("string"!=typeof e)return!1;let{pathname:a}=(0,i.parsePath)(e);return a===n||a.startsWith(n+"/")}},6923:(e,n)=>{"use strict";function removeTrailingSlash(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"removeTrailingSlash",{enumerable:!0,get:function(){return removeTrailingSlash}})},392:(e,n)=>{"use strict";function isGroupSegment(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isGroupSegment",{enumerable:!0,get:function(){return isGroupSegment}})},5153:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"createProxy",{enumerable:!0,get:function(){return s}});let i=a(5951),s=i.createClientModuleProxy},8730:(e,n,a)=>{"use strict";let{createProxy:i}=a(5153);e.exports=i("/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/app-router.js")},7284:(e,n,a)=>{"use strict";let{createProxy:i}=a(5153);e.exports=i("/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/error-boundary.js")},9195:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{DYNAMIC_ERROR_CODE:function(){return a},DynamicServerError:function(){return DynamicServerError}});let a="DYNAMIC_SERVER_USAGE";let DynamicServerError=class DynamicServerError extends Error{constructor(e){super("Dynamic server usage: "+e),this.digest=a}};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},8165:(e,n,a)=>{"use strict";let{createProxy:i}=a(5153);e.exports=i("/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/layout-router.js")},4009:(e,n,a)=>{"use strict";let{createProxy:i}=a(5153);e.exports=i("/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/not-found-boundary.js")},5676:(e,n,a)=>{"use strict";let{createProxy:i}=a(5153);e.exports=i("/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/render-from-template-context.js")},1263:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"createSearchParamsBailoutProxy",{enumerable:!0,get:function(){return createSearchParamsBailoutProxy}});let i=a(3657);function createSearchParamsBailoutProxy(){return new Proxy({},{get(e,n){"string"==typeof n&&(0,i.staticGenerationBailout)("searchParams."+n)}})}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},3657:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"staticGenerationBailout",{enumerable:!0,get:function(){return staticGenerationBailout}});let i=a(9195),s=a(5869);let StaticGenBailoutError=class StaticGenBailoutError extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}};function formatErrorMessage(e,n){let{dynamic:a,link:i}=n||{};return"Page"+(a?' with `dynamic = "'+a+'"`':"")+" couldn't be rendered statically because it used `"+e+"`."+(i?" See more info here: "+i:"")}let staticGenerationBailout=(e,n)=>{let a=s.staticGenerationAsyncStorage.getStore();if(null==a?void 0:a.forceStatic)return!0;if(null==a?void 0:a.dynamicShouldError){var u;throw new StaticGenBailoutError(formatErrorMessage(e,{...n,dynamic:null!=(u=null==n?void 0:n.dynamic)?u:"error"}))}if(!a||(a.revalidate=0,(null==n?void 0:n.dynamic)||(a.staticPrefetchBailout=!0)),null==a?void 0:a.isStaticGeneration){let s=new i.DynamicServerError(formatErrorMessage(e,{...n,link:"https://nextjs.org/docs/messages/dynamic-server-error"}));throw a.dynamicUsageDescription=e,a.dynamicUsageStack=s.stack,s}return!1};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},7701:(e,n,a)=>{"use strict";let{createProxy:i}=a(5153);e.exports=i("/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js")},2564:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{renderToReadableStream:function(){return i.renderToReadableStream},decodeReply:function(){return i.decodeReply},decodeAction:function(){return i.decodeAction},decodeFormState:function(){return i.decodeFormState},AppRouter:function(){return s.default},LayoutRouter:function(){return u.default},RenderFromTemplateContext:function(){return d.default},staticGenerationAsyncStorage:function(){return f.staticGenerationAsyncStorage},requestAsyncStorage:function(){return p.requestAsyncStorage},actionAsyncStorage:function(){return h.actionAsyncStorage},staticGenerationBailout:function(){return m.staticGenerationBailout},createSearchParamsBailoutProxy:function(){return v.createSearchParamsBailoutProxy},serverHooks:function(){return y},preloadStyle:function(){return b.preloadStyle},preloadFont:function(){return b.preloadFont},preconnect:function(){return b.preconnect},StaticGenerationSearchParamsBailoutProvider:function(){return g.default},NotFoundBoundary:function(){return _}});let i=a(5951),s=_interop_require_default(a(8730)),u=_interop_require_default(a(8165)),d=_interop_require_default(a(5676)),f=a(5869),p=a(4580),h=a(2934),m=a(3657),g=_interop_require_default(a(7701)),v=a(1263),y=function(e,n){if(!n&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var a=_getRequireWildcardCache(n);if(a&&a.has(e))return a.get(e);var i={},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&Object.prototype.hasOwnProperty.call(e,u)){var d=s?Object.getOwnPropertyDescriptor(e,u):null;d&&(d.get||d.set)?Object.defineProperty(i,u,d):i[u]=e[u]}return i.default=e,a&&a.set(e,i),i}(a(9195)),b=a(8483);function _interop_require_default(e){return e&&e.__esModule?e:{default:e}}function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var n=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function(e){return e?a:n})(e)}let{NotFoundBoundary:_}=a(4009)},8483:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{preloadStyle:function(){return preloadStyle},preloadFont:function(){return preloadFont},preconnect:function(){return preconnect}});let i=function(e){return e&&e.__esModule?e:{default:e}}(a(8337));function preloadStyle(e,n){let a={as:"style"};"string"==typeof n&&(a.crossOrigin=n),i.default.preload(e,a)}function preloadFont(e,n,a){let s={as:"font",type:n};"string"==typeof a&&(s.crossOrigin=a),i.default.preload(e,s)}function preconnect(e,n){i.default.preconnect(e,"string"==typeof n?{crossOrigin:n}:void 0)}},6132:(e,n)=>{"use strict";var a;Object.defineProperty(n,"x",{enumerable:!0,get:function(){return a}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(a||(a={}))},7096:(e,n,a)=>{"use strict";e.exports=a(399)},8337:(e,n,a)=>{"use strict";e.exports=a(7096).vendored["react-rsc"].ReactDOM},4656:(e,n,a)=>{"use strict";e.exports=a(7096).vendored["react-rsc"].ReactJsxRuntime},5951:(e,n,a)=>{"use strict";e.exports=a(7096).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},2290:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"getSegmentParam",{enumerable:!0,get:function(){return getSegmentParam}});let i=a(4265);function getSegmentParam(e){let n=i.INTERCEPTION_ROUTE_MARKERS.find(n=>e.startsWith(n));return(n&&(e=e.slice(n.length)),e.startsWith("[[...")&&e.endsWith("]]"))?{type:"optional-catchall",param:e.slice(5,-2)}:e.startsWith("[...")&&e.endsWith("]")?{type:"catchall",param:e.slice(4,-1)}:e.startsWith("[")&&e.endsWith("]")?{type:"dynamic",param:e.slice(1,-1)}:null}},4265:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var a in n)Object.defineProperty(e,a,{enumerable:!0,get:n[a]})}(n,{INTERCEPTION_ROUTE_MARKERS:function(){return s},isInterceptionRouteAppPath:function(){return isInterceptionRouteAppPath},extractInterceptionRouteInformation:function(){return extractInterceptionRouteInformation}});let i=a(8321),s=["(..)(..)","(.)","(..)","(...)"];function isInterceptionRouteAppPath(e){return void 0!==e.split("/").find(e=>s.find(n=>e.startsWith(n)))}function extractInterceptionRouteInformation(e){let n,a,u;for(let i of e.split("/"))if(a=s.find(e=>i.startsWith(e))){[n,u]=e.split(a,2);break}if(!n||!a||!u)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(n=(0,i.normalizeAppPath)(n),a){case"(.)":u="/"===n?`/${u}`:n+"/"+u;break;case"(..)":if("/"===n)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);u=n.split("/").slice(0,-1).concat(u).join("/");break;case"(...)":u="/"+u;break;case"(..)(..)":let d=n.split("/");if(d.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);u=d.slice(0,-2).concat(u).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:n,interceptedRoute:u}}},316:(e,n,a)=>{"use strict";e.exports=a(399)},2428:(e,n,a)=>{"use strict";e.exports=a(316).vendored.contexts.AppRouterContext},1736:(e,n,a)=>{"use strict";e.exports=a(316).vendored.contexts.HooksClientContext},5753:(e,n,a)=>{"use strict";e.exports=a(316).vendored.contexts.ServerInsertedHtml},8908:(e,n,a)=>{"use strict";e.exports=a(316).vendored["react-ssr"].ReactDOM},784:(e,n,a)=>{"use strict";e.exports=a(316).vendored["react-ssr"].ReactJsxRuntime},2623:(e,n,a)=>{"use strict";e.exports=a(316).vendored["react-ssr"].ReactServerDOMWebpackClientEdge},9885:(e,n,a)=>{"use strict";e.exports=a(316).vendored["react-ssr"].React},4626:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveScrollBar=n.useLockAttribute=n.lockAttribute=void 0;var i=a(6158).__importStar(a(9885)),s=a(7034),u=a(9461),d=a(2989),f=(0,s.styleSingleton)();n.lockAttribute="data-scroll-locked";var getStyles=function(e,a,i,s){var d=e.left,f=e.top,p=e.right,h=e.gap;return void 0===i&&(i="margin"),"\n  .".concat(u.noScrollbarsClassName," {\n   overflow: hidden ").concat(s,";\n   padding-right: ").concat(h,"px ").concat(s,";\n  }\n  body[").concat(n.lockAttribute,"] {\n    overflow: hidden ").concat(s,";\n    overscroll-behavior: contain;\n    ").concat([a&&"position: relative ".concat(s,";"),"margin"===i&&"\n    padding-left: ".concat(d,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(p,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(s,";\n    "),"padding"===i&&"padding-right: ".concat(h,"px ").concat(s,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u.zeroRightClassName," {\n    right: ").concat(h,"px ").concat(s,";\n  }\n  \n  .").concat(u.fullWidthClassName," {\n    margin-right: ").concat(h,"px ").concat(s,";\n  }\n  \n  .").concat(u.zeroRightClassName," .").concat(u.zeroRightClassName," {\n    right: 0 ").concat(s,";\n  }\n  \n  .").concat(u.fullWidthClassName," .").concat(u.fullWidthClassName," {\n    margin-right: 0 ").concat(s,";\n  }\n  \n  body[").concat(n.lockAttribute,"] {\n    ").concat(u.removedBarSizeVariable,": ").concat(h,"px;\n  }\n")},getCurrentUseCounter=function(){var e=parseInt(document.body.getAttribute(n.lockAttribute)||"0",10);return isFinite(e)?e:0};n.useLockAttribute=function(){i.useEffect(function(){return document.body.setAttribute(n.lockAttribute,(getCurrentUseCounter()+1).toString()),function(){var e=getCurrentUseCounter()-1;e<=0?document.body.removeAttribute(n.lockAttribute):document.body.setAttribute(n.lockAttribute,e.toString())}},[])},n.RemoveScrollBar=function(e){var a=e.noRelative,s=e.noImportant,u=e.gapMode,p=void 0===u?"margin":u;(0,n.useLockAttribute)();var h=i.useMemo(function(){return(0,d.getGapWidth)(p)},[p]);return i.createElement(f,{styles:getStyles(h,!a,p,s?"":"!important")})}},9461:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.removedBarSizeVariable=n.noScrollbarsClassName=n.fullWidthClassName=n.zeroRightClassName=void 0,n.zeroRightClassName="right-scroll-bar-position",n.fullWidthClassName="width-before-scroll-bar",n.noScrollbarsClassName="with-scroll-bars-hidden",n.removedBarSizeVariable="--removed-body-scroll-bar-size"},2786:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getGapWidth=n.removedBarSizeVariable=n.noScrollbarsClassName=n.fullWidthClassName=n.zeroRightClassName=n.RemoveScrollBar=void 0;var i=a(4626);Object.defineProperty(n,"RemoveScrollBar",{enumerable:!0,get:function(){return i.RemoveScrollBar}});var s=a(9461);Object.defineProperty(n,"zeroRightClassName",{enumerable:!0,get:function(){return s.zeroRightClassName}}),Object.defineProperty(n,"fullWidthClassName",{enumerable:!0,get:function(){return s.fullWidthClassName}}),Object.defineProperty(n,"noScrollbarsClassName",{enumerable:!0,get:function(){return s.noScrollbarsClassName}}),Object.defineProperty(n,"removedBarSizeVariable",{enumerable:!0,get:function(){return s.removedBarSizeVariable}});var u=a(2989);Object.defineProperty(n,"getGapWidth",{enumerable:!0,get:function(){return u.getGapWidth}})},2989:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.getGapWidth=n.zeroGap=void 0,n.zeroGap={left:0,top:0,right:0,gap:0};var parse=function(e){return parseInt(e||"",10)||0},getOffset=function(e){var n=window.getComputedStyle(document.body),a=n["padding"===e?"paddingLeft":"marginLeft"],i=n["padding"===e?"paddingTop":"marginTop"],s=n["padding"===e?"paddingRight":"marginRight"];return[parse(a),parse(i),parse(s)]};n.getGapWidth=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return n.zeroGap;var a=getOffset(e),i=document.documentElement.clientWidth,s=window.innerWidth;return{left:a[0],top:a[1],right:a[2],gap:Math.max(0,s-i+a[2]-a[0])}}},840:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=a(6158),s=i.__importStar(a(9885)),u=a(3254),d=i.__importDefault(a(902)),f=s.forwardRef(function(e,n){return s.createElement(u.RemoveScroll,i.__assign({},e,{ref:n,sideCar:d.default}))});f.classNames=u.RemoveScroll.classNames,n.default=f},5320:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveScrollSideCar=n.getDeltaXY=n.getTouchXY=void 0;var i=a(6158),s=i.__importStar(a(9885)),u=a(2786),d=a(7034),f=a(3731),p=a(7557);n.getTouchXY=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},n.getDeltaXY=function(e){return[e.deltaX,e.deltaY]};var extractRef=function(e){return e&&"current"in e?e.current:e},h=0,m=[];n.RemoveScrollSideCar=function(e){var a=s.useRef([]),g=s.useRef([0,0]),v=s.useRef(),y=s.useState(h++)[0],b=s.useState(d.styleSingleton)[0],_=s.useRef(e);s.useEffect(function(){_.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(y));var n=i.__spreadArray([e.lockRef.current],(e.shards||[]).map(extractRef),!0).filter(Boolean);return n.forEach(function(e){return e.classList.add("allow-interactivity-".concat(y))}),function(){document.body.classList.remove("block-interactivity-".concat(y)),n.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(y))})}}},[e.inert,e.lockRef.current,e.shards]);var w=s.useCallback(function(e,a){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!_.current.allowPinchZoom;var i,s=(0,n.getTouchXY)(e),u=g.current,d="deltaX"in e?e.deltaX:u[0]-s[0],f="deltaY"in e?e.deltaY:u[1]-s[1],h=e.target,m=Math.abs(d)>Math.abs(f)?"h":"v";if("touches"in e&&"h"===m&&"range"===h.type)return!1;var y=(0,p.locationCouldBeScrolled)(m,h);if(!y)return!0;if(y?i=m:(i="v"===m?"h":"v",y=(0,p.locationCouldBeScrolled)(m,h)),!y)return!1;if(!v.current&&"changedTouches"in e&&(d||f)&&(v.current=i),!i)return!0;var b=v.current||i;return(0,p.handleScroll)(b,a,e,"h"===b?d:f,!0)},[]),x=s.useCallback(function(e){if(m.length&&m[m.length-1]===b){var i="deltaY"in e?(0,n.getDeltaXY)(e):(0,n.getTouchXY)(e),s=a.current.filter(function(n){var a;return n.name===e.type&&(n.target===e.target||e.target===n.shadowParent)&&(a=n.delta)[0]===i[0]&&a[1]===i[1]})[0];if(s&&s.should){e.cancelable&&e.preventDefault();return}if(!s){var u=(_.current.shards||[]).map(extractRef).filter(Boolean).filter(function(n){return n.contains(e.target)});(u.length>0?w(e,u[0]):!_.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),E=s.useCallback(function(e,n,i,s){var u={name:e,delta:n,target:i,should:s,shadowParent:function(e){for(var n=null;null!==e;)e instanceof ShadowRoot&&(n=e.host,e=e.host),e=e.parentNode;return n}(i)};a.current.push(u),setTimeout(function(){a.current=a.current.filter(function(e){return e!==u})},1)},[]),S=s.useCallback(function(e){g.current=(0,n.getTouchXY)(e),v.current=void 0},[]),P=s.useCallback(function(a){E(a.type,(0,n.getDeltaXY)(a),a.target,w(a,e.lockRef.current))},[]),R=s.useCallback(function(a){E(a.type,(0,n.getTouchXY)(a),a.target,w(a,e.lockRef.current))},[]);s.useEffect(function(){return m.push(b),e.setCallbacks({onScrollCapture:P,onWheelCapture:P,onTouchMoveCapture:R}),document.addEventListener("wheel",x,f.nonPassive),document.addEventListener("touchmove",x,f.nonPassive),document.addEventListener("touchstart",S,f.nonPassive),function(){m=m.filter(function(e){return e!==b}),document.removeEventListener("wheel",x,f.nonPassive),document.removeEventListener("touchmove",x,f.nonPassive),document.removeEventListener("touchstart",S,f.nonPassive)}},[]);var C=e.removeScrollBar,O=e.inert;return s.createElement(s.Fragment,null,O?s.createElement(b,{styles:"\n  .block-interactivity-".concat(y," {pointer-events: none;}\n  .allow-interactivity-").concat(y," {pointer-events: all;}\n")}):null,C?s.createElement(u.RemoveScrollBar,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}},3254:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveScroll=void 0;var i=a(6158),s=i.__importStar(a(9885)),u=a(9461),d=a(3609),f=a(5210),nothing=function(){},p=s.forwardRef(function(e,n){var a=s.useRef(null),u=s.useState({onScrollCapture:nothing,onWheelCapture:nothing,onTouchMoveCapture:nothing}),p=u[0],h=u[1],m=e.forwardProps,g=e.children,v=e.className,y=e.removeScrollBar,b=e.enabled,_=e.shards,w=e.sideCar,x=e.noRelative,E=e.noIsolation,S=e.inert,P=e.allowPinchZoom,R=e.as,C=void 0===R?"div":R,O=e.gapMode,j=i.__rest(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),M=(0,d.useMergeRefs)([a,n]),N=i.__assign(i.__assign({},j),p);return s.createElement(s.Fragment,null,b&&s.createElement(w,{sideCar:f.effectCar,removeScrollBar:y,shards:_,noRelative:x,noIsolation:E,inert:S,setCallbacks:h,allowPinchZoom:!!P,lockRef:a,gapMode:O}),m?s.cloneElement(s.Children.only(g),i.__assign(i.__assign({},N),{ref:M})):s.createElement(C,i.__assign({},N,{className:v,ref:M}),g))});n.RemoveScroll=p,p.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},p.classNames={fullWidth:u.fullWidthClassName,zeroRight:u.zeroRightClassName}},3731:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.nonPassive=void 0;var a=!1;if("undefined"!=typeof window)try{var i=Object.defineProperty({},"passive",{get:function(){return a=!0,!0}});window.addEventListener("test",i,i),window.removeEventListener("test",i,i)}catch(e){a=!1}n.nonPassive=!!a&&{passive:!1}},7557:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.handleScroll=n.locationCouldBeScrolled=void 0;var elementCanBeScrolled=function(e,n){if(!(e instanceof Element))return!1;var a=window.getComputedStyle(e);return"hidden"!==a[n]&&!(a.overflowY===a.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===a[n])};n.locationCouldBeScrolled=function(e,n){var a=n.ownerDocument,i=n;do{if("undefined"!=typeof ShadowRoot&&i instanceof ShadowRoot&&(i=i.host),elementCouldBeScrolled(e,i)){var s=getScrollVariables(e,i);if(s[1]>s[2])return!0}i=i.parentNode}while(i&&i!==a.body);return!1};var elementCouldBeScrolled=function(e,n){return"v"===e?elementCanBeScrolled(n,"overflowY"):elementCanBeScrolled(n,"overflowX")},getScrollVariables=function(e,n){return"v"===e?[n.scrollTop,n.scrollHeight,n.clientHeight]:[n.scrollLeft,n.scrollWidth,n.clientWidth]};n.handleScroll=function(e,n,a,i,s){var u,d=(u=window.getComputedStyle(n).direction,"h"===e&&"rtl"===u?-1:1),f=d*i,p=a.target,h=n.contains(p),m=!1,g=f>0,v=0,y=0;do{if(!p)break;var b=getScrollVariables(e,p),_=b[0],w=b[1]-b[2]-d*_;(_||w)&&elementCouldBeScrolled(e,p)&&(v+=w,y+=_);var x=p.parentNode;p=x&&x.nodeType===Node.DOCUMENT_FRAGMENT_NODE?x.host:x}while(!h&&p!==document.body||h&&(n.contains(p)||n===p));return g&&(s&&1>Math.abs(v)||!s&&f>v)?m=!0:!g&&(s&&1>Math.abs(y)||!s&&-f>y)&&(m=!0),m}},2864:(e,n,a)=>{"use strict";n.f=void 0;var i=a(6158).__importDefault(a(840));n.f=i.default},5210:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.effectCar=void 0;var i=a(8221);n.effectCar=(0,i.createSidecarMedium)()},902:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0});var i=a(8221),s=a(5320),u=a(5210);n.default=(0,i.exportSidecar)(u.effectCar,s.RemoveScrollSideCar)},200:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.styleSingleton=void 0;var i=a(9812);n.styleSingleton=function(){var e=(0,i.styleHookSingleton)();return function(n){return e(n.styles,n.dynamic),null}}},9812:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.styleHookSingleton=void 0;var i=a(6158).__importStar(a(9885)),s=a(1995);n.styleHookSingleton=function(){var e=(0,s.stylesheetSingleton)();return function(n,a){i.useEffect(function(){return e.add(n),function(){e.remove()}},[n&&a])}}},7034:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.styleHookSingleton=n.stylesheetSingleton=n.styleSingleton=void 0;var i=a(200);Object.defineProperty(n,"styleSingleton",{enumerable:!0,get:function(){return i.styleSingleton}});var s=a(1995);Object.defineProperty(n,"stylesheetSingleton",{enumerable:!0,get:function(){return s.stylesheetSingleton}});var u=a(9812);Object.defineProperty(n,"styleHookSingleton",{enumerable:!0,get:function(){return u.styleHookSingleton}})},1995:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.stylesheetSingleton=void 0;var i=a(7643);n.stylesheetSingleton=function(){var e=0,n=null;return{add:function(a){if(0==e&&(n=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var n=(0,i.getNonce)();return n&&e.setAttribute("nonce",n),e}())){var s,u;(s=n).styleSheet?s.styleSheet.cssText=a:s.appendChild(document.createTextNode(a)),u=n,(document.head||document.getElementsByTagName("head")[0]).appendChild(u)}e++},remove:function(){--e||!n||(n.parentNode&&n.parentNode.removeChild(n),n=null)}}}},1859:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.assignRef=void 0,n.assignRef=function(e,n){return"function"==typeof e?e(n):e&&(e.current=n),e}},319:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.createCallbackRef=void 0,n.createCallbackRef=function(e){var n=null;return{get current(){return n},set current(value){var a=n;a!==value&&(n=value,e(value,a))}}}},3609:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.useRefToCallback=n.refToCallback=n.transformRef=n.useTransformRef=n.useMergeRefs=n.mergeRefs=n.createCallbackRef=n.useCallbackRef=n.assignRef=void 0;var i=a(1859);Object.defineProperty(n,"assignRef",{enumerable:!0,get:function(){return i.assignRef}});var s=a(9308);Object.defineProperty(n,"useCallbackRef",{enumerable:!0,get:function(){return s.useCallbackRef}});var u=a(319);Object.defineProperty(n,"createCallbackRef",{enumerable:!0,get:function(){return u.createCallbackRef}});var d=a(6256);Object.defineProperty(n,"mergeRefs",{enumerable:!0,get:function(){return d.mergeRefs}});var f=a(233);Object.defineProperty(n,"useMergeRefs",{enumerable:!0,get:function(){return f.useMergeRefs}});var p=a(8750);Object.defineProperty(n,"useTransformRef",{enumerable:!0,get:function(){return p.useTransformRef}});var h=a(1461);Object.defineProperty(n,"transformRef",{enumerable:!0,get:function(){return h.transformRef}});var m=a(5601);Object.defineProperty(n,"refToCallback",{enumerable:!0,get:function(){return m.refToCallback}}),Object.defineProperty(n,"useRefToCallback",{enumerable:!0,get:function(){return m.useRefToCallback}})},6256:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.mergeRefs=void 0;var i=a(1859),s=a(319);n.mergeRefs=function(e){return(0,s.createCallbackRef)(function(n){return e.forEach(function(e){return(0,i.assignRef)(e,n)})})}},5601:(e,n)=>{"use strict";function refToCallback(e){return function(n){"function"==typeof e?e(n):e&&(e.current=n)}}Object.defineProperty(n,"__esModule",{value:!0}),n.useRefToCallback=n.refToCallback=void 0,n.refToCallback=refToCallback;var nullCallback=function(){return null},a=new WeakMap,weakMemoize=function(e){var n=e||nullCallback,i=a.get(n);if(i)return i;var s=refToCallback(n);return a.set(n,s),s};n.useRefToCallback=function(e){return weakMemoize(e)}},1461:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.transformRef=void 0;var i=a(1859),s=a(319);n.transformRef=function(e,n){return(0,s.createCallbackRef)(function(a){return(0,i.assignRef)(e,n(a))})}},233:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.useMergeRefs=void 0;var i=a(6158).__importStar(a(9885)),s=a(1859),u=a(9308),d="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,f=new WeakMap;n.useMergeRefs=function(e,n){var a=(0,u.useCallbackRef)(n||null,function(n){return e.forEach(function(e){return(0,s.assignRef)(e,n)})});return d(function(){var n=f.get(a);if(n){var i=new Set(n),u=new Set(e),d=a.current;i.forEach(function(e){u.has(e)||(0,s.assignRef)(e,null)}),u.forEach(function(e){i.has(e)||(0,s.assignRef)(e,d)})}f.set(a,e)},[e]),a}},9308:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.useCallbackRef=void 0;var i=a(9885);n.useCallbackRef=function(e,n){var a=(0,i.useState)(function(){return{value:e,callback:n,facade:{get current(){return a.value},set current(value){var i=a.value;i!==value&&(a.value=value,a.callback(value,i))}}}})[0];return a.callback=n,a.facade}},8750:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.useTransformRef=void 0;var i=a(1859),s=a(9308);n.useTransformRef=function(e,n){return(0,s.useCallbackRef)(null,function(a){return(0,i.assignRef)(e,n(a))})}},3164:(e,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.setConfig=n.config=void 0,n.config={onError:function(e){return console.error(e)}},n.setConfig=function(e){Object.assign(n.config,e)}},4614:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.env=void 0;var i=a(882);n.env={isNode:i.isNode,forceCache:!1}},1183:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.exportSidecar=void 0;var i=a(6158),s=i.__importStar(a(9885)),SideCar=function(e){var n=e.sideCar,a=i.__rest(e,["sideCar"]);if(!n)throw Error("Sidecar: please provide `sideCar` property to import the right car");var u=n.read();if(!u)throw Error("Sidecar medium not found");return s.createElement(u,i.__assign({},a))};SideCar.isSideCarExport=!0,n.exportSidecar=function(e,n){return e.useMedium(n),SideCar}},3711:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.sidecar=void 0;var i=a(6158),s=i.__importStar(a(9885)),u=a(3421);n.sidecar=function(e,n){var ErrorCase=function(){return n};return function(a){var d=(0,u.useSidecar)(e,a.sideCar),f=d[0];return d[1]&&n?ErrorCase:f?s.createElement(f,i.__assign({},a)):null}}},3421:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.useSidecar=void 0;var i=a(9885),s=a(4614),u=new WeakMap,d={};n.useSidecar=function(e,n){var a,f,p,h,m,g,v,y,b=n&&n.options||d;return s.env.isNode&&!b.ssr?[null,null]:(a=n&&n.options||d,f=s.env.forceCache||s.env.isNode&&!!a.ssr||!a.async,h=(p=(0,i.useState)(f?function(){return u.get(e)}:void 0))[0],m=p[1],v=(g=(0,i.useState)(null))[0],y=g[1],(0,i.useEffect)(function(){h||e().then(function(a){var i,s=n?n.read():a.default||a;if(!s)throw console.error("Sidecar error: with importer",e),n?(console.error("Sidecar error: with medium",n),i=Error("Sidecar medium was not found")):i=Error("Sidecar was not found in exports"),y(function(){return i}),i;u.set(e,s),m(function(){return s})},function(e){return y(function(){return e})})},[]),[h,v])}},8221:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.exportSidecar=n.renderCar=n.createSidecarMedium=n.createMedium=n.setConfig=n.useSidecar=n.sidecar=void 0;var i=a(3711);Object.defineProperty(n,"sidecar",{enumerable:!0,get:function(){return i.sidecar}});var s=a(3421);Object.defineProperty(n,"useSidecar",{enumerable:!0,get:function(){return s.useSidecar}});var u=a(3164);Object.defineProperty(n,"setConfig",{enumerable:!0,get:function(){return u.setConfig}});var d=a(8626);Object.defineProperty(n,"createMedium",{enumerable:!0,get:function(){return d.createMedium}}),Object.defineProperty(n,"createSidecarMedium",{enumerable:!0,get:function(){return d.createSidecarMedium}});var f=a(5919);Object.defineProperty(n,"renderCar",{enumerable:!0,get:function(){return f.renderCar}});var p=a(1183);Object.defineProperty(n,"exportSidecar",{enumerable:!0,get:function(){return p.exportSidecar}})},8626:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.createSidecarMedium=n.createMedium=void 0;var i=a(6158);function ItoI(e){return e}function innerCreateMedium(e,n){void 0===n&&(n=ItoI);var a=[],i=!1;return{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:e},useMedium:function(e){var s=n(e,i);return a.push(s),function(){a=a.filter(function(e){return e!==s})}},assignSyncMedium:function(e){for(i=!0;a.length;){var n=a;a=[],n.forEach(e)}a={push:function(n){return e(n)},filter:function(){return a}}},assignMedium:function(e){i=!0;var n=[];if(a.length){var s=a;a=[],s.forEach(e),n=a}var executeQueue=function(){var a=n;n=[],a.forEach(e)},cycle=function(){return Promise.resolve().then(executeQueue)};cycle(),a={push:function(e){n.push(e),cycle()},filter:function(e){return n=n.filter(e),a}}}}}n.createMedium=function(e,n){return void 0===n&&(n=ItoI),innerCreateMedium(e,n)},n.createSidecarMedium=function(e){void 0===e&&(e={});var n=innerCreateMedium(null);return n.options=i.__assign({async:!0,ssr:!1},e),n}},5919:(e,n,a)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.renderCar=void 0;var i=a(6158),s=i.__importStar(a(9885)),u=a(9885);n.renderCar=function(e,n){function State(n){var a=n.stateRef,d=n.props,f=(0,u.useCallback)(function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return(0,u.useLayoutEffect)(function(){a.current(e)}),null},[]);return s.createElement(e,i.__assign({},d,{children:f}))}var a=s.memo(function(e){var n=e.stateRef,a=e.defaultState,i=e.children,s=(0,u.useState)(a.current),d=s[0],f=s[1];return(0,u.useEffect)(function(){n.current=f},[]),i.apply(void 0,d)},function(){return!0});return function(e){var i=s.useRef(n(e)),u=s.useRef(function(e){return i.current=e});return s.createElement(s.Fragment,null,s.createElement(State,{stateRef:u,props:e}),s.createElement(a,{stateRef:u,defaultState:i,children:e.children}))}}},8324:(e,n)=>{"use strict";n._=n._class_private_field_loose_base=function(e,n){if(!Object.prototype.hasOwnProperty.call(e,n))throw TypeError("attempted to use private field on non-instance");return e}},4567:(e,n)=>{"use strict";var a=0;n._=n._class_private_field_loose_key=function(e){return"__private_"+a+++"_"+e}},2147:(e,n)=>{"use strict";n._=n._interop_require_default=function(e){return e&&e.__esModule?e:{default:e}}},7795:(e,n)=>{"use strict";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var n=new WeakMap,a=new WeakMap;return(_getRequireWildcardCache=function(e){return e?a:n})(e)}n._=n._interop_require_wildcard=function(e,n){if(!n&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var a=_getRequireWildcardCache(n);if(a&&a.has(e))return a.get(e);var i={},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&Object.prototype.hasOwnProperty.call(e,u)){var d=s?Object.getOwnPropertyDescriptor(e,u):null;d&&(d.get||d.set)?Object.defineProperty(i,u,d):i[u]=e[u]}return i.default=e,a&&a.set(e,i),i}},5418:(e,n,a)=>{"use strict";function composeEventHandlers(e,n,{checkForDefaultPrevented:a=!0}={}){return function(i){if(e?.(i),!1===a||!i.defaultPrevented)return n?.(i)}}a.d(n,{M:()=>composeEventHandlers})},5537:(e,n,a)=>{"use strict";a.d(n,{B:()=>createCollection});var i=a(9885),s=a(784),u=a(880),d=a(1085);function createCollection(e){let n=e+"CollectionProvider",[a,f]=function(e,n=[]){let a=[],createScope=()=>{let n=a.map(e=>i.createContext(e));return function(a){let s=a?.[e]||n;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:s}}),[a,s])}};return createScope.scopeName=e,[function(n,u){let d=i.createContext(u),f=a.length;function Provider(n){let{scope:a,children:u,...p}=n,h=a?.[e][f]||d,m=i.useMemo(()=>p,Object.values(p));return(0,s.jsx)(h.Provider,{value:m,children:u})}return a=[...a,u],Provider.displayName=n+"Provider",[Provider,function(a,s){let p=s?.[e][f]||d,h=i.useContext(p);if(h)return h;if(void 0!==u)return u;throw Error(`\`${a}\` must be used within \`${n}\``)}]},function(...e){let n=e[0];if(1===e.length)return n;let createScope=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=a.reduce((n,{useScope:a,scopeName:i})=>{let s=a(e),u=s[`__scope${i}`];return{...n,...u}},{});return i.useMemo(()=>({[`__scope${n.scopeName}`]:s}),[s])}};return createScope.scopeName=n.scopeName,createScope}(createScope,...n)]}(n),[p,h]=a(n,{collectionRef:{current:null},itemMap:new Map}),CollectionProvider=e=>{let{scope:n,children:a}=e,u=i.useRef(null),d=i.useRef(new Map).current;return(0,s.jsx)(p,{scope:n,itemMap:d,collectionRef:u,children:a})};CollectionProvider.displayName=n;let m=e+"CollectionSlot",g=i.forwardRef((e,n)=>{let{scope:a,children:i}=e,f=h(m,a),p=(0,u.e)(n,f.collectionRef);return(0,s.jsx)(d.g7,{ref:p,children:i})});g.displayName=m;let v=e+"CollectionItemSlot",y="data-radix-collection-item",b=i.forwardRef((e,n)=>{let{scope:a,children:f,...p}=e,m=i.useRef(null),g=(0,u.e)(n,m),b=h(v,a);return i.useEffect(()=>(b.itemMap.set(m,{ref:m,...p}),()=>void b.itemMap.delete(m))),(0,s.jsx)(d.g7,{[y]:"",ref:g,children:f})});return b.displayName=v,[{Provider:CollectionProvider,Slot:g,ItemSlot:b},function(n){let a=h(e+"CollectionConsumer",n),s=i.useCallback(()=>{let e=a.collectionRef.current;if(!e)return[];let n=Array.from(e.querySelectorAll(`[${y}]`)),i=Array.from(a.itemMap.values()),s=i.sort((e,a)=>n.indexOf(e.ref.current)-n.indexOf(a.ref.current));return s},[a.collectionRef,a.itemMap]);return s},f]}},880:(e,n,a)=>{"use strict";a.d(n,{F:()=>composeRefs,e:()=>useComposedRefs});var i=a(9885);function composeRefs(...e){return n=>e.forEach(e=>{"function"==typeof e?e(n):null!=e&&(e.current=n)})}function useComposedRefs(...e){return i.useCallback(composeRefs(...e),e)}},8718:(e,n,a)=>{"use strict";a.d(n,{b:()=>createContextScope});var i=a(9885),s=a(784);function createContextScope(e,n=[]){let a=[],createScope=()=>{let n=a.map(e=>i.createContext(e));return function(a){let s=a?.[e]||n;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:s}}),[a,s])}};return createScope.scopeName=e,[function(n,u){let d=i.createContext(u),f=a.length;a=[...a,u];let Provider=n=>{let{scope:a,children:u,...p}=n,h=a?.[e]?.[f]||d,m=i.useMemo(()=>p,Object.values(p));return(0,s.jsx)(h.Provider,{value:m,children:u})};return Provider.displayName=n+"Provider",[Provider,function(a,s){let p=s?.[e]?.[f]||d,h=i.useContext(p);if(h)return h;if(void 0!==u)return u;throw Error(`\`${a}\` must be used within \`${n}\``)}]},function(...e){let n=e[0];if(1===e.length)return n;let createScope=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=a.reduce((n,{useScope:a,scopeName:i})=>{let s=a(e),u=s[`__scope${i}`];return{...n,...u}},{});return i.useMemo(()=>({[`__scope${n.scopeName}`]:s}),[s])}};return createScope.scopeName=n.scopeName,createScope}(createScope,...n)]}},6705:(e,n,a)=>{"use strict";a.d(n,{x8:()=>eC,VY:()=>eS,dk:()=>eR,aV:()=>eE,h_:()=>ex,fC:()=>e_,Dx:()=>eP,xz:()=>ew});var i,s=a(9885),u=a.t(s,2);function composeEventHandlers(e,n,{checkForDefaultPrevented:a=!0}={}){return function(i){if(e?.(i),!1===a||!i.defaultPrevented)return n?.(i)}}function setRef(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}function composeRefs(...e){return n=>{let a=!1,i=e.map(e=>{let i=setRef(e,n);return a||"function"!=typeof i||(a=!0),i});if(a)return()=>{for(let n=0;n<i.length;n++){let a=i[n];"function"==typeof a?a():setRef(e[n],null)}}}}function useComposedRefs(...e){return s.useCallback(composeRefs(...e),e)}var d=a(784),f=globalThis?.document?s.useLayoutEffect:()=>{},p=u[" useId ".trim().toString()]||(()=>void 0),h=0;function useId(e){let[n,a]=s.useState(p());return f(()=>{e||a(e=>e??String(h++))},[e]),e||(n?`radix-${n}`:"")}var m=u[" useInsertionEffect ".trim().toString()]||f;function dist_composeEventHandlers(e,n,{checkForDefaultPrevented:a=!0}={}){return function(i){if(e?.(i),!1===a||!i.defaultPrevented)return n?.(i)}}Symbol("RADIX:SYNC_STATE");var g=a(8908);function dist_setRef(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}function dist_composeRefs(...e){return n=>{let a=!1,i=e.map(e=>{let i=dist_setRef(e,n);return a||"function"!=typeof i||(a=!0),i});if(a)return()=>{for(let n=0;n<i.length;n++){let a=i[n];"function"==typeof a?a():dist_setRef(e[n],null)}}}}function dist_useComposedRefs(...e){return s.useCallback(dist_composeRefs(...e),e)}var v=Symbol("radix.slottable");function isSlottable(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===v}var y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,n)=>{let a=function(e){let n=function(e){let n=s.forwardRef((e,n)=>{let{children:a,...i}=e;if(s.isValidElement(a)){let e,u;let d=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.ref:(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.props.ref:a.props.ref||a.ref,f=function(e,n){let a={...n};for(let i in n){let s=e[i],u=n[i],d=/^on[A-Z]/.test(i);d?s&&u?a[i]=(...e)=>{let n=u(...e);return s(...e),n}:s&&(a[i]=s):"style"===i?a[i]={...s,...u}:"className"===i&&(a[i]=[s,u].filter(Boolean).join(" "))}return{...e,...a}}(i,a.props);return a.type!==s.Fragment&&(f.ref=n?dist_composeRefs(n,d):d),s.cloneElement(a,f)}return s.Children.count(a)>1?s.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}(e),a=s.forwardRef((e,a)=>{let{children:i,...u}=e,f=s.Children.toArray(i),p=f.find(isSlottable);if(p){let e=p.props.children,i=f.map(n=>n!==p?n:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,d.jsx)(n,{...u,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,i):null})}return(0,d.jsx)(n,{...u,ref:a,children:i})});return a.displayName=`${e}.Slot`,a}(`Primitive.${n}`),i=s.forwardRef((e,i)=>{let{asChild:s,...u}=e,f=s?a:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(f,{...u,ref:i})});return i.displayName=`Primitive.${n}`,{...e,[n]:i}},{});function useCallbackRef(e){let n=s.useRef(e);return s.useEffect(()=>{n.current=e}),s.useMemo(()=>(...e)=>n.current?.(...e),[])}var b="dismissableLayer.update",_=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=s.forwardRef((e,n)=>{let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:h,onDismiss:m,...g}=e,v=s.useContext(_),[w,x]=s.useState(null),E=w?.ownerDocument??globalThis?.document,[,S]=s.useState({}),P=dist_useComposedRefs(n,e=>x(e)),R=Array.from(v.layers),[C]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),O=R.indexOf(C),j=w?R.indexOf(w):-1,M=v.layersWithOutsidePointerEventsDisabled.size>0,N=j>=O,D=function(e,n=globalThis?.document){let a=useCallbackRef(e),i=s.useRef(!1),u=s.useRef(()=>{});return s.useEffect(()=>{let handlePointerDown=e=>{if(e.target&&!i.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",a,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",u.current),u.current=handleAndDispatchPointerDownOutsideEvent2,n.addEventListener("click",u.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else n.removeEventListener("click",u.current);i.current=!1},e=window.setTimeout(()=>{n.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),n.removeEventListener("pointerdown",handlePointerDown),n.removeEventListener("click",u.current)}},[n,a]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let n=e.target,a=[...v.branches].some(e=>e.contains(n));!N||a||(f?.(e),h?.(e),e.defaultPrevented||m?.())},E),k=function(e,n=globalThis?.document){let a=useCallbackRef(e),i=s.useRef(!1);return s.useEffect(()=>{let handleFocus=e=>{e.target&&!i.current&&handleAndDispatchCustomEvent("dismissableLayer.focusOutside",a,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",handleFocus),()=>n.removeEventListener("focusin",handleFocus)},[n,a]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let n=e.target,a=[...v.branches].some(e=>e.contains(n));a||(p?.(e),h?.(e),e.defaultPrevented||m?.())},E);return function(e,n=globalThis?.document){let a=function(e){let n=s.useRef(e);return s.useEffect(()=>{n.current=e}),s.useMemo(()=>(...e)=>n.current?.(...e),[])}(e);s.useEffect(()=>{let handleKeyDown=e=>{"Escape"===e.key&&a(e)};return n.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>n.removeEventListener("keydown",handleKeyDown,{capture:!0})},[a,n])}(e=>{let n=j===v.layers.size-1;n&&(u?.(e),!e.defaultPrevented&&m&&(e.preventDefault(),m()))},E),s.useEffect(()=>{if(w)return a&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(i=E.body.style.pointerEvents,E.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(w)),v.layers.add(w),dispatchUpdate(),()=>{a&&1===v.layersWithOutsidePointerEventsDisabled.size&&(E.body.style.pointerEvents=i)}},[w,E,a,v]),s.useEffect(()=>()=>{w&&(v.layers.delete(w),v.layersWithOutsidePointerEventsDisabled.delete(w),dispatchUpdate())},[w,v]),s.useEffect(()=>{let handleUpdate=()=>S({});return document.addEventListener(b,handleUpdate),()=>document.removeEventListener(b,handleUpdate)},[]),(0,d.jsx)(y.div,{...g,ref:P,style:{pointerEvents:M?N?"auto":"none":void 0,...e.style},onFocusCapture:dist_composeEventHandlers(e.onFocusCapture,k.onFocusCapture),onBlurCapture:dist_composeEventHandlers(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:dist_composeEventHandlers(e.onPointerDownCapture,D.onPointerDownCapture)})});function dispatchUpdate(){let e=new CustomEvent(b);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,n,a,{discrete:i}){let s=a.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:a});(n&&s.addEventListener(e,n,{once:!0}),i)?s&&g.flushSync(()=>s.dispatchEvent(u)):s.dispatchEvent(u)}function react_compose_refs_dist_setRef(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}function react_compose_refs_dist_composeRefs(...e){return n=>{let a=!1,i=e.map(e=>{let i=react_compose_refs_dist_setRef(e,n);return a||"function"!=typeof i||(a=!0),i});if(a)return()=>{for(let n=0;n<i.length;n++){let a=i[n];"function"==typeof a?a():react_compose_refs_dist_setRef(e[n],null)}}}}w.displayName="DismissableLayer",s.forwardRef((e,n)=>{let a=s.useContext(_),i=s.useRef(null),u=dist_useComposedRefs(n,i);return s.useEffect(()=>{let e=i.current;if(e)return a.branches.add(e),()=>{a.branches.delete(e)}},[a.branches]),(0,d.jsx)(y.div,{...e,ref:u})}).displayName="DismissableLayerBranch";var x=Symbol("radix.slottable");function dist_isSlottable(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===x}var E=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,n)=>{let a=function(e){let n=function(e){let n=s.forwardRef((e,n)=>{let{children:a,...i}=e;if(s.isValidElement(a)){let e,u;let d=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.ref:(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.props.ref:a.props.ref||a.ref,f=function(e,n){let a={...n};for(let i in n){let s=e[i],u=n[i],d=/^on[A-Z]/.test(i);d?s&&u?a[i]=(...e)=>{let n=u(...e);return s(...e),n}:s&&(a[i]=s):"style"===i?a[i]={...s,...u}:"className"===i&&(a[i]=[s,u].filter(Boolean).join(" "))}return{...e,...a}}(i,a.props);return a.type!==s.Fragment&&(f.ref=n?react_compose_refs_dist_composeRefs(n,d):d),s.cloneElement(a,f)}return s.Children.count(a)>1?s.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}(e),a=s.forwardRef((e,a)=>{let{children:i,...u}=e,f=s.Children.toArray(i),p=f.find(dist_isSlottable);if(p){let e=p.props.children,i=f.map(n=>n!==p?n:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,d.jsx)(n,{...u,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,i):null})}return(0,d.jsx)(n,{...u,ref:a,children:i})});return a.displayName=`${e}.Slot`,a}(`Primitive.${n}`),i=s.forwardRef((e,i)=>{let{asChild:s,...u}=e,f=s?a:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(f,{...u,ref:i})});return i.displayName=`Primitive.${n}`,{...e,[n]:i}},{});function react_use_callback_ref_dist_useCallbackRef(e){let n=s.useRef(e);return s.useEffect(()=>{n.current=e}),s.useMemo(()=>(...e)=>n.current?.(...e),[])}var S="focusScope.autoFocusOnMount",P="focusScope.autoFocusOnUnmount",R={bubbles:!1,cancelable:!0},C=s.forwardRef((e,n)=>{let{loop:a=!1,trapped:i=!1,onMountAutoFocus:u,onUnmountAutoFocus:f,...p}=e,[h,m]=s.useState(null),g=react_use_callback_ref_dist_useCallbackRef(u),v=react_use_callback_ref_dist_useCallbackRef(f),y=s.useRef(null),b=function(...e){return s.useCallback(react_compose_refs_dist_composeRefs(...e),e)}(n,e=>m(e)),_=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(i){let handleFocusIn2=function(e){if(_.paused||!h)return;let n=e.target;h.contains(n)?y.current=n:dist_focus(y.current,{select:!0})},handleFocusOut2=function(e){if(_.paused||!h)return;let n=e.relatedTarget;null===n||h.contains(n)||dist_focus(y.current,{select:!0})};document.addEventListener("focusin",handleFocusIn2),document.addEventListener("focusout",handleFocusOut2);let e=new MutationObserver(function(e){let n=document.activeElement;if(n===document.body)for(let n of e)n.removedNodes.length>0&&dist_focus(h)});return h&&e.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",handleFocusIn2),document.removeEventListener("focusout",handleFocusOut2),e.disconnect()}}},[i,h,_.paused]),s.useEffect(()=>{if(h){O.add(_);let e=document.activeElement,n=h.contains(e);if(!n){let n=new CustomEvent(S,R);h.addEventListener(S,g),h.dispatchEvent(n),n.defaultPrevented||(function(e,{select:n=!1}={}){let a=document.activeElement;for(let i of e)if(dist_focus(i,{select:n}),document.activeElement!==a)return}(getTabbableCandidates(h).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&dist_focus(h))}return()=>{h.removeEventListener(S,g),setTimeout(()=>{let n=new CustomEvent(P,R);h.addEventListener(P,v),h.dispatchEvent(n),n.defaultPrevented||dist_focus(e??document.body,{select:!0}),h.removeEventListener(P,v),O.remove(_)},0)}}},[h,g,v,_]);let w=s.useCallback(e=>{if(!a&&!i||_.paused)return;let n="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,s=document.activeElement;if(n&&s){let n=e.currentTarget,[i,u]=function(e){let n=getTabbableCandidates(e),a=findVisible(n,e),i=findVisible(n.reverse(),e);return[a,i]}(n),d=i&&u;d?e.shiftKey||s!==u?e.shiftKey&&s===i&&(e.preventDefault(),a&&dist_focus(u,{select:!0})):(e.preventDefault(),a&&dist_focus(i,{select:!0})):s===n&&e.preventDefault()}},[a,i,_.paused]);return(0,d.jsx)(E.div,{tabIndex:-1,...p,ref:b,onKeyDown:w})});function getTabbableCandidates(e){let n=[],a=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let n="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||n?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;a.nextNode();)n.push(a.currentNode);return n}function findVisible(e,n){for(let a of e)if(!function(e,{upTo:n}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(a,{upTo:n}))return a}function dist_focus(e,{select:n=!1}={}){if(e&&e.focus){var a;let i=document.activeElement;e.focus({preventScroll:!0}),e!==i&&(a=e)instanceof HTMLInputElement&&"select"in a&&n&&e.select()}}C.displayName="FocusScope";var O=function(){let e=[];return{add(n){let a=e[0];n!==a&&a?.pause(),(e=arrayRemove(e,n)).unshift(n)},remove(n){e=arrayRemove(e,n),e[0]?.resume()}}}();function arrayRemove(e,n){let a=[...e],i=a.indexOf(n);return -1!==i&&a.splice(i,1),a}function _radix_ui_react_compose_refs_dist_setRef(e,n){if("function"==typeof e)return e(n);null!=e&&(e.current=n)}var j=Symbol("radix.slottable");function react_slot_dist_isSlottable(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===j}var M=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,n)=>{let a=function(e){let n=function(e){let n=s.forwardRef((e,n)=>{let{children:a,...i}=e;if(s.isValidElement(a)){let e,u;let d=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.ref:(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.props.ref:a.props.ref||a.ref,f=function(e,n){let a={...n};for(let i in n){let s=e[i],u=n[i],d=/^on[A-Z]/.test(i);d?s&&u?a[i]=(...e)=>{let n=u(...e);return s(...e),n}:s&&(a[i]=s):"style"===i?a[i]={...s,...u}:"className"===i&&(a[i]=[s,u].filter(Boolean).join(" "))}return{...e,...a}}(i,a.props);return a.type!==s.Fragment&&(f.ref=n?function(...e){return n=>{let a=!1,i=e.map(e=>{let i=_radix_ui_react_compose_refs_dist_setRef(e,n);return a||"function"!=typeof i||(a=!0),i});if(a)return()=>{for(let n=0;n<i.length;n++){let a=i[n];"function"==typeof a?a():_radix_ui_react_compose_refs_dist_setRef(e[n],null)}}}}(n,d):d),s.cloneElement(a,f)}return s.Children.count(a)>1?s.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}(e),a=s.forwardRef((e,a)=>{let{children:i,...u}=e,f=s.Children.toArray(i),p=f.find(react_slot_dist_isSlottable);if(p){let e=p.props.children,i=f.map(n=>n!==p?n:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,d.jsx)(n,{...u,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,i):null})}return(0,d.jsx)(n,{...u,ref:a,children:i})});return a.displayName=`${e}.Slot`,a}(`Primitive.${n}`),i=s.forwardRef((e,i)=>{let{asChild:s,...u}=e,f=s?a:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(f,{...u,ref:i})});return i.displayName=`Primitive.${n}`,{...e,[n]:i}},{}),N=globalThis?.document?s.useLayoutEffect:()=>{},D=s.forwardRef((e,n)=>{let{container:a,...i}=e,[u,f]=s.useState(!1);N(()=>f(!0),[]);let p=a||u&&globalThis?.document?.body;return p?g.createPortal((0,d.jsx)(M.div,{...i,ref:n}),p):null});D.displayName="Portal";var Presence=e=>{let{present:n,children:a}=e,i=function(e){var n;let[a,i]=s.useState(),u=s.useRef(null),d=s.useRef(e),p=s.useRef("none"),h=e?"mounted":"unmounted",[m,g]=(n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,a)=>{let i=n[e][a];return i??e},h));return s.useEffect(()=>{let e=getAnimationName(u.current);p.current="mounted"===m?e:"none"},[m]),f(()=>{let n=u.current,a=d.current,i=a!==e;if(i){let i=p.current,s=getAnimationName(n);e?g("MOUNT"):"none"===s||n?.display==="none"?g("UNMOUNT"):a&&i!==s?g("ANIMATION_OUT"):g("UNMOUNT"),d.current=e}},[e,g]),f(()=>{if(a){let e;let n=a.ownerDocument.defaultView??window,handleAnimationEnd=i=>{let s=getAnimationName(u.current),f=s.includes(i.animationName);if(i.target===a&&f&&(g("ANIMATION_END"),!d.current)){let i=a.style.animationFillMode;a.style.animationFillMode="forwards",e=n.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=i)})}},handleAnimationStart=e=>{e.target===a&&(p.current=getAnimationName(u.current))};return a.addEventListener("animationstart",handleAnimationStart),a.addEventListener("animationcancel",handleAnimationEnd),a.addEventListener("animationend",handleAnimationEnd),()=>{n.clearTimeout(e),a.removeEventListener("animationstart",handleAnimationStart),a.removeEventListener("animationcancel",handleAnimationEnd),a.removeEventListener("animationend",handleAnimationEnd)}}g("ANIMATION_END")},[a,g]),{isPresent:["mounted","unmountSuspended"].includes(m),ref:s.useCallback(e=>{u.current=e?getComputedStyle(e):null,i(e)},[])}}(n),u="function"==typeof a?a({present:i.isPresent}):s.Children.only(a),d=useComposedRefs(i.ref,function(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,a=n&&"isReactWarning"in n&&n.isReactWarning;return a?e.ref:(a=(n=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u)),p="function"==typeof a;return p||i.isPresent?s.cloneElement(u,{ref:d}):null};function getAnimationName(e){return e?.animationName||"none"}function _radix_ui_react_slot_dist_createSlot(e){let n=function(e){let n=s.forwardRef((e,n)=>{let{children:a,...i}=e;if(s.isValidElement(a)){let e,u;let d=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.ref:(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?a.props.ref:a.props.ref||a.ref,f=function(e,n){let a={...n};for(let i in n){let s=e[i],u=n[i],d=/^on[A-Z]/.test(i);d?s&&u?a[i]=(...e)=>{let n=u(...e);return s(...e),n}:s&&(a[i]=s):"style"===i?a[i]={...s,...u}:"className"===i&&(a[i]=[s,u].filter(Boolean).join(" "))}return{...e,...a}}(i,a.props);return a.type!==s.Fragment&&(f.ref=n?composeRefs(n,d):d),s.cloneElement(a,f)}return s.Children.count(a)>1?s.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}(e),a=s.forwardRef((e,a)=>{let{children:i,...u}=e,f=s.Children.toArray(i),p=f.find(_radix_ui_react_slot_dist_isSlottable);if(p){let e=p.props.children,i=f.map(n=>n!==p?n:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,d.jsx)(n,{...u,ref:a,children:s.isValidElement(e)?s.cloneElement(e,void 0,i):null})}return(0,d.jsx)(n,{...u,ref:a,children:i})});return a.displayName=`${e}.Slot`,a}Presence.displayName="Presence";var k=Symbol("radix.slottable");function _radix_ui_react_slot_dist_isSlottable(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===k}var L=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,n)=>{let a=_radix_ui_react_slot_dist_createSlot(`Primitive.${n}`),i=s.forwardRef((e,i)=>{let{asChild:s,...u}=e,f=s?a:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(f,{...u,ref:i})});return i.displayName=`Primitive.${n}`,{...e,[n]:i}},{}),F=0;function createFocusGuard(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var W=a(2864),H=a(4348),V="Dialog",[z,K]=function(e,n=[]){let a=[],createScope=()=>{let n=a.map(e=>s.createContext(e));return function(a){let i=a?.[e]||n;return s.useMemo(()=>({[`__scope${e}`]:{...a,[e]:i}}),[a,i])}};return createScope.scopeName=e,[function(n,i){let u=s.createContext(i),f=a.length;a=[...a,i];let Provider=n=>{let{scope:a,children:i,...p}=n,h=a?.[e]?.[f]||u,m=s.useMemo(()=>p,Object.values(p));return(0,d.jsx)(h.Provider,{value:m,children:i})};return Provider.displayName=n+"Provider",[Provider,function(a,d){let p=d?.[e]?.[f]||u,h=s.useContext(p);if(h)return h;if(void 0!==i)return i;throw Error(`\`${a}\` must be used within \`${n}\``)}]},function(...e){let n=e[0];if(1===e.length)return n;let createScope=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=a.reduce((n,{useScope:a,scopeName:i})=>{let s=a(e),u=s[`__scope${i}`];return{...n,...u}},{});return s.useMemo(()=>({[`__scope${n.scopeName}`]:i}),[i])}};return createScope.scopeName=n.scopeName,createScope}(createScope,...n)]}(V),[$,Y]=z(V),Dialog=e=>{let{__scopeDialog:n,children:a,open:i,defaultOpen:u,onOpenChange:f,modal:p=!0}=e,h=s.useRef(null),g=s.useRef(null),[v,y]=function({prop:e,defaultProp:n,onChange:a=()=>{},caller:i}){let[u,d,f]=function({defaultProp:e,onChange:n}){let[a,i]=s.useState(e),u=s.useRef(a),d=s.useRef(n);return m(()=>{d.current=n},[n]),s.useEffect(()=>{u.current!==a&&(d.current?.(a),u.current=a)},[a,u]),[a,i,d]}({defaultProp:n,onChange:a}),p=void 0!==e,h=p?e:u;{let n=s.useRef(void 0!==e);s.useEffect(()=>{let e=n.current;if(e!==p){let n=p?"controlled":"uncontrolled";console.warn(`${i} is changing from ${e?"controlled":"uncontrolled"} to ${n}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}n.current=p},[p,i])}let g=s.useCallback(n=>{if(p){let a="function"==typeof n?n(e):n;a!==e&&f.current?.(a)}else d(n)},[p,e,d,f]);return[h,g]}({prop:i,defaultProp:u??!1,onChange:f,caller:V});return(0,d.jsx)($,{scope:n,triggerRef:h,contentRef:g,contentId:useId(),titleId:useId(),descriptionId:useId(),open:v,onOpenChange:y,onOpenToggle:s.useCallback(()=>y(e=>!e),[y]),modal:p,children:a})};Dialog.displayName=V;var Q="DialogTrigger",Z=s.forwardRef((e,n)=>{let{__scopeDialog:a,...i}=e,s=Y(Q,a),u=useComposedRefs(n,s.triggerRef);return(0,d.jsx)(L.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":getState(s.open),...i,ref:u,onClick:composeEventHandlers(e.onClick,s.onOpenToggle)})});Z.displayName=Q;var J="DialogPortal",[ee,et]=z(J,{forceMount:void 0}),DialogPortal=e=>{let{__scopeDialog:n,forceMount:a,children:i,container:u}=e,f=Y(J,n);return(0,d.jsx)(ee,{scope:n,forceMount:a,children:s.Children.map(i,e=>(0,d.jsx)(Presence,{present:a||f.open,children:(0,d.jsx)(D,{asChild:!0,container:u,children:e})}))})};DialogPortal.displayName=J;var er="DialogOverlay",en=s.forwardRef((e,n)=>{let a=et(er,e.__scopeDialog),{forceMount:i=a.forceMount,...s}=e,u=Y(er,e.__scopeDialog);return u.modal?(0,d.jsx)(Presence,{present:i||u.open,children:(0,d.jsx)(ea,{...s,ref:n})}):null});en.displayName=er;var eo=_radix_ui_react_slot_dist_createSlot("DialogOverlay.RemoveScroll"),ea=s.forwardRef((e,n)=>{let{__scopeDialog:a,...i}=e,s=Y(er,a);return(0,d.jsx)(W.f,{as:eo,allowPinchZoom:!0,shards:[s.contentRef],children:(0,d.jsx)(L.div,{"data-state":getState(s.open),...i,ref:n,style:{pointerEvents:"auto",...i.style}})})}),ei="DialogContent",el=s.forwardRef((e,n)=>{let a=et(ei,e.__scopeDialog),{forceMount:i=a.forceMount,...s}=e,u=Y(ei,e.__scopeDialog);return(0,d.jsx)(Presence,{present:i||u.open,children:u.modal?(0,d.jsx)(es,{...s,ref:n}):(0,d.jsx)(eu,{...s,ref:n})})});el.displayName=ei;var es=s.forwardRef((e,n)=>{let a=Y(ei,e.__scopeDialog),i=s.useRef(null),u=useComposedRefs(n,a.contentRef,i);return s.useEffect(()=>{let e=i.current;if(e)return(0,H.Ry)(e)},[]),(0,d.jsx)(ec,{...e,ref:u,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:composeEventHandlers(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:composeEventHandlers(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,a=0===n.button&&!0===n.ctrlKey,i=2===n.button||a;i&&e.preventDefault()}),onFocusOutside:composeEventHandlers(e.onFocusOutside,e=>e.preventDefault())})}),eu=s.forwardRef((e,n)=>{let a=Y(ei,e.__scopeDialog),i=s.useRef(!1),u=s.useRef(!1);return(0,d.jsx)(ec,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{e.onCloseAutoFocus?.(n),n.defaultPrevented||(i.current||a.triggerRef.current?.focus(),n.preventDefault()),i.current=!1,u.current=!1},onInteractOutside:n=>{e.onInteractOutside?.(n),n.defaultPrevented||(i.current=!0,"pointerdown"!==n.detail.originalEvent.type||(u.current=!0));let s=n.target,d=a.triggerRef.current?.contains(s);d&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&u.current&&n.preventDefault()}})}),ec=s.forwardRef((e,n)=>{let{__scopeDialog:a,trapFocus:i,onOpenAutoFocus:u,onCloseAutoFocus:f,...p}=e,h=Y(ei,a),m=s.useRef(null),g=useComposedRefs(n,m);return s.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??createFocusGuard()),document.body.insertAdjacentElement("beforeend",e[1]??createFocusGuard()),F++,()=>{1===F&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),F--}},[]),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(C,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:u,onUnmountAutoFocus:f,children:(0,d.jsx)(w,{role:"dialog",id:h.contentId,"aria-describedby":h.descriptionId,"aria-labelledby":h.titleId,"data-state":getState(h.open),...p,ref:g,onDismiss:()=>h.onOpenChange(!1)})}),(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(TitleWarning,{titleId:h.titleId}),(0,d.jsx)(DescriptionWarning,{contentRef:m,descriptionId:h.descriptionId})]})]})}),ed="DialogTitle",ef=s.forwardRef((e,n)=>{let{__scopeDialog:a,...i}=e,s=Y(ed,a);return(0,d.jsx)(L.h2,{id:s.titleId,...i,ref:n})});ef.displayName=ed;var ep="DialogDescription",eh=s.forwardRef((e,n)=>{let{__scopeDialog:a,...i}=e,s=Y(ep,a);return(0,d.jsx)(L.p,{id:s.descriptionId,...i,ref:n})});eh.displayName=ep;var em="DialogClose",eg=s.forwardRef((e,n)=>{let{__scopeDialog:a,...i}=e,s=Y(em,a);return(0,d.jsx)(L.button,{type:"button",...i,ref:n,onClick:composeEventHandlers(e.onClick,()=>s.onOpenChange(!1))})});function getState(e){return e?"open":"closed"}eg.displayName=em;var ev="DialogTitleWarning",[ey,eb]=function(e,n){let a=s.createContext(n),Provider=e=>{let{children:n,...i}=e,u=s.useMemo(()=>i,Object.values(i));return(0,d.jsx)(a.Provider,{value:u,children:n})};return Provider.displayName=e+"Provider",[Provider,function(i){let u=s.useContext(a);if(u)return u;if(void 0!==n)return n;throw Error(`\`${i}\` must be used within \`${e}\``)}]}(ev,{contentName:ei,titleName:ed,docsSlug:"dialog"}),TitleWarning=({titleId:e})=>{let n=eb(ev),a=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return s.useEffect(()=>{if(e){let n=document.getElementById(e);n||console.error(a)}},[a,e]),null},DescriptionWarning=({contentRef:e,descriptionId:n})=>{let a=eb("DialogDescriptionWarning"),i=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return s.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");if(n&&a){let e=document.getElementById(n);e||console.warn(i)}},[i,e,n]),null},e_=Dialog,ew=Z,ex=DialogPortal,eE=en,eS=el,eP=ef,eR=eh,eC=eg},6529:(e,n,a)=>{"use strict";a.d(n,{M:()=>useId});var i,s=a(9885),u=a(5852),d=(i||(i=a.t(s,2)))["useId".toString()]||(()=>void 0),f=0;function useId(e){let[n,a]=s.useState(d());return(0,u.b)(()=>{e||a(e=>e??String(f++))},[e]),e||(n?`radix-${n}`:"")}},3618:(e,n,a)=>{"use strict";a.d(n,{f:()=>f});var i=a(9885),s=a(3979),u=a(784),d=i.forwardRef((e,n)=>(0,u.jsx)(s.WV.label,{...e,ref:n,onMouseDown:n=>{let a=n.target;a.closest("button, input, select, textarea")||(e.onMouseDown?.(n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));d.displayName="Label";var f=d},9727:(e,n,a)=>{"use strict";a.d(n,{ee:()=>eu,Eh:()=>ed,VY:()=>ec,fC:()=>es,D7:()=>Y});var i=a(9885);let s=["top","right","bottom","left"],u=Math.min,d=Math.max,f=Math.round,p=Math.floor,createCoords=e=>({x:e,y:e}),h={left:"right",right:"left",bottom:"top",top:"bottom"},m={start:"end",end:"start"};function floating_ui_utils_evaluate(e,n){return"function"==typeof e?e(n):e}function floating_ui_utils_getSide(e){return e.split("-")[0]}function floating_ui_utils_getAlignment(e){return e.split("-")[1]}function getOppositeAxis(e){return"x"===e?"y":"x"}function getAxisLength(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function floating_ui_utils_getSideAxis(e){return g.has(floating_ui_utils_getSide(e))?"y":"x"}function floating_ui_utils_getOppositeAlignmentPlacement(e){return e.replace(/start|end/g,e=>m[e])}let v=["left","right"],y=["right","left"],b=["top","bottom"],_=["bottom","top"];function getOppositePlacement(e){return e.replace(/left|right|bottom|top/g,e=>h[e])}function floating_ui_utils_getPaddingObject(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function floating_ui_utils_rectToClientRect(e){let{x:n,y:a,width:i,height:s}=e;return{width:i,height:s,top:a,left:n,right:n+i,bottom:a+s,x:n,y:a}}function computeCoordsFromPlacement(e,n,a){let i,{reference:s,floating:u}=e,d=floating_ui_utils_getSideAxis(n),f=getOppositeAxis(floating_ui_utils_getSideAxis(n)),p=getAxisLength(f),h=floating_ui_utils_getSide(n),m="y"===d,g=s.x+s.width/2-u.width/2,v=s.y+s.height/2-u.height/2,y=s[p]/2-u[p]/2;switch(h){case"top":i={x:g,y:s.y-u.height};break;case"bottom":i={x:g,y:s.y+s.height};break;case"right":i={x:s.x+s.width,y:v};break;case"left":i={x:s.x-u.width,y:v};break;default:i={x:s.x,y:s.y}}switch(floating_ui_utils_getAlignment(n)){case"start":i[f]-=y*(a&&m?-1:1);break;case"end":i[f]+=y*(a&&m?-1:1)}return i}let computePosition=async(e,n,a)=>{let{placement:i="bottom",strategy:s="absolute",middleware:u=[],platform:d}=a,f=u.filter(Boolean),p=await (null==d.isRTL?void 0:d.isRTL(n)),h=await d.getElementRects({reference:e,floating:n,strategy:s}),{x:m,y:g}=computeCoordsFromPlacement(h,i,p),v=i,y={},b=0;for(let a=0;a<f.length;a++){let{name:u,fn:_}=f[a],{x:w,y:x,data:E,reset:S}=await _({x:m,y:g,initialPlacement:i,placement:v,strategy:s,middlewareData:y,rects:h,platform:d,elements:{reference:e,floating:n}});m=null!=w?w:m,g=null!=x?x:g,y={...y,[u]:{...y[u],...E}},S&&b<=50&&(b++,"object"==typeof S&&(S.placement&&(v=S.placement),S.rects&&(h=!0===S.rects?await d.getElementRects({reference:e,floating:n,strategy:s}):S.rects),{x:m,y:g}=computeCoordsFromPlacement(h,v,p)),a=-1)}return{x:m,y:g,placement:v,strategy:s,middlewareData:y}};async function detectOverflow(e,n){var a;void 0===n&&(n={});let{x:i,y:s,platform:u,rects:d,elements:f,strategy:p}=e,{boundary:h="clippingAncestors",rootBoundary:m="viewport",elementContext:g="floating",altBoundary:v=!1,padding:y=0}=floating_ui_utils_evaluate(n,e),b=floating_ui_utils_getPaddingObject(y),_=f[v?"floating"===g?"reference":"floating":g],w=floating_ui_utils_rectToClientRect(await u.getClippingRect({element:null==(a=await (null==u.isElement?void 0:u.isElement(_)))||a?_:_.contextElement||await (null==u.getDocumentElement?void 0:u.getDocumentElement(f.floating)),boundary:h,rootBoundary:m,strategy:p})),x="floating"===g?{x:i,y:s,width:d.floating.width,height:d.floating.height}:d.reference,E=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f.floating)),S=await (null==u.isElement?void 0:u.isElement(E))&&await (null==u.getScale?void 0:u.getScale(E))||{x:1,y:1},P=floating_ui_utils_rectToClientRect(u.convertOffsetParentRelativeRectToViewportRelativeRect?await u.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:x,offsetParent:E,strategy:p}):x);return{top:(w.top-P.top+b.top)/S.y,bottom:(P.bottom-w.bottom+b.bottom)/S.y,left:(w.left-P.left+b.left)/S.x,right:(P.right-w.right+b.right)/S.x}}function getSideOffsets(e,n){return{top:e.top-n.height,right:e.right-n.width,bottom:e.bottom-n.height,left:e.left-n.width}}function isAnySideFullyClipped(e){return s.some(n=>e[n]>=0)}let w=new Set(["left","top"]);async function convertValueToCoords(e,n){let{placement:a,platform:i,elements:s}=e,u=await (null==i.isRTL?void 0:i.isRTL(s.floating)),d=floating_ui_utils_getSide(a),f=floating_ui_utils_getAlignment(a),p="y"===floating_ui_utils_getSideAxis(a),h=w.has(d)?-1:1,m=u&&p?-1:1,g=floating_ui_utils_evaluate(n,e),{mainAxis:v,crossAxis:y,alignmentAxis:b}="number"==typeof g?{mainAxis:g,crossAxis:0,alignmentAxis:null}:{mainAxis:g.mainAxis||0,crossAxis:g.crossAxis||0,alignmentAxis:g.alignmentAxis};return f&&"number"==typeof b&&(y="end"===f?-1*b:b),p?{x:y*m,y:v*h}:{x:v*h,y:y*m}}function hasWindow(){return"undefined"!=typeof window}function getNodeName(e){return isNode(e)?(e.nodeName||"").toLowerCase():"#document"}function getWindow(e){var n;return(null==e||null==(n=e.ownerDocument)?void 0:n.defaultView)||window}function getDocumentElement(e){var n;return null==(n=(isNode(e)?e.ownerDocument:e.document)||window.document)?void 0:n.documentElement}function isNode(e){return!!hasWindow()&&(e instanceof Node||e instanceof getWindow(e).Node)}function isElement(e){return!!hasWindow()&&(e instanceof Element||e instanceof getWindow(e).Element)}function isHTMLElement(e){return!!hasWindow()&&(e instanceof HTMLElement||e instanceof getWindow(e).HTMLElement)}function isShadowRoot(e){return!!hasWindow()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof getWindow(e).ShadowRoot)}let x=new Set(["inline","contents"]);function isOverflowElement(e){let{overflow:n,overflowX:a,overflowY:i,display:s}=getComputedStyle(e);return/auto|scroll|overlay|hidden|clip/.test(n+i+a)&&!x.has(s)}let E=new Set(["table","td","th"]),S=[":popover-open",":modal"];function isTopLayer(e){return S.some(n=>{try{return e.matches(n)}catch(e){return!1}})}let P=["transform","translate","scale","rotate","perspective"],R=["transform","translate","scale","rotate","perspective","filter"],C=["paint","layout","strict","content"];function isContainingBlock(e){let n=isWebKit(),a=isElement(e)?getComputedStyle(e):e;return P.some(e=>!!a[e]&&"none"!==a[e])||!!a.containerType&&"normal"!==a.containerType||!n&&!!a.backdropFilter&&"none"!==a.backdropFilter||!n&&!!a.filter&&"none"!==a.filter||R.some(e=>(a.willChange||"").includes(e))||C.some(e=>(a.contain||"").includes(e))}function isWebKit(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let O=new Set(["html","body","#document"]);function isLastTraversableNode(e){return O.has(getNodeName(e))}function getComputedStyle(e){return getWindow(e).getComputedStyle(e)}function getNodeScroll(e){return isElement(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function getParentNode(e){if("html"===getNodeName(e))return e;let n=e.assignedSlot||e.parentNode||isShadowRoot(e)&&e.host||getDocumentElement(e);return isShadowRoot(n)?n.host:n}function getOverflowAncestors(e,n,a){var i;void 0===n&&(n=[]),void 0===a&&(a=!0);let s=function getNearestOverflowAncestor(e){let n=getParentNode(e);return isLastTraversableNode(n)?e.ownerDocument?e.ownerDocument.body:e.body:isHTMLElement(n)&&isOverflowElement(n)?n:getNearestOverflowAncestor(n)}(e),u=s===(null==(i=e.ownerDocument)?void 0:i.body),d=getWindow(s);if(u){let e=getFrameElement(d);return n.concat(d,d.visualViewport||[],isOverflowElement(s)?s:[],e&&a?getOverflowAncestors(e):[])}return n.concat(s,getOverflowAncestors(s,[],a))}function getFrameElement(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function getCssDimensions(e){let n=getComputedStyle(e),a=parseFloat(n.width)||0,i=parseFloat(n.height)||0,s=isHTMLElement(e),u=s?e.offsetWidth:a,d=s?e.offsetHeight:i,p=f(a)!==u||f(i)!==d;return p&&(a=u,i=d),{width:a,height:i,$:p}}function unwrapElement(e){return isElement(e)?e:e.contextElement}function getScale(e){let n=unwrapElement(e);if(!isHTMLElement(n))return createCoords(1);let a=n.getBoundingClientRect(),{width:i,height:s,$:u}=getCssDimensions(n),d=(u?f(a.width):a.width)/i,p=(u?f(a.height):a.height)/s;return d&&Number.isFinite(d)||(d=1),p&&Number.isFinite(p)||(p=1),{x:d,y:p}}let j=createCoords(0);function getVisualOffsets(e){let n=getWindow(e);return isWebKit()&&n.visualViewport?{x:n.visualViewport.offsetLeft,y:n.visualViewport.offsetTop}:j}function getBoundingClientRect(e,n,a,i){var s;void 0===n&&(n=!1),void 0===a&&(a=!1);let u=e.getBoundingClientRect(),d=unwrapElement(e),f=createCoords(1);n&&(i?isElement(i)&&(f=getScale(i)):f=getScale(e));let p=(void 0===(s=a)&&(s=!1),i&&(!s||i===getWindow(d))&&s)?getVisualOffsets(d):createCoords(0),h=(u.left+p.x)/f.x,m=(u.top+p.y)/f.y,g=u.width/f.x,v=u.height/f.y;if(d){let e=getWindow(d),n=i&&isElement(i)?getWindow(i):i,a=e,s=getFrameElement(a);for(;s&&i&&n!==a;){let e=getScale(s),n=s.getBoundingClientRect(),i=getComputedStyle(s),u=n.left+(s.clientLeft+parseFloat(i.paddingLeft))*e.x,d=n.top+(s.clientTop+parseFloat(i.paddingTop))*e.y;h*=e.x,m*=e.y,g*=e.x,v*=e.y,h+=u,m+=d,s=getFrameElement(a=getWindow(s))}}return floating_ui_utils_rectToClientRect({width:g,height:v,x:h,y:m})}function getWindowScrollBarX(e,n){let a=getNodeScroll(e).scrollLeft;return n?n.left+a:getBoundingClientRect(getDocumentElement(e)).left+a}function getHTMLOffset(e,n,a){void 0===a&&(a=!1);let i=e.getBoundingClientRect(),s=i.left+n.scrollLeft-(a?0:getWindowScrollBarX(e,i)),u=i.top+n.scrollTop;return{x:s,y:u}}let M=new Set(["absolute","fixed"]);function getClientRectFromClippingAncestor(e,n,a){let i;if("viewport"===n)i=function(e,n){let a=getWindow(e),i=getDocumentElement(e),s=a.visualViewport,u=i.clientWidth,d=i.clientHeight,f=0,p=0;if(s){u=s.width,d=s.height;let e=isWebKit();(!e||e&&"fixed"===n)&&(f=s.offsetLeft,p=s.offsetTop)}return{width:u,height:d,x:f,y:p}}(e,a);else if("document"===n)i=function(e){let n=getDocumentElement(e),a=getNodeScroll(e),i=e.ownerDocument.body,s=d(n.scrollWidth,n.clientWidth,i.scrollWidth,i.clientWidth),u=d(n.scrollHeight,n.clientHeight,i.scrollHeight,i.clientHeight),f=-a.scrollLeft+getWindowScrollBarX(e),p=-a.scrollTop;return"rtl"===getComputedStyle(i).direction&&(f+=d(n.clientWidth,i.clientWidth)-s),{width:s,height:u,x:f,y:p}}(getDocumentElement(e));else if(isElement(n))i=function(e,n){let a=getBoundingClientRect(e,!0,"fixed"===n),i=a.top+e.clientTop,s=a.left+e.clientLeft,u=isHTMLElement(e)?getScale(e):createCoords(1),d=e.clientWidth*u.x,f=e.clientHeight*u.y,p=s*u.x,h=i*u.y;return{width:d,height:f,x:p,y:h}}(n,a);else{let a=getVisualOffsets(e);i={x:n.x-a.x,y:n.y-a.y,width:n.width,height:n.height}}return floating_ui_utils_rectToClientRect(i)}function isStaticPositioned(e){return"static"===getComputedStyle(e).position}function getTrueOffsetParent(e,n){if(!isHTMLElement(e)||"fixed"===getComputedStyle(e).position)return null;if(n)return n(e);let a=e.offsetParent;return getDocumentElement(e)===a&&(a=a.ownerDocument.body),a}function getOffsetParent(e,n){var a;let i=getWindow(e);if(isTopLayer(e))return i;if(!isHTMLElement(e)){let n=getParentNode(e);for(;n&&!isLastTraversableNode(n);){if(isElement(n)&&!isStaticPositioned(n))return n;n=getParentNode(n)}return i}let s=getTrueOffsetParent(e,n);for(;s&&(a=s,E.has(getNodeName(a)))&&isStaticPositioned(s);)s=getTrueOffsetParent(s,n);return s&&isLastTraversableNode(s)&&isStaticPositioned(s)&&!isContainingBlock(s)?i:s||function(e){let n=getParentNode(e);for(;isHTMLElement(n)&&!isLastTraversableNode(n);){if(isContainingBlock(n))return n;if(isTopLayer(n))break;n=getParentNode(n)}return null}(e)||i}let getElementRects=async function(e){let n=this.getOffsetParent||getOffsetParent,a=this.getDimensions,i=await a(e.floating);return{reference:function(e,n,a){let i=isHTMLElement(n),s=getDocumentElement(n),u="fixed"===a,d=getBoundingClientRect(e,!0,u,n),f={scrollLeft:0,scrollTop:0},p=createCoords(0);if(i||!i&&!u){if(("body"!==getNodeName(n)||isOverflowElement(s))&&(f=getNodeScroll(n)),i){let e=getBoundingClientRect(n,!0,u,n);p.x=e.x+n.clientLeft,p.y=e.y+n.clientTop}else s&&(p.x=getWindowScrollBarX(s))}u&&!i&&s&&(p.x=getWindowScrollBarX(s));let h=!s||i||u?createCoords(0):getHTMLOffset(s,f),m=d.left+f.scrollLeft-p.x-h.x,g=d.top+f.scrollTop-p.y-h.y;return{x:m,y:g,width:d.width,height:d.height}}(e.reference,await n(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}},N={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:n,rect:a,offsetParent:i,strategy:s}=e,u="fixed"===s,d=getDocumentElement(i),f=!!n&&isTopLayer(n.floating);if(i===d||f&&u)return a;let p={scrollLeft:0,scrollTop:0},h=createCoords(1),m=createCoords(0),g=isHTMLElement(i);if((g||!g&&!u)&&(("body"!==getNodeName(i)||isOverflowElement(d))&&(p=getNodeScroll(i)),isHTMLElement(i))){let e=getBoundingClientRect(i);h=getScale(i),m.x=e.x+i.clientLeft,m.y=e.y+i.clientTop}let v=!d||g||u?createCoords(0):getHTMLOffset(d,p,!0);return{width:a.width*h.x,height:a.height*h.y,x:a.x*h.x-p.scrollLeft*h.x+m.x+v.x,y:a.y*h.y-p.scrollTop*h.y+m.y+v.y}},getDocumentElement:getDocumentElement,getClippingRect:function(e){let{element:n,boundary:a,rootBoundary:i,strategy:s}=e,f="clippingAncestors"===a?isTopLayer(n)?[]:function(e,n){let a=n.get(e);if(a)return a;let i=getOverflowAncestors(e,[],!1).filter(e=>isElement(e)&&"body"!==getNodeName(e)),s=null,u="fixed"===getComputedStyle(e).position,d=u?getParentNode(e):e;for(;isElement(d)&&!isLastTraversableNode(d);){let n=getComputedStyle(d),a=isContainingBlock(d);a||"fixed"!==n.position||(s=null);let f=u?!a&&!s:!a&&"static"===n.position&&!!s&&M.has(s.position)||isOverflowElement(d)&&!a&&function hasFixedPositionAncestor(e,n){let a=getParentNode(e);return!(a===n||!isElement(a)||isLastTraversableNode(a))&&("fixed"===getComputedStyle(a).position||hasFixedPositionAncestor(a,n))}(e,d);f?i=i.filter(e=>e!==d):s=n,d=getParentNode(d)}return n.set(e,i),i}(n,this._c):[].concat(a),p=[...f,i],h=p[0],m=p.reduce((e,a)=>{let i=getClientRectFromClippingAncestor(n,a,s);return e.top=d(i.top,e.top),e.right=u(i.right,e.right),e.bottom=u(i.bottom,e.bottom),e.left=d(i.left,e.left),e},getClientRectFromClippingAncestor(n,h,s));return{width:m.right-m.left,height:m.bottom-m.top,x:m.left,y:m.top}},getOffsetParent,getElementRects,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:n,height:a}=getCssDimensions(e);return{width:n,height:a}},getScale,isElement:isElement,isRTL:function(e){return"rtl"===getComputedStyle(e).direction}};function rectsAreEqual(e,n){return e.x===n.x&&e.y===n.y&&e.width===n.width&&e.height===n.height}let floating_ui_dom_arrow=e=>({name:"arrow",options:e,async fn(n){let{x:a,y:i,placement:s,rects:f,platform:p,elements:h,middlewareData:m}=n,{element:g,padding:v=0}=floating_ui_utils_evaluate(e,n)||{};if(null==g)return{};let y=floating_ui_utils_getPaddingObject(v),b={x:a,y:i},_=getOppositeAxis(floating_ui_utils_getSideAxis(s)),w=getAxisLength(_),x=await p.getDimensions(g),E="y"===_,S=E?"clientHeight":"clientWidth",P=f.reference[w]+f.reference[_]-b[_]-f.floating[w],R=b[_]-f.reference[_],C=await (null==p.getOffsetParent?void 0:p.getOffsetParent(g)),O=C?C[S]:0;O&&await (null==p.isElement?void 0:p.isElement(C))||(O=h.floating[S]||f.floating[w]);let j=O/2-x[w]/2-1,M=u(y[E?"top":"left"],j),N=u(y[E?"bottom":"right"],j),D=O-x[w]-N,k=O/2-x[w]/2+(P/2-R/2),L=d(M,u(k,D)),F=!m.arrow&&null!=floating_ui_utils_getAlignment(s)&&k!==L&&f.reference[w]/2-(k<M?M:N)-x[w]/2<0,W=F?k<M?k-M:k-D:0;return{[_]:b[_]+W,data:{[_]:L,centerOffset:k-L-W,...F&&{alignmentOffset:W}},reset:F}}}),floating_ui_dom_computePosition=(e,n,a)=>{let i=new Map,s={platform:N,...a},u={...s.platform,_c:i};return computePosition(e,n,{...s,platform:u})};var D=a(8908),k="undefined"!=typeof document?i.useLayoutEffect:function(){};function deepEqual(e,n){let a,i,s;if(e===n)return!0;if(typeof e!=typeof n)return!1;if("function"==typeof e&&e.toString()===n.toString())return!0;if(e&&n&&"object"==typeof e){if(Array.isArray(e)){if((a=e.length)!==n.length)return!1;for(i=a;0!=i--;)if(!deepEqual(e[i],n[i]))return!1;return!0}if((a=(s=Object.keys(e)).length)!==Object.keys(n).length)return!1;for(i=a;0!=i--;)if(!({}).hasOwnProperty.call(n,s[i]))return!1;for(i=a;0!=i--;){let a=s[i];if(("_owner"!==a||!e.$$typeof)&&!deepEqual(e[a],n[a]))return!1}return!0}return e!=e&&n!=n}function getDPR(e){if("undefined"==typeof window)return 1;let n=e.ownerDocument.defaultView||window;return n.devicePixelRatio||1}function roundByDPR(e,n){let a=getDPR(e);return Math.round(n*a)/a}function useLatestRef(e){let n=i.useRef(e);return k(()=>{n.current=e}),n}let arrow$1=e=>({name:"arrow",options:e,fn(n){let{element:a,padding:i}="function"==typeof e?e(n):e;return a&&({}).hasOwnProperty.call(a,"current")?null!=a.current?floating_ui_dom_arrow({element:a.current,padding:i}).fn(n):{}:a?floating_ui_dom_arrow({element:a,padding:i}).fn(n):{}}}),floating_ui_react_dom_offset=(e,n)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(n){var a,i;let{x:s,y:u,placement:d,middlewareData:f}=n,p=await convertValueToCoords(n,e);return d===(null==(a=f.offset)?void 0:a.placement)&&null!=(i=f.arrow)&&i.alignmentOffset?{}:{x:s+p.x,y:u+p.y,data:{...p,placement:d}}}}}(e),options:[e,n]}),floating_ui_react_dom_shift=(e,n)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(n){let{x:a,y:i,placement:s}=n,{mainAxis:f=!0,crossAxis:p=!1,limiter:h={fn:e=>{let{x:n,y:a}=e;return{x:n,y:a}}},...m}=floating_ui_utils_evaluate(e,n),g={x:a,y:i},v=await detectOverflow(n,m),y=floating_ui_utils_getSideAxis(floating_ui_utils_getSide(s)),b=getOppositeAxis(y),_=g[b],w=g[y];if(f){let e=_+v["y"===b?"top":"left"],n=_-v["y"===b?"bottom":"right"];_=d(e,u(_,n))}if(p){let e="y"===y?"top":"left",n="y"===y?"bottom":"right",a=w+v[e],i=w-v[n];w=d(a,u(w,i))}let x=h.fn({...n,[b]:_,[y]:w});return{...x,data:{x:x.x-a,y:x.y-i,enabled:{[b]:f,[y]:p}}}}}}(e),options:[e,n]}),floating_ui_react_dom_limitShift=(e,n)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(n){let{x:a,y:i,placement:s,rects:u,middlewareData:d}=n,{offset:f=0,mainAxis:p=!0,crossAxis:h=!0}=floating_ui_utils_evaluate(e,n),m={x:a,y:i},g=floating_ui_utils_getSideAxis(s),v=getOppositeAxis(g),y=m[v],b=m[g],_=floating_ui_utils_evaluate(f,n),x="number"==typeof _?{mainAxis:_,crossAxis:0}:{mainAxis:0,crossAxis:0,..._};if(p){let e="y"===v?"height":"width",n=u.reference[v]-u.floating[e]+x.mainAxis,a=u.reference[v]+u.reference[e]-x.mainAxis;y<n?y=n:y>a&&(y=a)}if(h){var E,S;let e="y"===v?"width":"height",n=w.has(floating_ui_utils_getSide(s)),a=u.reference[g]-u.floating[e]+(n&&(null==(E=d.offset)?void 0:E[g])||0)+(n?0:x.crossAxis),i=u.reference[g]+u.reference[e]+(n?0:(null==(S=d.offset)?void 0:S[g])||0)-(n?x.crossAxis:0);b<a?b=a:b>i&&(b=i)}return{[v]:y,[g]:b}}}}(e),options:[e,n]}),floating_ui_react_dom_flip=(e,n)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(n){var a,i,s,u,d;let{placement:f,middlewareData:p,rects:h,initialPlacement:m,platform:g,elements:w}=n,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:S,fallbackStrategy:P="bestFit",fallbackAxisSideDirection:R="none",flipAlignment:C=!0,...O}=floating_ui_utils_evaluate(e,n);if(null!=(a=p.arrow)&&a.alignmentOffset)return{};let j=floating_ui_utils_getSide(f),M=floating_ui_utils_getSideAxis(m),N=floating_ui_utils_getSide(m)===m,D=await (null==g.isRTL?void 0:g.isRTL(w.floating)),k=S||(N||!C?[getOppositePlacement(m)]:function(e){let n=getOppositePlacement(e);return[floating_ui_utils_getOppositeAlignmentPlacement(e),n,floating_ui_utils_getOppositeAlignmentPlacement(n)]}(m)),L="none"!==R;!S&&L&&k.push(...function(e,n,a,i){let s=floating_ui_utils_getAlignment(e),u=function(e,n,a){switch(e){case"top":case"bottom":if(a)return n?y:v;return n?v:y;case"left":case"right":return n?b:_;default:return[]}}(floating_ui_utils_getSide(e),"start"===a,i);return s&&(u=u.map(e=>e+"-"+s),n&&(u=u.concat(u.map(floating_ui_utils_getOppositeAlignmentPlacement)))),u}(m,C,R,D));let F=[m,...k],W=await detectOverflow(n,O),H=[],V=(null==(i=p.flip)?void 0:i.overflows)||[];if(x&&H.push(W[j]),E){let e=function(e,n,a){void 0===a&&(a=!1);let i=floating_ui_utils_getAlignment(e),s=getOppositeAxis(floating_ui_utils_getSideAxis(e)),u=getAxisLength(s),d="x"===s?i===(a?"end":"start")?"right":"left":"start"===i?"bottom":"top";return n.reference[u]>n.floating[u]&&(d=getOppositePlacement(d)),[d,getOppositePlacement(d)]}(f,h,D);H.push(W[e[0]],W[e[1]])}if(V=[...V,{placement:f,overflows:H}],!H.every(e=>e<=0)){let e=((null==(s=p.flip)?void 0:s.index)||0)+1,n=F[e];if(n){let a="alignment"===E&&M!==floating_ui_utils_getSideAxis(n);if(!a||V.every(e=>e.overflows[0]>0&&floating_ui_utils_getSideAxis(e.placement)===M))return{data:{index:e,overflows:V},reset:{placement:n}}}let a=null==(u=V.filter(e=>e.overflows[0]<=0).sort((e,n)=>e.overflows[1]-n.overflows[1])[0])?void 0:u.placement;if(!a)switch(P){case"bestFit":{let e=null==(d=V.filter(e=>{if(L){let n=floating_ui_utils_getSideAxis(e.placement);return n===M||"y"===n}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,n)=>e+n,0)]).sort((e,n)=>e[1]-n[1])[0])?void 0:d[0];e&&(a=e);break}case"initialPlacement":a=m}if(f!==a)return{reset:{placement:a}}}return{}}}}(e),options:[e,n]}),floating_ui_react_dom_size=(e,n)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(n){var a,i;let s,f;let{placement:p,rects:h,platform:m,elements:g}=n,{apply:v=()=>{},...y}=floating_ui_utils_evaluate(e,n),b=await detectOverflow(n,y),_=floating_ui_utils_getSide(p),w=floating_ui_utils_getAlignment(p),x="y"===floating_ui_utils_getSideAxis(p),{width:E,height:S}=h.floating;"top"===_||"bottom"===_?(s=_,f=w===(await (null==m.isRTL?void 0:m.isRTL(g.floating))?"start":"end")?"left":"right"):(f=_,s="end"===w?"top":"bottom");let P=S-b.top-b.bottom,R=E-b.left-b.right,C=u(S-b[s],P),O=u(E-b[f],R),j=!n.middlewareData.shift,M=C,N=O;if(null!=(a=n.middlewareData.shift)&&a.enabled.x&&(N=R),null!=(i=n.middlewareData.shift)&&i.enabled.y&&(M=P),j&&!w){let e=d(b.left,0),n=d(b.right,0),a=d(b.top,0),i=d(b.bottom,0);x?N=E-2*(0!==e||0!==n?e+n:d(b.left,b.right)):M=S-2*(0!==a||0!==i?a+i:d(b.top,b.bottom))}await v({...n,availableWidth:N,availableHeight:M});let D=await m.getDimensions(g.floating);return E!==D.width||S!==D.height?{reset:{rects:!0}}:{}}}}(e),options:[e,n]}),floating_ui_react_dom_hide=(e,n)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(n){let{rects:a}=n,{strategy:i="referenceHidden",...s}=floating_ui_utils_evaluate(e,n);switch(i){case"referenceHidden":{let e=await detectOverflow(n,{...s,elementContext:"reference"}),i=getSideOffsets(e,a.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:isAnySideFullyClipped(i)}}}case"escaped":{let e=await detectOverflow(n,{...s,altBoundary:!0}),i=getSideOffsets(e,a.floating);return{data:{escapedOffsets:i,escaped:isAnySideFullyClipped(i)}}}default:return{}}}}}(e),options:[e,n]}),floating_ui_react_dom_arrow=(e,n)=>({...arrow$1(e),options:[e,n]});var L=a(3979),F=a(784),W=i.forwardRef((e,n)=>{let{children:a,width:i=10,height:s=5,...u}=e;return(0,F.jsx)(L.WV.svg,{...u,ref:n,width:i,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?a:(0,F.jsx)("polygon",{points:"0,0 30,0 15,10"})})});W.displayName="Arrow";var H=a(880),V=a(2285),z=a(5852),K="Popper",[$,Y]=function(e,n=[]){let a=[],createScope=()=>{let n=a.map(e=>i.createContext(e));return function(a){let s=a?.[e]||n;return i.useMemo(()=>({[`__scope${e}`]:{...a,[e]:s}}),[a,s])}};return createScope.scopeName=e,[function(n,s){let u=i.createContext(s),d=a.length;function Provider(n){let{scope:a,children:s,...f}=n,p=a?.[e][d]||u,h=i.useMemo(()=>f,Object.values(f));return(0,F.jsx)(p.Provider,{value:h,children:s})}return a=[...a,s],Provider.displayName=n+"Provider",[Provider,function(a,f){let p=f?.[e][d]||u,h=i.useContext(p);if(h)return h;if(void 0!==s)return s;throw Error(`\`${a}\` must be used within \`${n}\``)}]},function(...e){let n=e[0];if(1===e.length)return n;let createScope=()=>{let a=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=a.reduce((n,{useScope:a,scopeName:i})=>{let s=a(e),u=s[`__scope${i}`];return{...n,...u}},{});return i.useMemo(()=>({[`__scope${n.scopeName}`]:s}),[s])}};return createScope.scopeName=n.scopeName,createScope}(createScope,...n)]}(K),[Q,Z]=$(K),Popper=e=>{let{__scopePopper:n,children:a}=e,[s,u]=i.useState(null);return(0,F.jsx)(Q,{scope:n,anchor:s,onAnchorChange:u,children:a})};Popper.displayName=K;var J="PopperAnchor",ee=i.forwardRef((e,n)=>{let{__scopePopper:a,virtualRef:s,...u}=e,d=Z(J,a),f=i.useRef(null),p=(0,H.e)(n,f);return i.useEffect(()=>{d.onAnchorChange(s?.current||f.current)}),s?null:(0,F.jsx)(L.WV.div,{...u,ref:p})});ee.displayName=J;var et="PopperContent",[er,en]=$(et),eo=i.forwardRef((e,n)=>{let{__scopePopper:a,side:s="bottom",sideOffset:f=0,align:h="center",alignOffset:m=0,arrowPadding:g=0,avoidCollisions:v=!0,collisionBoundary:y=[],collisionPadding:b=0,sticky:_="partial",hideWhenDetached:w=!1,updatePositionStrategy:x="optimized",onPlaced:E,...S}=e,P=Z(et,a),[R,C]=i.useState(null),O=(0,H.e)(n,e=>C(e)),[j,M]=i.useState(null),N=function(e){let[n,a]=i.useState(void 0);return(0,z.b)(()=>{if(e){a({width:e.offsetWidth,height:e.offsetHeight});let n=new ResizeObserver(n=>{let i,s;if(!Array.isArray(n)||!n.length)return;let u=n[0];if("borderBoxSize"in u){let e=u.borderBoxSize,n=Array.isArray(e)?e[0]:e;i=n.inlineSize,s=n.blockSize}else i=e.offsetWidth,s=e.offsetHeight;a({width:i,height:s})});return n.observe(e,{box:"border-box"}),()=>n.unobserve(e)}a(void 0)},[e]),n}(j),W=N?.width??0,K=N?.height??0,$="number"==typeof b?b:{top:0,right:0,bottom:0,left:0,...b},Y=Array.isArray(y)?y:[y],Q=Y.length>0,J={padding:$,boundary:Y.filter(isNotNull),altBoundary:Q},{refs:ee,floatingStyles:en,placement:eo,isPositioned:ea,middlewareData:ei}=function(e){void 0===e&&(e={});let{placement:n="bottom",strategy:a="absolute",middleware:s=[],platform:u,elements:{reference:d,floating:f}={},transform:p=!0,whileElementsMounted:h,open:m}=e,[g,v]=i.useState({x:0,y:0,strategy:a,placement:n,middlewareData:{},isPositioned:!1}),[y,b]=i.useState(s);deepEqual(y,s)||b(s);let[_,w]=i.useState(null),[x,E]=i.useState(null),S=i.useCallback(e=>{e!==O.current&&(O.current=e,w(e))},[]),P=i.useCallback(e=>{e!==j.current&&(j.current=e,E(e))},[]),R=d||_,C=f||x,O=i.useRef(null),j=i.useRef(null),M=i.useRef(g),N=null!=h,L=useLatestRef(h),F=useLatestRef(u),W=useLatestRef(m),H=i.useCallback(()=>{if(!O.current||!j.current)return;let e={placement:n,strategy:a,middleware:y};F.current&&(e.platform=F.current),floating_ui_dom_computePosition(O.current,j.current,e).then(e=>{let n={...e,isPositioned:!1!==W.current};V.current&&!deepEqual(M.current,n)&&(M.current=n,D.flushSync(()=>{v(n)}))})},[y,n,a,F,W]);k(()=>{!1===m&&M.current.isPositioned&&(M.current.isPositioned=!1,v(e=>({...e,isPositioned:!1})))},[m]);let V=i.useRef(!1);k(()=>(V.current=!0,()=>{V.current=!1}),[]),k(()=>{if(R&&(O.current=R),C&&(j.current=C),R&&C){if(L.current)return L.current(R,C,H);H()}},[R,C,H,L,N]);let z=i.useMemo(()=>({reference:O,floating:j,setReference:S,setFloating:P}),[S,P]),K=i.useMemo(()=>({reference:R,floating:C}),[R,C]),$=i.useMemo(()=>{let e={position:a,left:0,top:0};if(!K.floating)return e;let n=roundByDPR(K.floating,g.x),i=roundByDPR(K.floating,g.y);return p?{...e,transform:"translate("+n+"px, "+i+"px)",...getDPR(K.floating)>=1.5&&{willChange:"transform"}}:{position:a,left:n,top:i}},[a,p,K.floating,g.x,g.y]);return i.useMemo(()=>({...g,update:H,refs:z,elements:K,floatingStyles:$}),[g,H,z,K,$])}({strategy:"fixed",placement:s+("center"!==h?"-"+h:""),whileElementsMounted:(...e)=>{let n=function(e,n,a,i){let s;void 0===i&&(i={});let{ancestorScroll:f=!0,ancestorResize:h=!0,elementResize:m="function"==typeof ResizeObserver,layoutShift:g="function"==typeof IntersectionObserver,animationFrame:v=!1}=i,y=unwrapElement(e),b=f||h?[...y?getOverflowAncestors(y):[],...getOverflowAncestors(n)]:[];b.forEach(e=>{f&&e.addEventListener("scroll",a,{passive:!0}),h&&e.addEventListener("resize",a)});let _=y&&g?function(e,n){let a,i=null,s=getDocumentElement(e);function cleanup(){var e;clearTimeout(a),null==(e=i)||e.disconnect(),i=null}return function refresh(f,h){void 0===f&&(f=!1),void 0===h&&(h=1),cleanup();let m=e.getBoundingClientRect(),{left:g,top:v,width:y,height:b}=m;if(f||n(),!y||!b)return;let _=p(v),w=p(s.clientWidth-(g+y)),x=p(s.clientHeight-(v+b)),E=p(g),S={rootMargin:-_+"px "+-w+"px "+-x+"px "+-E+"px",threshold:d(0,u(1,h))||1},P=!0;function handleObserve(n){let i=n[0].intersectionRatio;if(i!==h){if(!P)return refresh();i?refresh(!1,i):a=setTimeout(()=>{refresh(!1,1e-7)},1e3)}1!==i||rectsAreEqual(m,e.getBoundingClientRect())||refresh(),P=!1}try{i=new IntersectionObserver(handleObserve,{...S,root:s.ownerDocument})}catch(e){i=new IntersectionObserver(handleObserve,S)}i.observe(e)}(!0),cleanup}(y,a):null,w=-1,x=null;m&&(x=new ResizeObserver(e=>{let[i]=e;i&&i.target===y&&x&&(x.unobserve(n),cancelAnimationFrame(w),w=requestAnimationFrame(()=>{var e;null==(e=x)||e.observe(n)})),a()}),y&&!v&&x.observe(y),x.observe(n));let E=v?getBoundingClientRect(e):null;return v&&function frameLoop(){let n=getBoundingClientRect(e);E&&!rectsAreEqual(E,n)&&a(),E=n,s=requestAnimationFrame(frameLoop)}(),a(),()=>{var e;b.forEach(e=>{f&&e.removeEventListener("scroll",a),h&&e.removeEventListener("resize",a)}),null==_||_(),null==(e=x)||e.disconnect(),x=null,v&&cancelAnimationFrame(s)}}(...e,{animationFrame:"always"===x});return n},elements:{reference:P.anchor},middleware:[floating_ui_react_dom_offset({mainAxis:f+K,alignmentAxis:m}),v&&floating_ui_react_dom_shift({mainAxis:!0,crossAxis:!1,limiter:"partial"===_?floating_ui_react_dom_limitShift():void 0,...J}),v&&floating_ui_react_dom_flip({...J}),floating_ui_react_dom_size({...J,apply:({elements:e,rects:n,availableWidth:a,availableHeight:i})=>{let{width:s,height:u}=n.reference,d=e.floating.style;d.setProperty("--radix-popper-available-width",`${a}px`),d.setProperty("--radix-popper-available-height",`${i}px`),d.setProperty("--radix-popper-anchor-width",`${s}px`),d.setProperty("--radix-popper-anchor-height",`${u}px`)}}),j&&floating_ui_react_dom_arrow({element:j,padding:g}),transformOrigin({arrowWidth:W,arrowHeight:K}),w&&floating_ui_react_dom_hide({strategy:"referenceHidden",...J})]}),[el,es]=getSideAndAlignFromPlacement(eo),eu=(0,V.W)(E);(0,z.b)(()=>{ea&&eu?.()},[ea,eu]);let ec=ei.arrow?.x,ed=ei.arrow?.y,ef=ei.arrow?.centerOffset!==0,[ep,eh]=i.useState();return(0,z.b)(()=>{R&&eh(window.getComputedStyle(R).zIndex)},[R]),(0,F.jsx)("div",{ref:ee.setFloating,"data-radix-popper-content-wrapper":"",style:{...en,transform:ea?en.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ep,"--radix-popper-transform-origin":[ei.transformOrigin?.x,ei.transformOrigin?.y].join(" "),...ei.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,F.jsx)(er,{scope:a,placedSide:el,onArrowChange:M,arrowX:ec,arrowY:ed,shouldHideArrow:ef,children:(0,F.jsx)(L.WV.div,{"data-side":el,"data-align":es,...S,ref:O,style:{...S.style,animation:ea?void 0:"none"}})})})});eo.displayName=et;var ea="PopperArrow",ei={top:"bottom",right:"left",bottom:"top",left:"right"},el=i.forwardRef(function(e,n){let{__scopePopper:a,...i}=e,s=en(ea,a),u=ei[s.placedSide];return(0,F.jsx)("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[u]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:(0,F.jsx)(W,{...i,ref:n,style:{...i.style,display:"block"}})})});function isNotNull(e){return null!==e}el.displayName=ea;var transformOrigin=e=>({name:"transformOrigin",options:e,fn(n){let{placement:a,rects:i,middlewareData:s}=n,u=s.arrow?.centerOffset!==0,d=u?0:e.arrowWidth,f=u?0:e.arrowHeight,[p,h]=getSideAndAlignFromPlacement(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(s.arrow?.x??0)+d/2,v=(s.arrow?.y??0)+f/2,y="",b="";return"bottom"===p?(y=u?m:`${g}px`,b=`${-f}px`):"top"===p?(y=u?m:`${g}px`,b=`${i.floating.height+f}px`):"right"===p?(y=`${-f}px`,b=u?m:`${v}px`):"left"===p&&(y=`${i.floating.width+f}px`,b=u?m:`${v}px`),{data:{x:y,y:b}}}});function getSideAndAlignFromPlacement(e){let[n,a="center"]=e.split("-");return[n,a]}var es=Popper,eu=ee,ec=eo,ed=el},9752:(e,n,a)=>{"use strict";a.d(n,{z:()=>Presence});var i=a(9885),s=a(880),u=a(5852),Presence=e=>{let{present:n,children:a}=e,d=function(e){var n;let[a,s]=i.useState(),d=i.useRef({}),f=i.useRef(e),p=i.useRef("none"),h=e?"mounted":"unmounted",[m,g]=(n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,a)=>{let i=n[e][a];return i??e},h));return i.useEffect(()=>{let e=getAnimationName(d.current);p.current="mounted"===m?e:"none"},[m]),(0,u.b)(()=>{let n=d.current,a=f.current,i=a!==e;if(i){let i=p.current,s=getAnimationName(n);e?g("MOUNT"):"none"===s||n?.display==="none"?g("UNMOUNT"):a&&i!==s?g("ANIMATION_OUT"):g("UNMOUNT"),f.current=e}},[e,g]),(0,u.b)(()=>{if(a){let e;let n=a.ownerDocument.defaultView??window,handleAnimationEnd=i=>{let s=getAnimationName(d.current),u=s.includes(i.animationName);if(i.target===a&&u&&(g("ANIMATION_END"),!f.current)){let i=a.style.animationFillMode;a.style.animationFillMode="forwards",e=n.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=i)})}},handleAnimationStart=e=>{e.target===a&&(p.current=getAnimationName(d.current))};return a.addEventListener("animationstart",handleAnimationStart),a.addEventListener("animationcancel",handleAnimationEnd),a.addEventListener("animationend",handleAnimationEnd),()=>{n.clearTimeout(e),a.removeEventListener("animationstart",handleAnimationStart),a.removeEventListener("animationcancel",handleAnimationEnd),a.removeEventListener("animationend",handleAnimationEnd)}}g("ANIMATION_END")},[a,g]),{isPresent:["mounted","unmountSuspended"].includes(m),ref:i.useCallback(e=>{e&&(d.current=getComputedStyle(e)),s(e)},[])}}(n),f="function"==typeof a?a({present:d.isPresent}):i.Children.only(a),p=(0,s.e)(d.ref,function(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,a=n&&"isReactWarning"in n&&n.isReactWarning;return a?e.ref:(a=(n=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(f)),h="function"==typeof a;return h||d.isPresent?i.cloneElement(f,{ref:p}):null};function getAnimationName(e){return e?.animationName||"none"}Presence.displayName="Presence"},3979:(e,n,a)=>{"use strict";a.d(n,{WV:()=>f,jH:()=>dispatchDiscreteCustomEvent});var i=a(9885),s=a(8908),u=a(1085),d=a(784),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,n)=>{let a=i.forwardRef((e,a)=>{let{asChild:i,...s}=e,f=i?u.g7:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,d.jsx)(f,{...s,ref:a})});return a.displayName=`Primitive.${n}`,{...e,[n]:a}},{});function dispatchDiscreteCustomEvent(e,n){e&&s.flushSync(()=>e.dispatchEvent(n))}},5985:(e,n,a)=>{"use strict";a.d(n,{VY:()=>e$,ZA:()=>eY,JO:()=>eK,ck:()=>eQ,wU:()=>eJ,eT:()=>eZ,__:()=>eX,h_:()=>eG,fC:()=>eH,$G:()=>e1,u_:()=>e0,Z0:()=>e2,xz:()=>eV,B4:()=>ez,l_:()=>eq});var i,s=a(9885),u=a(8908);function clamp(e,[n,a]){return Math.min(a,Math.max(n,e))}var d=a(5418),f=a(5537),p=a(880),h=a(8718),m=a(784),g=s.createContext(void 0),v=a(3979),y=a(2285),b="dismissableLayer.update",_=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),w=s.forwardRef((e,n)=>{let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:g,onDismiss:w,...x}=e,E=s.useContext(_),[S,P]=s.useState(null),R=S?.ownerDocument??globalThis?.document,[,C]=s.useState({}),O=(0,p.e)(n,e=>P(e)),j=Array.from(E.layers),[M]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),N=j.indexOf(M),D=S?j.indexOf(S):-1,k=E.layersWithOutsidePointerEventsDisabled.size>0,L=D>=N,F=function(e,n=globalThis?.document){let a=(0,y.W)(e),i=s.useRef(!1),u=s.useRef(()=>{});return s.useEffect(()=>{let handlePointerDown=e=>{if(e.target&&!i.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",a,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",u.current),u.current=handleAndDispatchPointerDownOutsideEvent2,n.addEventListener("click",u.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else n.removeEventListener("click",u.current);i.current=!1},e=window.setTimeout(()=>{n.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),n.removeEventListener("pointerdown",handlePointerDown),n.removeEventListener("click",u.current)}},[n,a]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let n=e.target,a=[...E.branches].some(e=>e.contains(n));!L||a||(f?.(e),g?.(e),e.defaultPrevented||w?.())},R),W=function(e,n=globalThis?.document){let a=(0,y.W)(e),i=s.useRef(!1);return s.useEffect(()=>{let handleFocus=e=>{e.target&&!i.current&&handleAndDispatchCustomEvent("dismissableLayer.focusOutside",a,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",handleFocus),()=>n.removeEventListener("focusin",handleFocus)},[n,a]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let n=e.target,a=[...E.branches].some(e=>e.contains(n));a||(h?.(e),g?.(e),e.defaultPrevented||w?.())},R);return function(e,n=globalThis?.document){let a=(0,y.W)(e);s.useEffect(()=>{let handleKeyDown=e=>{"Escape"===e.key&&a(e)};return n.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>n.removeEventListener("keydown",handleKeyDown,{capture:!0})},[a,n])}(e=>{let n=D===E.layers.size-1;n&&(u?.(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},R),s.useEffect(()=>{if(S)return a&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(i=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),dispatchUpdate(),()=>{a&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=i)}},[S,R,a,E]),s.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),dispatchUpdate())},[S,E]),s.useEffect(()=>{let handleUpdate=()=>C({});return document.addEventListener(b,handleUpdate),()=>document.removeEventListener(b,handleUpdate)},[]),(0,m.jsx)(v.WV.div,{...x,ref:O,style:{pointerEvents:k?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,d.M)(e.onFocusCapture,W.onFocusCapture),onBlurCapture:(0,d.M)(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:(0,d.M)(e.onPointerDownCapture,F.onPointerDownCapture)})});function dispatchUpdate(){let e=new CustomEvent(b);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,n,a,{discrete:i}){let s=a.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:a});n&&s.addEventListener(e,n,{once:!0}),i?(0,v.jH)(s,u):s.dispatchEvent(u)}w.displayName="DismissableLayer",s.forwardRef((e,n)=>{let a=s.useContext(_),i=s.useRef(null),u=(0,p.e)(n,i);return s.useEffect(()=>{let e=i.current;if(e)return a.branches.add(e),()=>{a.branches.delete(e)}},[a.branches]),(0,m.jsx)(v.WV.div,{...e,ref:u})}).displayName="DismissableLayerBranch";var x=0;function createFocusGuard(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var E="focusScope.autoFocusOnMount",S="focusScope.autoFocusOnUnmount",P={bubbles:!1,cancelable:!0},R=s.forwardRef((e,n)=>{let{loop:a=!1,trapped:i=!1,onMountAutoFocus:u,onUnmountAutoFocus:d,...f}=e,[h,g]=s.useState(null),b=(0,y.W)(u),_=(0,y.W)(d),w=s.useRef(null),x=(0,p.e)(n,e=>g(e)),R=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(i){let handleFocusIn2=function(e){if(R.paused||!h)return;let n=e.target;h.contains(n)?w.current=n:dist_focus(w.current,{select:!0})},handleFocusOut2=function(e){if(R.paused||!h)return;let n=e.relatedTarget;null===n||h.contains(n)||dist_focus(w.current,{select:!0})};document.addEventListener("focusin",handleFocusIn2),document.addEventListener("focusout",handleFocusOut2);let e=new MutationObserver(function(e){let n=document.activeElement;if(n===document.body)for(let n of e)n.removedNodes.length>0&&dist_focus(h)});return h&&e.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",handleFocusIn2),document.removeEventListener("focusout",handleFocusOut2),e.disconnect()}}},[i,h,R.paused]),s.useEffect(()=>{if(h){C.add(R);let e=document.activeElement,n=h.contains(e);if(!n){let n=new CustomEvent(E,P);h.addEventListener(E,b),h.dispatchEvent(n),n.defaultPrevented||(function(e,{select:n=!1}={}){let a=document.activeElement;for(let i of e)if(dist_focus(i,{select:n}),document.activeElement!==a)return}(getTabbableCandidates(h).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&dist_focus(h))}return()=>{h.removeEventListener(E,b),setTimeout(()=>{let n=new CustomEvent(S,P);h.addEventListener(S,_),h.dispatchEvent(n),n.defaultPrevented||dist_focus(e??document.body,{select:!0}),h.removeEventListener(S,_),C.remove(R)},0)}}},[h,b,_,R]);let O=s.useCallback(e=>{if(!a&&!i||R.paused)return;let n="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,s=document.activeElement;if(n&&s){let n=e.currentTarget,[i,u]=function(e){let n=getTabbableCandidates(e),a=findVisible(n,e),i=findVisible(n.reverse(),e);return[a,i]}(n),d=i&&u;d?e.shiftKey||s!==u?e.shiftKey&&s===i&&(e.preventDefault(),a&&dist_focus(u,{select:!0})):(e.preventDefault(),a&&dist_focus(i,{select:!0})):s===n&&e.preventDefault()}},[a,i,R.paused]);return(0,m.jsx)(v.WV.div,{tabIndex:-1,...f,ref:x,onKeyDown:O})});function getTabbableCandidates(e){let n=[],a=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let n="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||n?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;a.nextNode();)n.push(a.currentNode);return n}function findVisible(e,n){for(let a of e)if(!function(e,{upTo:n}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(a,{upTo:n}))return a}function dist_focus(e,{select:n=!1}={}){if(e&&e.focus){var a;let i=document.activeElement;e.focus({preventScroll:!0}),e!==i&&(a=e)instanceof HTMLInputElement&&"select"in a&&n&&e.select()}}R.displayName="FocusScope";var C=function(){let e=[];return{add(n){let a=e[0];n!==a&&a?.pause(),(e=arrayRemove(e,n)).unshift(n)},remove(n){e=arrayRemove(e,n),e[0]?.resume()}}}();function arrayRemove(e,n){let a=[...e],i=a.indexOf(n);return -1!==i&&a.splice(i,1),a}var O=a(6529),j=a(9727),M=a(5852),N=s.forwardRef((e,n)=>{let{container:a,...i}=e,[d,f]=s.useState(!1);(0,M.b)(()=>f(!0),[]);let p=a||d&&globalThis?.document?.body;return p?u.createPortal((0,m.jsx)(v.WV.div,{...i,ref:n}),p):null});N.displayName="Portal";var D=a(1085),k=a(3408),L=a(1359),F=a(4348),W=a(5286),H=[" ","Enter","ArrowUp","ArrowDown"],V=[" ","Enter"],z="Select",[K,$,Y]=(0,f.B)(z),[Q,Z]=(0,h.b)(z,[Y,j.D7]),J=(0,j.D7)(),[ee,et]=Q(z),[er,en]=Q(z),Select=e=>{let{__scopeSelect:n,children:a,open:i,defaultOpen:u,onOpenChange:d,value:f,defaultValue:p,onValueChange:h,dir:v,name:y,autoComplete:b,disabled:_,required:w,form:x}=e,E=J(n),[S,P]=s.useState(null),[R,C]=s.useState(null),[M,N]=s.useState(!1),D=function(e){let n=s.useContext(g);return e||n||"ltr"}(v),[L=!1,F]=(0,k.T)({prop:i,defaultProp:u,onChange:d}),[W,H]=(0,k.T)({prop:f,defaultProp:p,onChange:h}),V=s.useRef(null),z=!S||x||!!S.closest("form"),[$,Y]=s.useState(new Set),Q=Array.from($).map(e=>e.props.value).join(";");return(0,m.jsx)(j.fC,{...E,children:(0,m.jsxs)(ee,{required:w,scope:n,trigger:S,onTriggerChange:P,valueNode:R,onValueNodeChange:C,valueNodeHasChildren:M,onValueNodeHasChildrenChange:N,contentId:(0,O.M)(),value:W,onValueChange:H,open:L,onOpenChange:F,dir:D,triggerPointerDownPosRef:V,disabled:_,children:[(0,m.jsx)(K.Provider,{scope:n,children:(0,m.jsx)(er,{scope:e.__scopeSelect,onNativeOptionAdd:s.useCallback(e=>{Y(n=>new Set(n).add(e))},[]),onNativeOptionRemove:s.useCallback(e=>{Y(n=>{let a=new Set(n);return a.delete(e),a})},[]),children:a})}),z?(0,m.jsxs)(eU,{"aria-hidden":!0,required:w,tabIndex:-1,name:y,autoComplete:b,value:W,onChange:e=>H(e.target.value),disabled:_,form:x,children:[void 0===W?(0,m.jsx)("option",{value:""}):null,Array.from($)]},Q):null]})})};Select.displayName=z;var eo="SelectTrigger",ea=s.forwardRef((e,n)=>{let{__scopeSelect:a,disabled:i=!1,...u}=e,f=J(a),h=et(eo,a),g=h.disabled||i,y=(0,p.e)(n,h.onTriggerChange),b=$(a),_=s.useRef("touch"),[w,x,E]=useTypeaheadSearch(e=>{let n=b().filter(e=>!e.disabled),a=n.find(e=>e.value===h.value),i=findNextItem(n,e,a);void 0!==i&&h.onValueChange(i.value)}),handleOpen=e=>{g||(h.onOpenChange(!0),E()),e&&(h.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,m.jsx)(j.ee,{asChild:!0,...f,children:(0,m.jsx)(v.WV.button,{type:"button",role:"combobox","aria-controls":h.contentId,"aria-expanded":h.open,"aria-required":h.required,"aria-autocomplete":"none",dir:h.dir,"data-state":h.open?"open":"closed",disabled:g,"data-disabled":g?"":void 0,"data-placeholder":shouldShowPlaceholder(h.value)?"":void 0,...u,ref:y,onClick:(0,d.M)(u.onClick,e=>{e.currentTarget.focus(),"mouse"!==_.current&&handleOpen(e)}),onPointerDown:(0,d.M)(u.onPointerDown,e=>{_.current=e.pointerType;let n=e.target;n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(handleOpen(e),e.preventDefault())}),onKeyDown:(0,d.M)(u.onKeyDown,e=>{let n=""!==w.current,a=e.ctrlKey||e.altKey||e.metaKey;a||1!==e.key.length||x(e.key),(!n||" "!==e.key)&&H.includes(e.key)&&(handleOpen(),e.preventDefault())})})})});ea.displayName=eo;var ei="SelectValue",el=s.forwardRef((e,n)=>{let{__scopeSelect:a,className:i,style:s,children:u,placeholder:d="",...f}=e,h=et(ei,a),{onValueNodeHasChildrenChange:g}=h,y=void 0!==u,b=(0,p.e)(n,h.onValueNodeChange);return(0,M.b)(()=>{g(y)},[g,y]),(0,m.jsx)(v.WV.span,{...f,ref:b,style:{pointerEvents:"none"},children:shouldShowPlaceholder(h.value)?(0,m.jsx)(m.Fragment,{children:d}):u})});el.displayName=ei;var es=s.forwardRef((e,n)=>{let{__scopeSelect:a,children:i,...s}=e;return(0,m.jsx)(v.WV.span,{"aria-hidden":!0,...s,ref:n,children:i||"▼"})});es.displayName="SelectIcon";var SelectPortal=e=>(0,m.jsx)(N,{asChild:!0,...e});SelectPortal.displayName="SelectPortal";var eu="SelectContent",ec=s.forwardRef((e,n)=>{let a=et(eu,e.__scopeSelect),[i,d]=s.useState();return((0,M.b)(()=>{d(new DocumentFragment)},[]),a.open)?(0,m.jsx)(ep,{...e,ref:n}):i?u.createPortal((0,m.jsx)(ed,{scope:e.__scopeSelect,children:(0,m.jsx)(K.Slot,{scope:e.__scopeSelect,children:(0,m.jsx)("div",{children:e.children})})}),i):null});ec.displayName=eu;var[ed,ef]=Q(eu),ep=s.forwardRef((e,n)=>{let{__scopeSelect:a,position:i="item-aligned",onCloseAutoFocus:u,onEscapeKeyDown:f,onPointerDownOutside:h,side:g,sideOffset:v,align:y,alignOffset:b,arrowPadding:_,collisionBoundary:E,collisionPadding:S,sticky:P,hideWhenDetached:C,avoidCollisions:O,...j}=e,M=et(eu,a),[N,k]=s.useState(null),[L,H]=s.useState(null),V=(0,p.e)(n,e=>k(e)),[z,K]=s.useState(null),[Y,Q]=s.useState(null),Z=$(a),[J,ee]=s.useState(!1),er=s.useRef(!1);s.useEffect(()=>{if(N)return(0,F.Ry)(N)},[N]),s.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??createFocusGuard()),document.body.insertAdjacentElement("beforeend",e[1]??createFocusGuard()),x++,()=>{1===x&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),x--}},[]);let en=s.useCallback(e=>{let[n,...a]=Z().map(e=>e.ref.current),[i]=a.slice(-1),s=document.activeElement;for(let a of e)if(a===s||(a?.scrollIntoView({block:"nearest"}),a===n&&L&&(L.scrollTop=0),a===i&&L&&(L.scrollTop=L.scrollHeight),a?.focus(),document.activeElement!==s))return},[Z,L]),eo=s.useCallback(()=>en([z,N]),[en,z,N]);s.useEffect(()=>{J&&eo()},[J,eo]);let{onOpenChange:ea,triggerPointerDownPosRef:ei}=M;s.useEffect(()=>{if(N){let e={x:0,y:0},handlePointerMove=n=>{e={x:Math.abs(Math.round(n.pageX)-(ei.current?.x??0)),y:Math.abs(Math.round(n.pageY)-(ei.current?.y??0))}},handlePointerUp=n=>{e.x<=10&&e.y<=10?n.preventDefault():N.contains(n.target)||ea(!1),document.removeEventListener("pointermove",handlePointerMove),ei.current=null};return null!==ei.current&&(document.addEventListener("pointermove",handlePointerMove),document.addEventListener("pointerup",handlePointerUp,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",handlePointerMove),document.removeEventListener("pointerup",handlePointerUp,{capture:!0})}}},[N,ea,ei]),s.useEffect(()=>{let close=()=>ea(!1);return window.addEventListener("blur",close),window.addEventListener("resize",close),()=>{window.removeEventListener("blur",close),window.removeEventListener("resize",close)}},[ea]);let[el,es]=useTypeaheadSearch(e=>{let n=Z().filter(e=>!e.disabled),a=n.find(e=>e.ref.current===document.activeElement),i=findNextItem(n,e,a);i&&setTimeout(()=>i.ref.current.focus())}),ec=s.useCallback((e,n,a)=>{let i=!er.current&&!a,s=void 0!==M.value&&M.value===n;(s||i)&&(K(e),i&&(er.current=!0))},[M.value]),ef=s.useCallback(()=>N?.focus(),[N]),ep=s.useCallback((e,n,a)=>{let i=!er.current&&!a,s=void 0!==M.value&&M.value===n;(s||i)&&Q(e)},[M.value]),eg="popper"===i?em:eh,ev=eg===em?{side:g,sideOffset:v,align:y,alignOffset:b,arrowPadding:_,collisionBoundary:E,collisionPadding:S,sticky:P,hideWhenDetached:C,avoidCollisions:O}:{};return(0,m.jsx)(ed,{scope:a,content:N,viewport:L,onViewportChange:H,itemRefCallback:ec,selectedItem:z,onItemLeave:ef,itemTextRefCallback:ep,focusSelectedItem:eo,selectedItemText:Y,position:i,isPositioned:J,searchRef:el,children:(0,m.jsx)(W.f,{as:D.g7,allowPinchZoom:!0,children:(0,m.jsx)(R,{asChild:!0,trapped:M.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,d.M)(u,e=>{M.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,m.jsx)(w,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>M.onOpenChange(!1),children:(0,m.jsx)(eg,{role:"listbox",id:M.contentId,"data-state":M.open?"open":"closed",dir:M.dir,onContextMenu:e=>e.preventDefault(),...j,...ev,onPlaced:()=>ee(!0),ref:V,style:{display:"flex",flexDirection:"column",outline:"none",...j.style},onKeyDown:(0,d.M)(j.onKeyDown,e=>{let n=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),n||1!==e.key.length||es(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let n=Z().filter(e=>!e.disabled),a=n.map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(a=a.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,i=a.indexOf(n);a=a.slice(i+1)}setTimeout(()=>en(a)),e.preventDefault()}})})})})})})});ep.displayName="SelectContentImpl";var eh=s.forwardRef((e,n)=>{let{__scopeSelect:a,onPlaced:i,...u}=e,d=et(eu,a),f=ef(eu,a),[h,g]=s.useState(null),[y,b]=s.useState(null),_=(0,p.e)(n,e=>b(e)),w=$(a),x=s.useRef(!1),E=s.useRef(!0),{viewport:S,selectedItem:P,selectedItemText:R,focusSelectedItem:C}=f,O=s.useCallback(()=>{if(d.trigger&&d.valueNode&&h&&y&&S&&P&&R){let e=d.trigger.getBoundingClientRect(),n=y.getBoundingClientRect(),a=d.valueNode.getBoundingClientRect(),s=R.getBoundingClientRect();if("rtl"!==d.dir){let i=s.left-n.left,u=a.left-i,d=e.left-u,f=e.width+d,p=Math.max(f,n.width),m=window.innerWidth-10,g=clamp(u,[10,Math.max(10,m-p)]);h.style.minWidth=f+"px",h.style.left=g+"px"}else{let i=n.right-s.right,u=window.innerWidth-a.right-i,d=window.innerWidth-e.right-u,f=e.width+d,p=Math.max(f,n.width),m=window.innerWidth-10,g=clamp(u,[10,Math.max(10,m-p)]);h.style.minWidth=f+"px",h.style.right=g+"px"}let u=w(),f=window.innerHeight-20,p=S.scrollHeight,m=window.getComputedStyle(y),g=parseInt(m.borderTopWidth,10),v=parseInt(m.paddingTop,10),b=parseInt(m.borderBottomWidth,10),_=parseInt(m.paddingBottom,10),E=g+v+p+_+b,C=Math.min(5*P.offsetHeight,E),O=window.getComputedStyle(S),j=parseInt(O.paddingTop,10),M=parseInt(O.paddingBottom,10),N=e.top+e.height/2-10,D=P.offsetHeight/2,k=P.offsetTop+D,L=g+v+k;if(L<=N){let e=u.length>0&&P===u[u.length-1].ref.current;h.style.bottom="0px";let n=y.clientHeight-S.offsetTop-S.offsetHeight;h.style.height=L+Math.max(f-N,D+(e?M:0)+n+b)+"px"}else{let e=u.length>0&&P===u[0].ref.current;h.style.top="0px";let n=Math.max(N,g+S.offsetTop+(e?j:0)+D);h.style.height=n+(E-L)+"px",S.scrollTop=L-N+S.offsetTop}h.style.margin="10px 0",h.style.minHeight=C+"px",h.style.maxHeight=f+"px",i?.(),requestAnimationFrame(()=>x.current=!0)}},[w,d.trigger,d.valueNode,h,y,S,P,R,d.dir,i]);(0,M.b)(()=>O(),[O]);let[j,N]=s.useState();(0,M.b)(()=>{y&&N(window.getComputedStyle(y).zIndex)},[y]);let D=s.useCallback(e=>{e&&!0===E.current&&(O(),C?.(),E.current=!1)},[O,C]);return(0,m.jsx)(eg,{scope:a,contentWrapper:h,shouldExpandOnScrollRef:x,onScrollButtonChange:D,children:(0,m.jsx)("div",{ref:g,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,m.jsx)(v.WV.div,{...u,ref:_,style:{boxSizing:"border-box",maxHeight:"100%",...u.style}})})})});eh.displayName="SelectItemAlignedPosition";var em=s.forwardRef((e,n)=>{let{__scopeSelect:a,align:i="start",collisionPadding:s=10,...u}=e,d=J(a);return(0,m.jsx)(j.VY,{...d,...u,ref:n,align:i,collisionPadding:s,style:{boxSizing:"border-box",...u.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});em.displayName="SelectPopperPosition";var[eg,ev]=Q(eu,{}),ey="SelectViewport",eb=s.forwardRef((e,n)=>{let{__scopeSelect:a,nonce:i,...u}=e,f=ef(ey,a),h=ev(ey,a),g=(0,p.e)(n,f.onViewportChange),y=s.useRef(0);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,m.jsx)(K.Slot,{scope:a,children:(0,m.jsx)(v.WV.div,{"data-radix-select-viewport":"",role:"presentation",...u,ref:g,style:{position:"relative",flex:1,overflow:"hidden auto",...u.style},onScroll:(0,d.M)(u.onScroll,e=>{let n=e.currentTarget,{contentWrapper:a,shouldExpandOnScrollRef:i}=h;if(i?.current&&a){let e=Math.abs(y.current-n.scrollTop);if(e>0){let i=window.innerHeight-20,s=parseFloat(a.style.minHeight),u=parseFloat(a.style.height),d=Math.max(s,u);if(d<i){let s=d+e,u=Math.min(i,s),f=s-u;a.style.height=u+"px","0px"===a.style.bottom&&(n.scrollTop=f>0?f:0,a.style.justifyContent="flex-end")}}}y.current=n.scrollTop})})})]})});eb.displayName=ey;var e_="SelectGroup",[ew,ex]=Q(e_),eE=s.forwardRef((e,n)=>{let{__scopeSelect:a,...i}=e,s=(0,O.M)();return(0,m.jsx)(ew,{scope:a,id:s,children:(0,m.jsx)(v.WV.div,{role:"group","aria-labelledby":s,...i,ref:n})})});eE.displayName=e_;var eS="SelectLabel",eP=s.forwardRef((e,n)=>{let{__scopeSelect:a,...i}=e,s=ex(eS,a);return(0,m.jsx)(v.WV.div,{id:s.id,...i,ref:n})});eP.displayName=eS;var eR="SelectItem",[eC,eO]=Q(eR),eT=s.forwardRef((e,n)=>{let{__scopeSelect:a,value:i,disabled:u=!1,textValue:f,...h}=e,g=et(eR,a),y=ef(eR,a),b=g.value===i,[_,w]=s.useState(f??""),[x,E]=s.useState(!1),S=(0,p.e)(n,e=>y.itemRefCallback?.(e,i,u)),P=(0,O.M)(),R=s.useRef("touch"),handleSelect=()=>{u||(g.onValueChange(i),g.onOpenChange(!1))};if(""===i)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,m.jsx)(eC,{scope:a,value:i,disabled:u,textId:P,isSelected:b,onItemTextChange:s.useCallback(e=>{w(n=>n||(e?.textContent??"").trim())},[]),children:(0,m.jsx)(K.ItemSlot,{scope:a,value:i,disabled:u,textValue:_,children:(0,m.jsx)(v.WV.div,{role:"option","aria-labelledby":P,"data-highlighted":x?"":void 0,"aria-selected":b&&x,"data-state":b?"checked":"unchecked","aria-disabled":u||void 0,"data-disabled":u?"":void 0,tabIndex:u?void 0:-1,...h,ref:S,onFocus:(0,d.M)(h.onFocus,()=>E(!0)),onBlur:(0,d.M)(h.onBlur,()=>E(!1)),onClick:(0,d.M)(h.onClick,()=>{"mouse"!==R.current&&handleSelect()}),onPointerUp:(0,d.M)(h.onPointerUp,()=>{"mouse"===R.current&&handleSelect()}),onPointerDown:(0,d.M)(h.onPointerDown,e=>{R.current=e.pointerType}),onPointerMove:(0,d.M)(h.onPointerMove,e=>{R.current=e.pointerType,u?y.onItemLeave?.():"mouse"===R.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,d.M)(h.onPointerLeave,e=>{e.currentTarget===document.activeElement&&y.onItemLeave?.()}),onKeyDown:(0,d.M)(h.onKeyDown,e=>{let n=y.searchRef?.current!=="";n&&" "===e.key||(V.includes(e.key)&&handleSelect()," "===e.key&&e.preventDefault())})})})})});eT.displayName=eR;var ej="SelectItemText",eA=s.forwardRef((e,n)=>{let{__scopeSelect:a,className:i,style:d,...f}=e,h=et(ej,a),g=ef(ej,a),y=eO(ej,a),b=en(ej,a),[_,w]=s.useState(null),x=(0,p.e)(n,e=>w(e),y.onItemTextChange,e=>g.itemTextRefCallback?.(e,y.value,y.disabled)),E=_?.textContent,S=s.useMemo(()=>(0,m.jsx)("option",{value:y.value,disabled:y.disabled,children:E},y.value),[y.disabled,y.value,E]),{onNativeOptionAdd:P,onNativeOptionRemove:R}=b;return(0,M.b)(()=>(P(S),()=>R(S)),[P,R,S]),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(v.WV.span,{id:y.textId,...f,ref:x}),y.isSelected&&h.valueNode&&!h.valueNodeHasChildren?u.createPortal(f.children,h.valueNode):null]})});eA.displayName=ej;var eM="SelectItemIndicator",eN=s.forwardRef((e,n)=>{let{__scopeSelect:a,...i}=e,s=eO(eM,a);return s.isSelected?(0,m.jsx)(v.WV.span,{"aria-hidden":!0,...i,ref:n}):null});eN.displayName=eM;var eD="SelectScrollUpButton",ek=s.forwardRef((e,n)=>{let a=ef(eD,e.__scopeSelect),i=ev(eD,e.__scopeSelect),[u,d]=s.useState(!1),f=(0,p.e)(n,i.onScrollButtonChange);return(0,M.b)(()=>{if(a.viewport&&a.isPositioned){let handleScroll2=function(){let n=e.scrollTop>0;d(n)},e=a.viewport;return handleScroll2(),e.addEventListener("scroll",handleScroll2),()=>e.removeEventListener("scroll",handleScroll2)}},[a.viewport,a.isPositioned]),u?(0,m.jsx)(eI,{...e,ref:f,onAutoScroll:()=>{let{viewport:e,selectedItem:n}=a;e&&n&&(e.scrollTop=e.scrollTop-n.offsetHeight)}}):null});ek.displayName=eD;var eL="SelectScrollDownButton",eF=s.forwardRef((e,n)=>{let a=ef(eL,e.__scopeSelect),i=ev(eL,e.__scopeSelect),[u,d]=s.useState(!1),f=(0,p.e)(n,i.onScrollButtonChange);return(0,M.b)(()=>{if(a.viewport&&a.isPositioned){let handleScroll2=function(){let n=e.scrollHeight-e.clientHeight,a=Math.ceil(e.scrollTop)<n;d(a)},e=a.viewport;return handleScroll2(),e.addEventListener("scroll",handleScroll2),()=>e.removeEventListener("scroll",handleScroll2)}},[a.viewport,a.isPositioned]),u?(0,m.jsx)(eI,{...e,ref:f,onAutoScroll:()=>{let{viewport:e,selectedItem:n}=a;e&&n&&(e.scrollTop=e.scrollTop+n.offsetHeight)}}):null});eF.displayName=eL;var eI=s.forwardRef((e,n)=>{let{__scopeSelect:a,onAutoScroll:i,...u}=e,f=ef("SelectScrollButton",a),p=s.useRef(null),h=$(a),g=s.useCallback(()=>{null!==p.current&&(window.clearInterval(p.current),p.current=null)},[]);return s.useEffect(()=>()=>g(),[g]),(0,M.b)(()=>{let e=h().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[h]),(0,m.jsx)(v.WV.div,{"aria-hidden":!0,...u,ref:n,style:{flexShrink:0,...u.style},onPointerDown:(0,d.M)(u.onPointerDown,()=>{null===p.current&&(p.current=window.setInterval(i,50))}),onPointerMove:(0,d.M)(u.onPointerMove,()=>{f.onItemLeave?.(),null===p.current&&(p.current=window.setInterval(i,50))}),onPointerLeave:(0,d.M)(u.onPointerLeave,()=>{g()})})}),eW=s.forwardRef((e,n)=>{let{__scopeSelect:a,...i}=e;return(0,m.jsx)(v.WV.div,{"aria-hidden":!0,...i,ref:n})});eW.displayName="SelectSeparator";var eB="SelectArrow";function shouldShowPlaceholder(e){return""===e||void 0===e}s.forwardRef((e,n)=>{let{__scopeSelect:a,...i}=e,s=J(a),u=et(eB,a),d=ef(eB,a);return u.open&&"popper"===d.position?(0,m.jsx)(j.Eh,{...s,...i,ref:n}):null}).displayName=eB;var eU=s.forwardRef((e,n)=>{let{value:a,...i}=e,u=s.useRef(null),d=(0,p.e)(n,u),f=function(e){let n=s.useRef({value:e,previous:e});return s.useMemo(()=>(n.current.value!==e&&(n.current.previous=n.current.value,n.current.value=e),n.current.previous),[e])}(a);return s.useEffect(()=>{let e=u.current,n=window.HTMLSelectElement.prototype,i=Object.getOwnPropertyDescriptor(n,"value"),s=i.set;if(f!==a&&s){let n=new Event("change",{bubbles:!0});s.call(e,a),e.dispatchEvent(n)}},[f,a]),(0,m.jsx)(L.T,{asChild:!0,children:(0,m.jsx)("select",{...i,ref:d,defaultValue:a})})});function useTypeaheadSearch(e){let n=(0,y.W)(e),a=s.useRef(""),i=s.useRef(0),u=s.useCallback(e=>{let s=a.current+e;n(s),function updateSearch(e){a.current=e,window.clearTimeout(i.current),""!==e&&(i.current=window.setTimeout(()=>updateSearch(""),1e3))}(s)},[n]),d=s.useCallback(()=>{a.current="",window.clearTimeout(i.current)},[]);return s.useEffect(()=>()=>window.clearTimeout(i.current),[]),[a,u,d]}function findNextItem(e,n,a){var i;let s=n.length>1&&Array.from(n).every(e=>e===n[0]),u=s?n[0]:n,d=a?e.indexOf(a):-1,f=(i=Math.max(d,0),e.map((n,a)=>e[(i+a)%e.length])),p=1===u.length;p&&(f=f.filter(e=>e!==a));let h=f.find(e=>e.textValue.toLowerCase().startsWith(u.toLowerCase()));return h!==a?h:void 0}eU.displayName="BubbleSelect";var eH=Select,eV=ea,ez=el,eK=es,eG=SelectPortal,e$=ec,eq=eb,eY=eE,eX=eP,eQ=eT,eZ=eA,eJ=eN,e0=ek,e1=eF,e2=eW},1085:(e,n,a)=>{"use strict";a.d(n,{A4:()=>Slottable,g7:()=>d});var i=a(9885),s=a(880),u=a(784),d=i.forwardRef((e,n)=>{let{children:a,...s}=e,d=i.Children.toArray(a),p=d.find(isSlottable);if(p){let e=p.props.children,a=d.map(n=>n!==p?n:i.Children.count(e)>1?i.Children.only(null):i.isValidElement(e)?e.props.children:null);return(0,u.jsx)(f,{...s,ref:n,children:i.isValidElement(e)?i.cloneElement(e,void 0,a):null})}return(0,u.jsx)(f,{...s,ref:n,children:a})});d.displayName="Slot";var f=i.forwardRef((e,n)=>{let{children:a,...u}=e;if(i.isValidElement(a)){let e=function(e){let n=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,a=n&&"isReactWarning"in n&&n.isReactWarning;return a?e.ref:(a=(n=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a);return i.cloneElement(a,{...function(e,n){let a={...n};for(let i in n){let s=e[i],u=n[i],d=/^on[A-Z]/.test(i);d?s&&u?a[i]=(...e)=>{u(...e),s(...e)}:s&&(a[i]=s):"style"===i?a[i]={...s,...u}:"className"===i&&(a[i]=[s,u].filter(Boolean).join(" "))}return{...e,...a}}(u,a.props),ref:n?(0,s.F)(n,e):e})}return i.Children.count(a)>1?i.Children.only(null):null});f.displayName="SlotClone";var Slottable=({children:e})=>(0,u.jsx)(u.Fragment,{children:e});function isSlottable(e){return i.isValidElement(e)&&e.type===Slottable}},9203:(e,n,a)=>{"use strict";a.d(n,{aU:()=>ep,x8:()=>eh,dk:()=>ef,zt:()=>es,fC:()=>ec,Dx:()=>ed,l_:()=>eu});var i,s=a(9885),u=a(8908),d=a(5418),f=a(880),p=a(5537),h=a(8718),m=a(3979),g=a(2285),v=a(784),y="dismissableLayer.update",b=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),_=s.forwardRef((e,n)=>{let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:p,onFocusOutside:h,onInteractOutside:_,onDismiss:w,...x}=e,E=s.useContext(b),[S,P]=s.useState(null),R=S?.ownerDocument??globalThis?.document,[,C]=s.useState({}),O=(0,f.e)(n,e=>P(e)),j=Array.from(E.layers),[M]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),N=j.indexOf(M),D=S?j.indexOf(S):-1,k=E.layersWithOutsidePointerEventsDisabled.size>0,L=D>=N,F=function(e,n=globalThis?.document){let a=(0,g.W)(e),i=s.useRef(!1),u=s.useRef(()=>{});return s.useEffect(()=>{let handlePointerDown=e=>{if(e.target&&!i.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",a,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",u.current),u.current=handleAndDispatchPointerDownOutsideEvent2,n.addEventListener("click",u.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else n.removeEventListener("click",u.current);i.current=!1},e=window.setTimeout(()=>{n.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),n.removeEventListener("pointerdown",handlePointerDown),n.removeEventListener("click",u.current)}},[n,a]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let n=e.target,a=[...E.branches].some(e=>e.contains(n));!L||a||(p?.(e),_?.(e),e.defaultPrevented||w?.())},R),W=function(e,n=globalThis?.document){let a=(0,g.W)(e),i=s.useRef(!1);return s.useEffect(()=>{let handleFocus=e=>{e.target&&!i.current&&handleAndDispatchCustomEvent("dismissableLayer.focusOutside",a,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",handleFocus),()=>n.removeEventListener("focusin",handleFocus)},[n,a]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let n=e.target,a=[...E.branches].some(e=>e.contains(n));a||(h?.(e),_?.(e),e.defaultPrevented||w?.())},R);return function(e,n=globalThis?.document){let a=(0,g.W)(e);s.useEffect(()=>{let handleKeyDown=e=>{"Escape"===e.key&&a(e)};return n.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>n.removeEventListener("keydown",handleKeyDown,{capture:!0})},[a,n])}(e=>{let n=D===E.layers.size-1;n&&(u?.(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},R),s.useEffect(()=>{if(S)return a&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(i=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),dispatchUpdate(),()=>{a&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=i)}},[S,R,a,E]),s.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),dispatchUpdate())},[S,E]),s.useEffect(()=>{let handleUpdate=()=>C({});return document.addEventListener(y,handleUpdate),()=>document.removeEventListener(y,handleUpdate)},[]),(0,v.jsx)(m.WV.div,{...x,ref:O,style:{pointerEvents:k?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,d.M)(e.onFocusCapture,W.onFocusCapture),onBlurCapture:(0,d.M)(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:(0,d.M)(e.onPointerDownCapture,F.onPointerDownCapture)})});_.displayName="DismissableLayer";var w=s.forwardRef((e,n)=>{let a=s.useContext(b),i=s.useRef(null),u=(0,f.e)(n,i);return s.useEffect(()=>{let e=i.current;if(e)return a.branches.add(e),()=>{a.branches.delete(e)}},[a.branches]),(0,v.jsx)(m.WV.div,{...e,ref:u})});function dispatchUpdate(){let e=new CustomEvent(y);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,n,a,{discrete:i}){let s=a.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:a});n&&s.addEventListener(e,n,{once:!0}),i?(0,m.jH)(s,u):s.dispatchEvent(u)}w.displayName="DismissableLayerBranch";var x=a(5852),E=s.forwardRef((e,n)=>{let{container:a,...i}=e,[d,f]=s.useState(!1);(0,x.b)(()=>f(!0),[]);let p=a||d&&globalThis?.document?.body;return p?u.createPortal((0,v.jsx)(m.WV.div,{...i,ref:n}),p):null});E.displayName="Portal";var S=a(9752),P=a(3408),R=a(1359),C="ToastProvider",[O,j,M]=(0,p.B)("Toast"),[N,D]=(0,h.b)("Toast",[M]),[k,L]=N(C),ToastProvider=e=>{let{__scopeToast:n,label:a="Notification",duration:i=5e3,swipeDirection:u="right",swipeThreshold:d=50,children:f}=e,[p,h]=s.useState(null),[m,g]=s.useState(0),y=s.useRef(!1),b=s.useRef(!1);return a.trim()||console.error(`Invalid prop \`label\` supplied to \`${C}\`. Expected non-empty \`string\`.`),(0,v.jsx)(O.Provider,{scope:n,children:(0,v.jsx)(k,{scope:n,label:a,duration:i,swipeDirection:u,swipeThreshold:d,toastCount:m,viewport:p,onViewportChange:h,onToastAdd:s.useCallback(()=>g(e=>e+1),[]),onToastRemove:s.useCallback(()=>g(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:y,isClosePausedRef:b,children:f})})};ToastProvider.displayName=C;var F="ToastViewport",W=["F8"],H="toast.viewportPause",V="toast.viewportResume",z=s.forwardRef((e,n)=>{let{__scopeToast:a,hotkey:i=W,label:u="Notifications ({hotkey})",...d}=e,p=L(F,a),h=j(a),g=s.useRef(null),y=s.useRef(null),b=s.useRef(null),_=s.useRef(null),x=(0,f.e)(n,_,p.onViewportChange),E=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),S=p.toastCount>0;s.useEffect(()=>{let handleKeyDown=e=>{let n=0!==i.length&&i.every(n=>e[n]||e.code===n);n&&_.current?.focus()};return document.addEventListener("keydown",handleKeyDown),()=>document.removeEventListener("keydown",handleKeyDown)},[i]),s.useEffect(()=>{let e=g.current,n=_.current;if(S&&e&&n){let handlePause=()=>{if(!p.isClosePausedRef.current){let e=new CustomEvent(H);n.dispatchEvent(e),p.isClosePausedRef.current=!0}},handleResume=()=>{if(p.isClosePausedRef.current){let e=new CustomEvent(V);n.dispatchEvent(e),p.isClosePausedRef.current=!1}},handleFocusOutResume=n=>{let a=!e.contains(n.relatedTarget);a&&handleResume()},handlePointerLeaveResume=()=>{let n=e.contains(document.activeElement);n||handleResume()};return e.addEventListener("focusin",handlePause),e.addEventListener("focusout",handleFocusOutResume),e.addEventListener("pointermove",handlePause),e.addEventListener("pointerleave",handlePointerLeaveResume),window.addEventListener("blur",handlePause),window.addEventListener("focus",handleResume),()=>{e.removeEventListener("focusin",handlePause),e.removeEventListener("focusout",handleFocusOutResume),e.removeEventListener("pointermove",handlePause),e.removeEventListener("pointerleave",handlePointerLeaveResume),window.removeEventListener("blur",handlePause),window.removeEventListener("focus",handleResume)}}},[S,p.isClosePausedRef]);let P=s.useCallback(({tabbingDirection:e})=>{let n=h(),a=n.map(n=>{let a=n.ref.current,i=[a,...function(e){let n=[],a=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let n="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||n?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;a.nextNode();)n.push(a.currentNode);return n}(a)];return"forwards"===e?i:i.reverse()});return("forwards"===e?a.reverse():a).flat()},[h]);return s.useEffect(()=>{let e=_.current;if(e){let handleKeyDown=n=>{let a=n.altKey||n.ctrlKey||n.metaKey,i="Tab"===n.key&&!a;if(i){let a=document.activeElement,i=n.shiftKey,s=n.target===e;if(s&&i){y.current?.focus();return}let u=P({tabbingDirection:i?"backwards":"forwards"}),d=u.findIndex(e=>e===a);focusFirst(u.slice(d+1))?n.preventDefault():i?y.current?.focus():b.current?.focus()}};return e.addEventListener("keydown",handleKeyDown),()=>e.removeEventListener("keydown",handleKeyDown)}},[h,P]),(0,v.jsxs)(w,{ref:g,role:"region","aria-label":u.replace("{hotkey}",E),tabIndex:-1,style:{pointerEvents:S?void 0:"none"},children:[S&&(0,v.jsx)($,{ref:y,onFocusFromOutsideViewport:()=>{let e=P({tabbingDirection:"forwards"});focusFirst(e)}}),(0,v.jsx)(O.Slot,{scope:a,children:(0,v.jsx)(m.WV.ol,{tabIndex:-1,...d,ref:x})}),S&&(0,v.jsx)($,{ref:b,onFocusFromOutsideViewport:()=>{let e=P({tabbingDirection:"backwards"});focusFirst(e)}})]})});z.displayName=F;var K="ToastFocusProxy",$=s.forwardRef((e,n)=>{let{__scopeToast:a,onFocusFromOutsideViewport:i,...s}=e,u=L(K,a);return(0,v.jsx)(R.T,{"aria-hidden":!0,tabIndex:0,...s,ref:n,style:{position:"fixed"},onFocus:e=>{let n=e.relatedTarget,a=!u.viewport?.contains(n);a&&i()}})});$.displayName=K;var Y="Toast",Q=s.forwardRef((e,n)=>{let{forceMount:a,open:i,defaultOpen:s,onOpenChange:u,...f}=e,[p=!0,h]=(0,P.T)({prop:i,defaultProp:s,onChange:u});return(0,v.jsx)(S.z,{present:a||p,children:(0,v.jsx)(ee,{open:p,...f,ref:n,onClose:()=>h(!1),onPause:(0,g.W)(e.onPause),onResume:(0,g.W)(e.onResume),onSwipeStart:(0,d.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,d.M)(e.onSwipeMove,e=>{let{x:n,y:a}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${n}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${a}px`)}),onSwipeCancel:(0,d.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,d.M)(e.onSwipeEnd,e=>{let{x:n,y:a}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${n}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${a}px`),h(!1)})})})});Q.displayName=Y;var[Z,J]=N(Y,{onClose(){}}),ee=s.forwardRef((e,n)=>{let{__scopeToast:a,type:i="foreground",duration:p,open:h,onClose:y,onEscapeKeyDown:b,onPause:w,onResume:x,onSwipeStart:E,onSwipeMove:S,onSwipeCancel:P,onSwipeEnd:R,...C}=e,j=L(Y,a),[M,N]=s.useState(null),D=(0,f.e)(n,e=>N(e)),k=s.useRef(null),F=s.useRef(null),W=p||j.duration,z=s.useRef(0),K=s.useRef(W),$=s.useRef(0),{onToastAdd:Q,onToastRemove:J}=j,ee=(0,g.W)(()=>{let e=M?.contains(document.activeElement);e&&j.viewport?.focus(),y()}),et=s.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout($.current),z.current=new Date().getTime(),$.current=window.setTimeout(ee,e))},[ee]);s.useEffect(()=>{let e=j.viewport;if(e){let handleResume=()=>{et(K.current),x?.()},handlePause=()=>{let e=new Date().getTime()-z.current;K.current=K.current-e,window.clearTimeout($.current),w?.()};return e.addEventListener(H,handlePause),e.addEventListener(V,handleResume),()=>{e.removeEventListener(H,handlePause),e.removeEventListener(V,handleResume)}}},[j.viewport,W,w,x,et]),s.useEffect(()=>{h&&!j.isClosePausedRef.current&&et(W)},[h,W,j.isClosePausedRef,et]),s.useEffect(()=>(Q(),()=>J()),[Q,J]);let er=s.useMemo(()=>M?function getAnnounceTextContent(e){let n=[],a=Array.from(e.childNodes);return a.forEach(e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&n.push(e.textContent),e.nodeType===e.ELEMENT_NODE){let a=e.ariaHidden||e.hidden||"none"===e.style.display,i=""===e.dataset.radixToastAnnounceExclude;if(!a){if(i){let a=e.dataset.radixToastAnnounceAlt;a&&n.push(a)}else n.push(...getAnnounceTextContent(e))}}}),n}(M):null,[M]);return j.viewport?(0,v.jsxs)(v.Fragment,{children:[er&&(0,v.jsx)(ToastAnnounce,{__scopeToast:a,role:"status","aria-live":"foreground"===i?"assertive":"polite","aria-atomic":!0,children:er}),(0,v.jsx)(Z,{scope:a,onClose:ee,children:u.createPortal((0,v.jsx)(O.ItemSlot,{scope:a,children:(0,v.jsx)(_,{asChild:!0,onEscapeKeyDown:(0,d.M)(b,()=>{j.isFocusedToastEscapeKeyDownRef.current||ee(),j.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,v.jsx)(m.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":h?"open":"closed","data-swipe-direction":j.swipeDirection,...C,ref:D,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,d.M)(e.onKeyDown,e=>{"Escape"!==e.key||(b?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(j.isFocusedToastEscapeKeyDownRef.current=!0,ee()))}),onPointerDown:(0,d.M)(e.onPointerDown,e=>{0===e.button&&(k.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,d.M)(e.onPointerMove,e=>{if(!k.current)return;let n=e.clientX-k.current.x,a=e.clientY-k.current.y,i=!!F.current,s=["left","right"].includes(j.swipeDirection),u=["left","up"].includes(j.swipeDirection)?Math.min:Math.max,d=s?u(0,n):0,f=s?0:u(0,a),p="touch"===e.pointerType?10:2,h={x:d,y:f},m={originalEvent:e,delta:h};i?(F.current=h,dist_handleAndDispatchCustomEvent("toast.swipeMove",S,m,{discrete:!1})):isDeltaInDirection(h,j.swipeDirection,p)?(F.current=h,dist_handleAndDispatchCustomEvent("toast.swipeStart",E,m,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(n)>p||Math.abs(a)>p)&&(k.current=null)}),onPointerUp:(0,d.M)(e.onPointerUp,e=>{let n=F.current,a=e.target;if(a.hasPointerCapture(e.pointerId)&&a.releasePointerCapture(e.pointerId),F.current=null,k.current=null,n){let a=e.currentTarget,i={originalEvent:e,delta:n};isDeltaInDirection(n,j.swipeDirection,j.swipeThreshold)?dist_handleAndDispatchCustomEvent("toast.swipeEnd",R,i,{discrete:!0}):dist_handleAndDispatchCustomEvent("toast.swipeCancel",P,i,{discrete:!0}),a.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),j.viewport)})]}):null}),ToastAnnounce=e=>{let{__scopeToast:n,children:a,...i}=e,u=L(Y,n),[d,f]=s.useState(!1),[p,h]=s.useState(!1);return function(e=()=>{}){let n=(0,g.W)(e);(0,x.b)(()=>{let e=0,a=0;return e=window.requestAnimationFrame(()=>a=window.requestAnimationFrame(n)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(a)}},[n])}(()=>f(!0)),s.useEffect(()=>{let e=window.setTimeout(()=>h(!0),1e3);return()=>window.clearTimeout(e)},[]),p?null:(0,v.jsx)(E,{asChild:!0,children:(0,v.jsx)(R.T,{...i,children:d&&(0,v.jsxs)(v.Fragment,{children:[u.label," ",a]})})})},et=s.forwardRef((e,n)=>{let{__scopeToast:a,...i}=e;return(0,v.jsx)(m.WV.div,{...i,ref:n})});et.displayName="ToastTitle";var er=s.forwardRef((e,n)=>{let{__scopeToast:a,...i}=e;return(0,v.jsx)(m.WV.div,{...i,ref:n})});er.displayName="ToastDescription";var en="ToastAction",eo=s.forwardRef((e,n)=>{let{altText:a,...i}=e;return a.trim()?(0,v.jsx)(el,{altText:a,asChild:!0,children:(0,v.jsx)(ei,{...i,ref:n})}):(console.error(`Invalid prop \`altText\` supplied to \`${en}\`. Expected non-empty \`string\`.`),null)});eo.displayName=en;var ea="ToastClose",ei=s.forwardRef((e,n)=>{let{__scopeToast:a,...i}=e,s=J(ea,a);return(0,v.jsx)(el,{asChild:!0,children:(0,v.jsx)(m.WV.button,{type:"button",...i,ref:n,onClick:(0,d.M)(e.onClick,s.onClose)})})});ei.displayName=ea;var el=s.forwardRef((e,n)=>{let{__scopeToast:a,altText:i,...s}=e;return(0,v.jsx)(m.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":i||void 0,...s,ref:n})});function dist_handleAndDispatchCustomEvent(e,n,a,{discrete:i}){let s=a.originalEvent.currentTarget,u=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:a});n&&s.addEventListener(e,n,{once:!0}),i?(0,m.jH)(s,u):s.dispatchEvent(u)}var isDeltaInDirection=(e,n,a=0)=>{let i=Math.abs(e.x),s=Math.abs(e.y),u=i>s;return"left"===n||"right"===n?u&&i>a:!u&&s>a};function focusFirst(e){let n=document.activeElement;return e.some(e=>e===n||(e.focus(),document.activeElement!==n))}var es=ToastProvider,eu=z,ec=Q,ed=et,ef=er,ep=eo,eh=ei},8691:(e,n,a)=>{"use strict";a.d(n,{VY:()=>ea,zt:()=>er,fC:()=>en,xz:()=>eo});var i,s=a(9885),u=a(5418),d=a(880),f=a(8718),p=a(3979),h=a(2285),m=a(784),g="dismissableLayer.update",v=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=s.forwardRef((e,n)=>{let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:f,onPointerDownOutside:y,onFocusOutside:b,onInteractOutside:_,onDismiss:w,...x}=e,E=s.useContext(v),[S,P]=s.useState(null),R=S?.ownerDocument??globalThis?.document,[,C]=s.useState({}),O=(0,d.e)(n,e=>P(e)),j=Array.from(E.layers),[M]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),N=j.indexOf(M),D=S?j.indexOf(S):-1,k=E.layersWithOutsidePointerEventsDisabled.size>0,L=D>=N,F=function(e,n=globalThis?.document){let a=(0,h.W)(e),i=s.useRef(!1),u=s.useRef(()=>{});return s.useEffect(()=>{let handlePointerDown=e=>{if(e.target&&!i.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",a,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",u.current),u.current=handleAndDispatchPointerDownOutsideEvent2,n.addEventListener("click",u.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else n.removeEventListener("click",u.current);i.current=!1},e=window.setTimeout(()=>{n.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),n.removeEventListener("pointerdown",handlePointerDown),n.removeEventListener("click",u.current)}},[n,a]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let n=e.target,a=[...E.branches].some(e=>e.contains(n));!L||a||(y?.(e),_?.(e),e.defaultPrevented||w?.())},R),W=function(e,n=globalThis?.document){let a=(0,h.W)(e),i=s.useRef(!1);return s.useEffect(()=>{let handleFocus=e=>{e.target&&!i.current&&handleAndDispatchCustomEvent("dismissableLayer.focusOutside",a,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",handleFocus),()=>n.removeEventListener("focusin",handleFocus)},[n,a]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let n=e.target,a=[...E.branches].some(e=>e.contains(n));a||(b?.(e),_?.(e),e.defaultPrevented||w?.())},R);return function(e,n=globalThis?.document){let a=(0,h.W)(e);s.useEffect(()=>{let handleKeyDown=e=>{"Escape"===e.key&&a(e)};return n.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>n.removeEventListener("keydown",handleKeyDown,{capture:!0})},[a,n])}(e=>{let n=D===E.layers.size-1;n&&(f?.(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},R),s.useEffect(()=>{if(S)return a&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(i=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),dispatchUpdate(),()=>{a&&1===E.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=i)}},[S,R,a,E]),s.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),dispatchUpdate())},[S,E]),s.useEffect(()=>{let handleUpdate=()=>C({});return document.addEventListener(g,handleUpdate),()=>document.removeEventListener(g,handleUpdate)},[]),(0,m.jsx)(p.WV.div,{...x,ref:O,style:{pointerEvents:k?L?"auto":"none":void 0,...e.style},onFocusCapture:(0,u.M)(e.onFocusCapture,W.onFocusCapture),onBlurCapture:(0,u.M)(e.onBlurCapture,W.onBlurCapture),onPointerDownCapture:(0,u.M)(e.onPointerDownCapture,F.onPointerDownCapture)})});function dispatchUpdate(){let e=new CustomEvent(g);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,n,a,{discrete:i}){let s=a.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:a});n&&s.addEventListener(e,n,{once:!0}),i?(0,p.jH)(s,u):s.dispatchEvent(u)}y.displayName="DismissableLayer",s.forwardRef((e,n)=>{let a=s.useContext(v),i=s.useRef(null),u=(0,d.e)(n,i);return s.useEffect(()=>{let e=i.current;if(e)return a.branches.add(e),()=>{a.branches.delete(e)}},[a.branches]),(0,m.jsx)(p.WV.div,{...e,ref:u})}).displayName="DismissableLayerBranch";var b=a(6529),_=a(9727),w=a(8908),x=a(5852);s.forwardRef((e,n)=>{let{container:a,...i}=e,[u,d]=s.useState(!1);(0,x.b)(()=>d(!0),[]);let f=a||u&&globalThis?.document?.body;return f?w.createPortal((0,m.jsx)(p.WV.div,{...i,ref:n}),f):null}).displayName="Portal";var E=a(9752),S=a(1085),P=a(3408),R=a(1359),[C,O]=(0,f.b)("Tooltip",[_.D7]),j=(0,_.D7)(),M="TooltipProvider",N="tooltip.open",[D,k]=C(M),TooltipProvider=e=>{let{__scopeTooltip:n,delayDuration:a=700,skipDelayDuration:i=300,disableHoverableContent:u=!1,children:d}=e,[f,p]=s.useState(!0),h=s.useRef(!1),g=s.useRef(0);return s.useEffect(()=>{let e=g.current;return()=>window.clearTimeout(e)},[]),(0,m.jsx)(D,{scope:n,isOpenDelayed:f,delayDuration:a,onOpen:s.useCallback(()=>{window.clearTimeout(g.current),p(!1)},[]),onClose:s.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>p(!0),i)},[i]),isPointerInTransitRef:h,onPointerInTransitChange:s.useCallback(e=>{h.current=e},[]),disableHoverableContent:u,children:d})};TooltipProvider.displayName=M;var L="Tooltip",[F,W]=C(L),Tooltip=e=>{let{__scopeTooltip:n,children:a,open:i,defaultOpen:u=!1,onOpenChange:d,disableHoverableContent:f,delayDuration:p}=e,h=k(L,e.__scopeTooltip),g=j(n),[v,y]=s.useState(null),w=(0,b.M)(),x=s.useRef(0),E=f??h.disableHoverableContent,S=p??h.delayDuration,R=s.useRef(!1),[C=!1,O]=(0,P.T)({prop:i,defaultProp:u,onChange:e=>{e?(h.onOpen(),document.dispatchEvent(new CustomEvent(N))):h.onClose(),d?.(e)}}),M=s.useMemo(()=>C?R.current?"delayed-open":"instant-open":"closed",[C]),D=s.useCallback(()=>{window.clearTimeout(x.current),x.current=0,R.current=!1,O(!0)},[O]),W=s.useCallback(()=>{window.clearTimeout(x.current),x.current=0,O(!1)},[O]),H=s.useCallback(()=>{window.clearTimeout(x.current),x.current=window.setTimeout(()=>{R.current=!0,O(!0),x.current=0},S)},[S,O]);return s.useEffect(()=>()=>{x.current&&(window.clearTimeout(x.current),x.current=0)},[]),(0,m.jsx)(_.fC,{...g,children:(0,m.jsx)(F,{scope:n,contentId:w,open:C,stateAttribute:M,trigger:v,onTriggerChange:y,onTriggerEnter:s.useCallback(()=>{h.isOpenDelayed?H():D()},[h.isOpenDelayed,H,D]),onTriggerLeave:s.useCallback(()=>{E?W():(window.clearTimeout(x.current),x.current=0)},[W,E]),onOpen:D,onClose:W,disableHoverableContent:E,children:a})})};Tooltip.displayName=L;var H="TooltipTrigger",V=s.forwardRef((e,n)=>{let{__scopeTooltip:a,...i}=e,f=W(H,a),h=k(H,a),g=j(a),v=s.useRef(null),y=(0,d.e)(n,v,f.onTriggerChange),b=s.useRef(!1),w=s.useRef(!1),x=s.useCallback(()=>b.current=!1,[]);return s.useEffect(()=>()=>document.removeEventListener("pointerup",x),[x]),(0,m.jsx)(_.ee,{asChild:!0,...g,children:(0,m.jsx)(p.WV.button,{"aria-describedby":f.open?f.contentId:void 0,"data-state":f.stateAttribute,...i,ref:y,onPointerMove:(0,u.M)(e.onPointerMove,e=>{"touch"===e.pointerType||w.current||h.isPointerInTransitRef.current||(f.onTriggerEnter(),w.current=!0)}),onPointerLeave:(0,u.M)(e.onPointerLeave,()=>{f.onTriggerLeave(),w.current=!1}),onPointerDown:(0,u.M)(e.onPointerDown,()=>{b.current=!0,document.addEventListener("pointerup",x,{once:!0})}),onFocus:(0,u.M)(e.onFocus,()=>{b.current||f.onOpen()}),onBlur:(0,u.M)(e.onBlur,f.onClose),onClick:(0,u.M)(e.onClick,f.onClose)})})});V.displayName=H;var[z,K]=C("TooltipPortal",{forceMount:void 0}),$="TooltipContent",Y=s.forwardRef((e,n)=>{let a=K($,e.__scopeTooltip),{forceMount:i=a.forceMount,side:s="top",...u}=e,d=W($,e.__scopeTooltip);return(0,m.jsx)(E.z,{present:i||d.open,children:d.disableHoverableContent?(0,m.jsx)(ee,{side:s,...u,ref:n}):(0,m.jsx)(Q,{side:s,...u,ref:n})})}),Q=s.forwardRef((e,n)=>{let a=W($,e.__scopeTooltip),i=k($,e.__scopeTooltip),u=s.useRef(null),f=(0,d.e)(n,u),[p,h]=s.useState(null),{trigger:g,onClose:v}=a,y=u.current,{onPointerInTransitChange:b}=i,_=s.useCallback(()=>{h(null),b(!1)},[b]),w=s.useCallback((e,n)=>{let a=e.currentTarget,i={x:e.clientX,y:e.clientY},s=function(e,n){let a=Math.abs(n.top-e.y),i=Math.abs(n.bottom-e.y),s=Math.abs(n.right-e.x),u=Math.abs(n.left-e.x);switch(Math.min(a,i,s,u)){case u:return"left";case s:return"right";case a:return"top";case i:return"bottom";default:throw Error("unreachable")}}(i,a.getBoundingClientRect()),u=function(e,n,a=5){let i=[];switch(n){case"top":i.push({x:e.x-a,y:e.y+a},{x:e.x+a,y:e.y+a});break;case"bottom":i.push({x:e.x-a,y:e.y-a},{x:e.x+a,y:e.y-a});break;case"left":i.push({x:e.x+a,y:e.y-a},{x:e.x+a,y:e.y+a});break;case"right":i.push({x:e.x-a,y:e.y-a},{x:e.x-a,y:e.y+a})}return i}(i,s),d=function(e){let{top:n,right:a,bottom:i,left:s}=e;return[{x:s,y:n},{x:a,y:n},{x:a,y:i},{x:s,y:i}]}(n.getBoundingClientRect()),f=function(e){let n=e.slice();return n.sort((e,n)=>e.x<n.x?-1:e.x>n.x?1:e.y<n.y?-1:e.y>n.y?1:0),function(e){if(e.length<=1)return e.slice();let n=[];for(let a=0;a<e.length;a++){let i=e[a];for(;n.length>=2;){let e=n[n.length-1],a=n[n.length-2];if((e.x-a.x)*(i.y-a.y)>=(e.y-a.y)*(i.x-a.x))n.pop();else break}n.push(i)}n.pop();let a=[];for(let n=e.length-1;n>=0;n--){let i=e[n];for(;a.length>=2;){let e=a[a.length-1],n=a[a.length-2];if((e.x-n.x)*(i.y-n.y)>=(e.y-n.y)*(i.x-n.x))a.pop();else break}a.push(i)}return(a.pop(),1===n.length&&1===a.length&&n[0].x===a[0].x&&n[0].y===a[0].y)?n:n.concat(a)}(n)}([...u,...d]);h(f),b(!0)},[b]);return s.useEffect(()=>()=>_(),[_]),s.useEffect(()=>{if(g&&y){let handleTriggerLeave=e=>w(e,y),handleContentLeave=e=>w(e,g);return g.addEventListener("pointerleave",handleTriggerLeave),y.addEventListener("pointerleave",handleContentLeave),()=>{g.removeEventListener("pointerleave",handleTriggerLeave),y.removeEventListener("pointerleave",handleContentLeave)}}},[g,y,w,_]),s.useEffect(()=>{if(p){let handleTrackPointerGrace=e=>{let n=e.target,a={x:e.clientX,y:e.clientY},i=g?.contains(n)||y?.contains(n),s=!function(e,n){let{x:a,y:i}=e,s=!1;for(let e=0,u=n.length-1;e<n.length;u=e++){let d=n[e].x,f=n[e].y,p=n[u].x,h=n[u].y,m=f>i!=h>i&&a<(p-d)*(i-f)/(h-f)+d;m&&(s=!s)}return s}(a,p);i?_():s&&(_(),v())};return document.addEventListener("pointermove",handleTrackPointerGrace),()=>document.removeEventListener("pointermove",handleTrackPointerGrace)}},[g,y,p,v,_]),(0,m.jsx)(ee,{...e,ref:f})}),[Z,J]=C(L,{isInside:!1}),ee=s.forwardRef((e,n)=>{let{__scopeTooltip:a,children:i,"aria-label":u,onEscapeKeyDown:d,onPointerDownOutside:f,...p}=e,h=W($,a),g=j(a),{onClose:v}=h;return s.useEffect(()=>(document.addEventListener(N,v),()=>document.removeEventListener(N,v)),[v]),s.useEffect(()=>{if(h.trigger){let handleScroll=e=>{let n=e.target;n?.contains(h.trigger)&&v()};return window.addEventListener("scroll",handleScroll,{capture:!0}),()=>window.removeEventListener("scroll",handleScroll,{capture:!0})}},[h.trigger,v]),(0,m.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:e=>e.preventDefault(),onDismiss:v,children:(0,m.jsxs)(_.VY,{"data-state":h.stateAttribute,...g,...p,ref:n,style:{...p.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,m.jsx)(S.A4,{children:i}),(0,m.jsx)(Z,{scope:a,isInside:!0,children:(0,m.jsx)(R.f,{id:h.contentId,role:"tooltip",children:u||i})})]})})});Y.displayName=$;var et="TooltipArrow";s.forwardRef((e,n)=>{let{__scopeTooltip:a,...i}=e,s=j(a),u=J(et,a);return u.isInside?null:(0,m.jsx)(_.Eh,{...s,...i,ref:n})}).displayName=et;var er=TooltipProvider,en=Tooltip,eo=V,ea=Y},2285:(e,n,a)=>{"use strict";a.d(n,{W:()=>useCallbackRef});var i=a(9885);function useCallbackRef(e){let n=i.useRef(e);return i.useEffect(()=>{n.current=e}),i.useMemo(()=>(...e)=>n.current?.(...e),[])}},3408:(e,n,a)=>{"use strict";a.d(n,{T:()=>useControllableState});var i=a(9885),s=a(2285);function useControllableState({prop:e,defaultProp:n,onChange:a=()=>{}}){let[u,d]=function({defaultProp:e,onChange:n}){let a=i.useState(e),[u]=a,d=i.useRef(u),f=(0,s.W)(n);return i.useEffect(()=>{d.current!==u&&(f(u),d.current=u)},[u,d,f]),a}({defaultProp:n,onChange:a}),f=void 0!==e,p=f?e:u,h=(0,s.W)(a),m=i.useCallback(n=>{if(f){let a="function"==typeof n?n(e):n;a!==e&&h(a)}else d(n)},[f,e,d,h]);return[p,m]}},5852:(e,n,a)=>{"use strict";a.d(n,{b:()=>s});var i=a(9885),s=globalThis?.document?i.useLayoutEffect:()=>{}},1359:(e,n,a)=>{"use strict";a.d(n,{T:()=>d,f:()=>f});var i=a(9885),s=a(3979),u=a(784),d=i.forwardRef((e,n)=>(0,u.jsx)(s.WV.span,{...e,ref:n,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));d.displayName="VisuallyHidden";var f=d},3162:(e,n,a)=>{"use strict";a.d(n,{S:()=>_});var i="undefined"==typeof window||"Deno"in globalThis;function noop(){}function resolveStaleTime(e,n){return"function"==typeof e?e(n):e}function matchQuery(e,n){let{type:a="all",exact:i,fetchStatus:s,predicate:u,queryKey:d,stale:f}=e;if(d){if(i){if(n.queryHash!==hashQueryKeyByOptions(d,n.options))return!1}else if(!partialMatchKey(n.queryKey,d))return!1}if("all"!==a){let e=n.isActive();if("active"===a&&!e||"inactive"===a&&e)return!1}return("boolean"!=typeof f||n.isStale()===f)&&(!s||s===n.state.fetchStatus)&&(!u||!!u(n))}function matchMutation(e,n){let{exact:a,status:i,predicate:s,mutationKey:u}=e;if(u){if(!n.options.mutationKey)return!1;if(a){if(hashKey(n.options.mutationKey)!==hashKey(u))return!1}else if(!partialMatchKey(n.options.mutationKey,u))return!1}return(!i||n.state.status===i)&&(!s||!!s(n))}function hashQueryKeyByOptions(e,n){let a=n?.queryKeyHashFn||hashKey;return a(e)}function hashKey(e){return JSON.stringify(e,(e,n)=>isPlainObject(n)?Object.keys(n).sort().reduce((e,a)=>(e[a]=n[a],e),{}):n)}function partialMatchKey(e,n){return e===n||typeof e==typeof n&&!!e&&!!n&&"object"==typeof e&&"object"==typeof n&&!Object.keys(n).some(a=>!partialMatchKey(e[a],n[a]))}function isPlainArray(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function isPlainObject(e){if(!hasObjectPrototype(e))return!1;let n=e.constructor;if(void 0===n)return!0;let a=n.prototype;return!!(hasObjectPrototype(a)&&a.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function hasObjectPrototype(e){return"[object Object]"===Object.prototype.toString.call(e)}function addToEnd(e,n,a=0){let i=[...e,n];return a&&i.length>a?i.slice(1):i}function addToStart(e,n,a=0){let i=[n,...e];return a&&i.length>a?i.slice(0,-1):i}var s=Symbol();function ensureQueryFn(e,n){return!e.queryFn&&n?.initialPromise?()=>n.initialPromise:e.queryFn&&e.queryFn!==s?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var u=function(){let e=[],n=0,notifyFn=e=>{e()},batchNotifyFn=e=>{e()},scheduleFn=e=>setTimeout(e,0),schedule=a=>{n?e.push(a):scheduleFn(()=>{notifyFn(a)})},flush=()=>{let n=e;e=[],n.length&&scheduleFn(()=>{batchNotifyFn(()=>{n.forEach(e=>{notifyFn(e)})})})};return{batch:e=>{let a;n++;try{a=e()}finally{--n||flush()}return a},batchCalls:e=>(...n)=>{schedule(()=>{e(...n)})},schedule,setNotifyFunction:e=>{notifyFn=e},setBatchNotifyFunction:e=>{batchNotifyFn=e},setScheduler:e=>{scheduleFn=e}}}(),d=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},f=new class extends d{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!i&&window.addEventListener){let listener=()=>e();return window.addEventListener("visibilitychange",listener,!1),()=>{window.removeEventListener("visibilitychange",listener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){let n=this.#e!==e;n&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(n=>{n(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},p=new class extends d{#n=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!i&&window.addEventListener){let onlineListener=()=>e(!0),offlineListener=()=>e(!1);return window.addEventListener("online",onlineListener,!1),window.addEventListener("offline",offlineListener,!1),()=>{window.removeEventListener("online",onlineListener),window.removeEventListener("offline",offlineListener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){let n=this.#n!==e;n&&(this.#n=e,this.listeners.forEach(n=>{n(e)}))}isOnline(){return this.#n}};function defaultRetryDelay(e){return Math.min(1e3*2**e,3e4)}function canFetch(e){return(e??"online")!=="online"||p.isOnline()}var h=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function isCancelledError(e){return e instanceof h}function createRetryer(e){let n,a=!1,s=0,u=!1,d=function(){let e,n;let a=new Promise((a,i)=>{e=a,n=i});function finalize(e){Object.assign(a,e),delete a.resolve,delete a.reject}return a.status="pending",a.catch(()=>{}),a.resolve=n=>{finalize({status:"fulfilled",value:n}),e(n)},a.reject=e=>{finalize({status:"rejected",reason:e}),n(e)},a}(),canContinue=()=>f.isFocused()&&("always"===e.networkMode||p.isOnline())&&e.canRun(),canStart=()=>canFetch(e.networkMode)&&e.canRun(),resolve=a=>{u||(u=!0,e.onSuccess?.(a),n?.(),d.resolve(a))},reject=a=>{u||(u=!0,e.onError?.(a),n?.(),d.reject(a))},pause=()=>new Promise(a=>{n=e=>{(u||canContinue())&&a(e)},e.onPause?.()}).then(()=>{n=void 0,u||e.onContinue?.()}),run=()=>{let n;if(u)return;let d=0===s?e.initialPromise:void 0;try{n=d??e.fn()}catch(e){n=Promise.reject(e)}Promise.resolve(n).then(resolve).catch(n=>{if(u)return;let d=e.retry??(i?0:3),f=e.retryDelay??defaultRetryDelay,p="function"==typeof f?f(s,n):f,h=!0===d||"number"==typeof d&&s<d||"function"==typeof d&&d(s,n);if(a||!h){reject(n);return}s++,e.onFail?.(s,n),new Promise(e=>{setTimeout(e,p)}).then(()=>canContinue()?void 0:pause()).then(()=>{a?reject(n):run()})})};return{promise:d,cancel:n=>{u||(reject(new h(n)),e.abort?.())},continue:()=>(n?.(),d),cancelRetry:()=>{a=!0},continueRetry:()=>{a=!1},canStart,start:()=>(canStart()?run():pause().then(run),d)}}var m=class{#o;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#o=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(i?1/0:3e5))}clearGcTimeout(){this.#o&&(clearTimeout(this.#o),this.#o=void 0)}},g=class extends m{#a;#i;#l;#s;#u;#c;constructor(e){super(),this.#c=!1,this.#u=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#a=function(e){let n="function"==typeof e.initialData?e.initialData():e.initialData,a=void 0!==n,i=a?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:n,dataUpdateCount:0,dataUpdatedAt:a?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:a?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#a,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#s?.promise}setOptions(e){this.options={...this.#u,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#l.remove(this)}setData(e,n){var a,i;let s=(a=this.state.data,"function"==typeof(i=this.options).structuralSharing?i.structuralSharing(a,e):!1!==i.structuralSharing?function replaceEqualDeep(e,n){if(e===n)return e;let a=isPlainArray(e)&&isPlainArray(n);if(a||isPlainObject(e)&&isPlainObject(n)){let i=a?e:Object.keys(e),s=i.length,u=a?n:Object.keys(n),d=u.length,f=a?[]:{},p=0;for(let s=0;s<d;s++){let d=a?s:u[s];(!a&&i.includes(d)||a)&&void 0===e[d]&&void 0===n[d]?(f[d]=void 0,p++):(f[d]=replaceEqualDeep(e[d],n[d]),f[d]===e[d]&&void 0!==e[d]&&p++)}return s===d&&p===s?e:f}return n}(a,e):e);return this.#d({data:s,type:"success",dataUpdatedAt:n?.updatedAt,manual:n?.manual}),s}setState(e,n){this.#d({type:"setState",state:e,setStateOptions:n})}cancel(e){let n=this.#s?.promise;return this.#s?.cancel(e),n?n.then(noop).catch(noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#a)}isActive(){return this.observers.some(e=>{var n;return!1!==("function"==typeof(n=e.options.enabled)?n(this):n)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===s||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#s?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#s?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#l.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(n=>n!==e),this.observers.length||(this.#s&&(this.#c?this.#s.cancel({revert:!0}):this.#s.cancelRetry()),this.scheduleGc()),this.#l.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#d({type:"invalidate"})}fetch(e,n){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&n?.cancelRefetch)this.cancel({silent:!0});else if(this.#s)return this.#s.continueRetry(),this.#s.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let a=new AbortController,addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#c=!0,a.signal)})},i={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{let e=ensureQueryFn(this.options,n),a={queryKey:this.queryKey,meta:this.meta};return(addSignalProperty(a),this.#c=!1,this.options.persister)?this.options.persister(e,a,this):e(a)}};addSignalProperty(i),this.options.behavior?.onFetch(i,this),this.#i=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==i.fetchOptions?.meta)&&this.#d({type:"fetch",meta:i.fetchOptions?.meta});let onError=e=>{isCancelledError(e)&&e.silent||this.#d({type:"error",error:e}),isCancelledError(e)||(this.#l.config.onError?.(e,this),this.#l.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#s=createRetryer({initialPromise:n?.initialPromise,fn:i.fetchFn,abort:a.abort.bind(a),onSuccess:e=>{if(void 0===e){onError(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){onError(e);return}this.#l.config.onSuccess?.(e,this),this.#l.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError,onFail:(e,n)=>{this.#d({type:"failed",failureCount:e,error:n})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#s.start()}#d(e){this.state=(n=>{switch(e.type){case"failed":return{...n,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...n,fetchStatus:"paused"};case"continue":return{...n,fetchStatus:"fetching"};case"fetch":var a;return{...n,...(a=n.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:canFetch(this.options.networkMode)?"fetching":"paused",...void 0===a&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...n,data:e.data,dataUpdateCount:n.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let i=e.error;if(isCancelledError(i)&&i.revert&&this.#i)return{...this.#i,fetchStatus:"idle"};return{...n,error:i,errorUpdateCount:n.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:n.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...n,isInvalidated:!0};case"setState":return{...n,...e.state}}})(this.state),u.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#l.notify({query:this,type:"updated",action:e})})}},v=class extends d{constructor(e={}){super(),this.config=e,this.#f=new Map}#f;build(e,n,a){let i=n.queryKey,s=n.queryHash??hashQueryKeyByOptions(i,n),u=this.get(s);return u||(u=new g({cache:this,queryKey:i,queryHash:s,options:e.defaultQueryOptions(n),state:a,defaultOptions:e.getQueryDefaults(i)}),this.add(u)),u}add(e){this.#f.has(e.queryHash)||(this.#f.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let n=this.#f.get(e.queryHash);n&&(e.destroy(),n===e&&this.#f.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){u.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#f.get(e)}getAll(){return[...this.#f.values()]}find(e){let n={exact:!0,...e};return this.getAll().find(e=>matchQuery(n,e))}findAll(e={}){let n=this.getAll();return Object.keys(e).length>0?n.filter(n=>matchQuery(e,n)):n}notify(e){u.batch(()=>{this.listeners.forEach(n=>{n(e)})})}onFocus(){u.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){u.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},y=class extends m{#p;#h;#s;constructor(e){super(),this.mutationId=e.mutationId,this.#h=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#h.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(n=>n!==e),this.scheduleGc(),this.#h.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#h.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(e){this.#s=createRetryer({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,n)=>{this.#d({type:"failed",failureCount:e,error:n})},onPause:()=>{this.#d({type:"pause"})},onContinue:()=>{this.#d({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#h.canRun(this)});let n="pending"===this.state.status,a=!this.#s.canStart();try{if(!n){this.#d({type:"pending",variables:e,isPaused:a}),await this.#h.config.onMutate?.(e,this);let n=await this.options.onMutate?.(e);n!==this.state.context&&this.#d({type:"pending",context:n,variables:e,isPaused:a})}let i=await this.#s.start();return await this.#h.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#h.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#d({type:"success",data:i}),i}catch(n){try{throw await this.#h.config.onError?.(n,e,this.state.context,this),await this.options.onError?.(n,e,this.state.context),await this.#h.config.onSettled?.(void 0,n,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,n,e,this.state.context),n}finally{this.#d({type:"error",error:n})}}finally{this.#h.runNext(this)}}#d(e){this.state=(n=>{switch(e.type){case"failed":return{...n,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...n,isPaused:!0};case"continue":return{...n,isPaused:!1};case"pending":return{...n,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...n,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...n,data:void 0,error:e.error,failureCount:n.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),u.batch(()=>{this.#p.forEach(n=>{n.onMutationUpdate(e)}),this.#h.notify({mutation:this,type:"updated",action:e})})}},b=class extends d{constructor(e={}){super(),this.config=e,this.#m=new Map,this.#g=Date.now()}#m;#g;build(e,n,a){let i=new y({mutationCache:this,mutationId:++this.#g,options:e.defaultMutationOptions(n),state:a});return this.add(i),i}add(e){let n=scopeFor(e),a=this.#m.get(n)??[];a.push(e),this.#m.set(n,a),this.notify({type:"added",mutation:e})}remove(e){let n=scopeFor(e);if(this.#m.has(n)){let a=this.#m.get(n)?.filter(n=>n!==e);a&&(0===a.length?this.#m.delete(n):this.#m.set(n,a))}this.notify({type:"removed",mutation:e})}canRun(e){let n=this.#m.get(scopeFor(e))?.find(e=>"pending"===e.state.status);return!n||n===e}runNext(e){let n=this.#m.get(scopeFor(e))?.find(n=>n!==e&&n.state.isPaused);return n?.continue()??Promise.resolve()}clear(){u.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}getAll(){return[...this.#m.values()].flat()}find(e){let n={exact:!0,...e};return this.getAll().find(e=>matchMutation(n,e))}findAll(e={}){return this.getAll().filter(n=>matchMutation(e,n))}notify(e){u.batch(()=>{this.listeners.forEach(n=>{n(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return u.batch(()=>Promise.all(e.map(e=>e.continue().catch(noop))))}};function scopeFor(e){return e.options.scope?.id??String(e.mutationId)}function infiniteQueryBehavior(e){return{onFetch:(n,a)=>{let i=n.options,s=n.fetchOptions?.meta?.fetchMore?.direction,u=n.state.data?.pages||[],d=n.state.data?.pageParams||[],f={pages:[],pageParams:[]},p=0,fetchFn=async()=>{let a=!1,addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(n.signal.aborted?a=!0:n.signal.addEventListener("abort",()=>{a=!0}),n.signal)})},h=ensureQueryFn(n.options,n.fetchOptions),fetchPage=async(e,i,s)=>{if(a)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let u={queryKey:n.queryKey,pageParam:i,direction:s?"backward":"forward",meta:n.options.meta};addSignalProperty(u);let d=await h(u),{maxPages:f}=n.options,p=s?addToStart:addToEnd;return{pages:p(e.pages,d,f),pageParams:p(e.pageParams,i,f)}};if(s&&u.length){let e="backward"===s,n=e?getPreviousPageParam:getNextPageParam,a={pages:u,pageParams:d},p=n(i,a);f=await fetchPage(a,p,e)}else{let n=e??u.length;do{let e=0===p?d[0]??i.initialPageParam:getNextPageParam(i,f);if(p>0&&null==e)break;f=await fetchPage(f,e),p++}while(p<n)}return f};n.options.persister?n.fetchFn=()=>n.options.persister?.(fetchFn,{queryKey:n.queryKey,meta:n.options.meta,signal:n.signal},a):n.fetchFn=fetchFn}}}function getNextPageParam(e,{pages:n,pageParams:a}){let i=n.length-1;return n.length>0?e.getNextPageParam(n[i],n,a[i],a):void 0}function getPreviousPageParam(e,{pages:n,pageParams:a}){return n.length>0?e.getPreviousPageParam?.(n[0],n,a[0],a):void 0}var _=class{#v;#h;#u;#y;#b;#_;#w;#x;constructor(e={}){this.#v=e.queryCache||new v,this.#h=e.mutationCache||new b,this.#u=e.defaultOptions||{},this.#y=new Map,this.#b=new Map,this.#_=0}mount(){this.#_++,1===this.#_&&(this.#w=f.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#v.onFocus())}),this.#x=p.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#v.onOnline())}))}unmount(){this.#_--,0===this.#_&&(this.#w?.(),this.#w=void 0,this.#x?.(),this.#x=void 0)}isFetching(e){return this.#v.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#h.findAll({...e,status:"pending"}).length}getQueryData(e){let n=this.defaultQueryOptions({queryKey:e});return this.#v.get(n.queryHash)?.state.data}ensureQueryData(e){let n=this.getQueryData(e.queryKey);if(void 0===n)return this.fetchQuery(e);{let a=this.defaultQueryOptions(e),i=this.#v.build(this,a);return e.revalidateIfStale&&i.isStaleByTime(resolveStaleTime(a.staleTime,i))&&this.prefetchQuery(a),Promise.resolve(n)}}getQueriesData(e){return this.#v.findAll(e).map(({queryKey:e,state:n})=>{let a=n.data;return[e,a]})}setQueryData(e,n,a){let i=this.defaultQueryOptions({queryKey:e}),s=this.#v.get(i.queryHash),u=s?.state.data,d="function"==typeof n?n(u):n;if(void 0!==d)return this.#v.build(this,i).setData(d,{...a,manual:!0})}setQueriesData(e,n,a){return u.batch(()=>this.#v.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,n,a)]))}getQueryState(e){let n=this.defaultQueryOptions({queryKey:e});return this.#v.get(n.queryHash)?.state}removeQueries(e){let n=this.#v;u.batch(()=>{n.findAll(e).forEach(e=>{n.remove(e)})})}resetQueries(e,n){let a=this.#v,i={type:"active",...e};return u.batch(()=>(a.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries(i,n)))}cancelQueries(e={},n={}){let a={revert:!0,...n},i=u.batch(()=>this.#v.findAll(e).map(e=>e.cancel(a)));return Promise.all(i).then(noop).catch(noop)}invalidateQueries(e={},n={}){return u.batch(()=>{if(this.#v.findAll(e).forEach(e=>{e.invalidate()}),"none"===e.refetchType)return Promise.resolve();let a={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(a,n)})}refetchQueries(e={},n){let a={...n,cancelRefetch:n?.cancelRefetch??!0},i=u.batch(()=>this.#v.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let n=e.fetch(void 0,a);return a.throwOnError||(n=n.catch(noop)),"paused"===e.state.fetchStatus?Promise.resolve():n}));return Promise.all(i).then(noop)}fetchQuery(e){let n=this.defaultQueryOptions(e);void 0===n.retry&&(n.retry=!1);let a=this.#v.build(this,n);return a.isStaleByTime(resolveStaleTime(n.staleTime,a))?a.fetch(n):Promise.resolve(a.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(noop).catch(noop)}fetchInfiniteQuery(e){return e.behavior=infiniteQueryBehavior(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(noop).catch(noop)}ensureInfiniteQueryData(e){return e.behavior=infiniteQueryBehavior(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return p.isOnline()?this.#h.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#v}getMutationCache(){return this.#h}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,n){this.#y.set(hashKey(e),{queryKey:e,defaultOptions:n})}getQueryDefaults(e){let n=[...this.#y.values()],a={};return n.forEach(n=>{partialMatchKey(e,n.queryKey)&&(a={...a,...n.defaultOptions})}),a}setMutationDefaults(e,n){this.#b.set(hashKey(e),{mutationKey:e,defaultOptions:n})}getMutationDefaults(e){let n=[...this.#b.values()],a={};return n.forEach(n=>{partialMatchKey(e,n.mutationKey)&&(a={...a,...n.defaultOptions})}),a}defaultQueryOptions(e){if(e._defaulted)return e;let n={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return n.queryHash||(n.queryHash=hashQueryKeyByOptions(n.queryKey,n)),void 0===n.refetchOnReconnect&&(n.refetchOnReconnect="always"!==n.networkMode),void 0===n.throwOnError&&(n.throwOnError=!!n.suspense),!n.networkMode&&n.persister&&(n.networkMode="offlineFirst"),!0!==n.enabled&&n.queryFn===s&&(n.enabled=!1),n}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#v.clear(),this.#h.clear()}}},4070:(e,n,a)=>{"use strict";a.d(n,{aH:()=>QueryClientProvider});var i=a(9885),s=a(784),u=i.createContext(void 0),QueryClientProvider=({client:e,children:n})=>(i.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,s.jsx)(u.Provider,{value:e,children:n}))},1971:(e,n,a)=>{"use strict";a.d(n,{j:()=>cva});var i=a(566);let falsyToString=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=i.W,cva=(e,n)=>a=>{var i;if((null==n?void 0:n.variants)==null)return s(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:u,defaultVariants:d}=n,f=Object.keys(u).map(e=>{let n=null==a?void 0:a[e],i=null==d?void 0:d[e];if(null===n)return null;let s=falsyToString(n)||falsyToString(i);return u[e][s]}),p=a&&Object.entries(a).reduce((e,n)=>{let[a,i]=n;return void 0===i||(e[a]=i),e},{}),h=null==n?void 0:null===(i=n.compoundVariants)||void 0===i?void 0:i.reduce((e,n)=>{let{class:a,className:i,...s}=n;return Object.entries(s).every(e=>{let[n,a]=e;return Array.isArray(a)?a.includes({...d,...p}[n]):({...d,...p})[n]===a})?[...e,a,i]:e},[]);return s(e,f,h,null==a?void 0:a.class,null==a?void 0:a.className)}},566:(e,n,a)=>{"use strict";function clsx(){for(var e,n,a=0,i="",s=arguments.length;a<s;a++)(e=arguments[a])&&(n=function r(e){var n,a,i="";if("string"==typeof e||"number"==typeof e)i+=e;else if("object"==typeof e){if(Array.isArray(e)){var s=e.length;for(n=0;n<s;n++)e[n]&&(a=r(e[n]))&&(i&&(i+=" "),i+=a)}else for(a in e)e[a]&&(i&&(i+=" "),i+=a)}return i}(e))&&(i&&(i+=" "),i+=n);return i}a.d(n,{W:()=>clsx})},9941:(e,n,a)=>{"use strict";a.d(n,{x7:()=>Te});var i=a(9885),s=a(8908),Ct=e=>{switch(e){case"success":return d;case"info":return p;case"warning":return f;case"error":return h;default:return null}},u=Array(12).fill(0),It=({visible:e})=>i.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},i.createElement("div",{className:"sonner-spinner"},u.map((e,n)=>i.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),d=i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},i.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),f=i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},i.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),p=i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},i.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),h=i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},i.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),Dt=()=>{let[e,n]=i.useState(document.hidden);return i.useEffect(()=>{let t=()=>{n(document.hidden)};return document.addEventListener("visibilitychange",t),()=>window.removeEventListener("visibilitychange",t)},[]),e},m=1,g=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let n=this.subscribers.indexOf(e);this.subscribers.splice(n,1)}),this.publish=e=>{this.subscribers.forEach(n=>n(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var n;let{message:a,...i}=e,s="number"==typeof(null==e?void 0:e.id)||(null==(n=e.id)?void 0:n.length)>0?e.id:m++,u=this.toasts.find(e=>e.id===s),d=void 0===e.dismissible||e.dismissible;return u?this.toasts=this.toasts.map(n=>n.id===s?(this.publish({...n,...e,id:s,title:a}),{...n,...e,id:s,dismissible:d,title:a}):n):this.addToast({title:a,...i,dismissible:d,id:s}),s},this.dismiss=e=>(e||this.toasts.forEach(e=>{this.subscribers.forEach(n=>n({id:e.id,dismiss:!0}))}),this.subscribers.forEach(n=>n({id:e,dismiss:!0})),e),this.message=(e,n)=>this.create({...n,message:e}),this.error=(e,n)=>this.create({...n,message:e,type:"error"}),this.success=(e,n)=>this.create({...n,type:"success",message:e}),this.info=(e,n)=>this.create({...n,type:"info",message:e}),this.warning=(e,n)=>this.create({...n,type:"warning",message:e}),this.loading=(e,n)=>this.create({...n,type:"loading",message:e}),this.promise=(e,n)=>{let a;if(!n)return;void 0!==n.loading&&(a=this.create({...n,promise:e,type:"loading",message:n.loading,description:"function"!=typeof n.description?n.description:void 0}));let i=e instanceof Promise?e:e(),s=void 0!==a;return i.then(async e=>{if(Ot(e)&&!e.ok){s=!1;let i="function"==typeof n.error?await n.error(`HTTP error! status: ${e.status}`):n.error,u="function"==typeof n.description?await n.description(`HTTP error! status: ${e.status}`):n.description;this.create({id:a,type:"error",message:i,description:u})}else if(void 0!==n.success){s=!1;let i="function"==typeof n.success?await n.success(e):n.success,u="function"==typeof n.description?await n.description(e):n.description;this.create({id:a,type:"success",message:i,description:u})}}).catch(async e=>{if(void 0!==n.error){s=!1;let i="function"==typeof n.error?await n.error(e):n.error,u="function"==typeof n.description?await n.description(e):n.description;this.create({id:a,type:"error",message:i,description:u})}}).finally(()=>{var e;s&&(this.dismiss(a),a=void 0),null==(e=n.finally)||e.call(n)}),a},this.custom=(e,n)=>{let a=(null==n?void 0:n.id)||m++;return this.create({jsx:e(a),id:a,...n}),a},this.subscribers=[],this.toasts=[]}},Ot=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status;function U(e){return void 0!==e.label}function ne(...e){return e.filter(Boolean).join(" ")}Object.assign((e,n)=>{let a=(null==n?void 0:n.id)||m++;return g.addToast({title:e,...n,id:a}),a},{success:g.success,info:g.info,warning:g.warning,error:g.error,custom:g.custom,message:g.message,promise:g.promise,dismiss:g.dismiss,loading:g.loading},{getHistory:()=>g.toasts}),function(e,{insertAt:n}={}){if(!e||"undefined"==typeof document)return;let a=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css","top"===n&&a.firstChild?a.insertBefore(i,a.firstChild):a.appendChild(i),i.styleSheet?i.styleSheet.cssText=e:i.appendChild(document.createTextNode(e))}(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);var se=e=>{var n,a,s,u,d,f,p,h,m,g;let{invert:v,toast:y,unstyled:b,interacting:_,setHeights:w,visibleToasts:x,heights:E,index:S,toasts:P,expanded:R,removeToast:C,defaultRichColors:O,closeButton:j,style:M,cancelButtonStyle:N,actionButtonStyle:D,className:k="",descriptionClassName:L="",duration:F,position:W,gap:H,loadingIcon:V,expandByDefault:z,classNames:K,icons:$,closeButtonAriaLabel:Y="Close toast",pauseWhenPageIsHidden:Q,cn:Z}=e,[J,ee]=i.useState(!1),[et,er]=i.useState(!1),[en,eo]=i.useState(!1),[ea,ei]=i.useState(!1),[el,es]=i.useState(0),[eu,ec]=i.useState(0),ed=i.useRef(null),ef=i.useRef(null),ep=y.type,eh=!1!==y.dismissible,em=y.className||"",eg=y.descriptionClassName||"",ev=i.useMemo(()=>E.findIndex(e=>e.toastId===y.id)||0,[E,y.id]),ey=i.useMemo(()=>{var e;return null!=(e=y.closeButton)?e:j},[y.closeButton,j]),eb=i.useMemo(()=>y.duration||F||4e3,[y.duration,F]),e_=i.useRef(0),ew=i.useRef(0),ex=i.useRef(0),eE=i.useRef(null),[eS,eP]=W.split("-"),eR=i.useMemo(()=>E.reduce((e,n,a)=>a>=ev?e:e+n.height,0),[E,ev]),eC=Dt(),eO=y.invert||v,eT="loading"===ep;ew.current=i.useMemo(()=>ev*H+eR,[ev,eR]),i.useEffect(()=>{ee(!0)},[]),i.useLayoutEffect(()=>{if(!J)return;let e=ef.current,n=e.style.height;e.style.height="auto";let a=e.getBoundingClientRect().height;e.style.height=n,ec(a),w(e=>e.find(e=>e.toastId===y.id)?e.map(e=>e.toastId===y.id?{...e,height:a}:e):[{toastId:y.id,height:a,position:y.position},...e])},[J,y.title,y.description,w,y.id]);let ej=i.useCallback(()=>{er(!0),es(ew.current),w(e=>e.filter(e=>e.toastId!==y.id)),setTimeout(()=>{C(y)},200)},[y,C,w,ew]);return i.useEffect(()=>{if(y.promise&&"loading"===ep||y.duration===1/0||"loading"===y.type)return;let e,n=eb;return R||_||Q&&eC?(()=>{if(ex.current<e_.current){let e=new Date().getTime()-e_.current;n-=e}ex.current=new Date().getTime()})():n!==1/0&&(e_.current=new Date().getTime(),e=setTimeout(()=>{var e;null==(e=y.onAutoClose)||e.call(y,y),ej()},n)),()=>clearTimeout(e)},[R,_,z,y,eb,ej,y.promise,ep,Q,eC]),i.useEffect(()=>{let e=ef.current;if(e){let n=e.getBoundingClientRect().height;return ec(n),w(e=>[{toastId:y.id,height:n,position:y.position},...e]),()=>w(e=>e.filter(e=>e.toastId!==y.id))}},[w,y.id]),i.useEffect(()=>{y.delete&&ej()},[ej,y.delete]),i.createElement("li",{"aria-live":y.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:ef,className:Z(k,em,null==K?void 0:K.toast,null==(n=null==y?void 0:y.classNames)?void 0:n.toast,null==K?void 0:K.default,null==K?void 0:K[ep],null==(a=null==y?void 0:y.classNames)?void 0:a[ep]),"data-sonner-toast":"","data-rich-colors":null!=(s=y.richColors)?s:O,"data-styled":!(y.jsx||y.unstyled||b),"data-mounted":J,"data-promise":!!y.promise,"data-removed":et,"data-visible":S+1<=x,"data-y-position":eS,"data-x-position":eP,"data-index":S,"data-front":0===S,"data-swiping":en,"data-dismissible":eh,"data-type":ep,"data-invert":eO,"data-swipe-out":ea,"data-expanded":!!(R||z&&J),style:{"--index":S,"--toasts-before":S,"--z-index":P.length-S,"--offset":`${et?el:ew.current}px`,"--initial-height":z?"auto":`${eu}px`,...M,...y.style},onPointerDown:e=>{eT||!eh||(ed.current=new Date,es(ew.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(eo(!0),eE.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,n,a,i;if(ea||!eh)return;eE.current=null;let s=Number((null==(e=ef.current)?void 0:e.style.getPropertyValue("--swipe-amount").replace("px",""))||0),u=new Date().getTime()-(null==(n=ed.current)?void 0:n.getTime());if(Math.abs(s)>=20||Math.abs(s)/u>.11){es(ew.current),null==(a=y.onDismiss)||a.call(y,y),ej(),ei(!0);return}null==(i=ef.current)||i.style.setProperty("--swipe-amount","0px"),eo(!1)},onPointerMove:e=>{var n;if(!eE.current||!eh)return;let a=e.clientY-eE.current.y,i=e.clientX-eE.current.x,s=("top"===eS?Math.min:Math.max)(0,a),u="touch"===e.pointerType?10:2;Math.abs(s)>u?null==(n=ef.current)||n.style.setProperty("--swipe-amount",`${a}px`):Math.abs(i)>u&&(eE.current=null)}},ey&&!y.jsx?i.createElement("button",{"aria-label":Y,"data-disabled":eT,"data-close-button":!0,onClick:eT||!eh?()=>{}:()=>{var e;ej(),null==(e=y.onDismiss)||e.call(y,y)},className:Z(null==K?void 0:K.closeButton,null==(u=null==y?void 0:y.classNames)?void 0:u.closeButton)},i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},i.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),i.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,y.jsx||i.isValidElement(y.title)?y.jsx||y.title:i.createElement(i.Fragment,null,ep||y.icon||y.promise?i.createElement("div",{"data-icon":"",className:Z(null==K?void 0:K.icon,null==(d=null==y?void 0:y.classNames)?void 0:d.icon)},y.promise||"loading"===y.type&&!y.icon?y.icon||(null!=$&&$.loading?i.createElement("div",{className:"sonner-loader","data-visible":"loading"===ep},$.loading):V?i.createElement("div",{className:"sonner-loader","data-visible":"loading"===ep},V):i.createElement(It,{visible:"loading"===ep})):null,"loading"!==y.type?y.icon||(null==$?void 0:$[ep])||Ct(ep):null):null,i.createElement("div",{"data-content":"",className:Z(null==K?void 0:K.content,null==(f=null==y?void 0:y.classNames)?void 0:f.content)},i.createElement("div",{"data-title":"",className:Z(null==K?void 0:K.title,null==(p=null==y?void 0:y.classNames)?void 0:p.title)},y.title),y.description?i.createElement("div",{"data-description":"",className:Z(L,eg,null==K?void 0:K.description,null==(h=null==y?void 0:y.classNames)?void 0:h.description)},y.description):null),i.isValidElement(y.cancel)?y.cancel:y.cancel&&U(y.cancel)?i.createElement("button",{"data-button":!0,"data-cancel":!0,style:y.cancelButtonStyle||N,onClick:e=>{var n,a;U(y.cancel)&&eh&&(null==(a=(n=y.cancel).onClick)||a.call(n,e),ej())},className:Z(null==K?void 0:K.cancelButton,null==(m=null==y?void 0:y.classNames)?void 0:m.cancelButton)},y.cancel.label):null,i.isValidElement(y.action)?y.action:y.action&&U(y.action)?i.createElement("button",{"data-button":!0,"data-action":!0,style:y.actionButtonStyle||D,onClick:e=>{var n,a;U(y.action)&&(e.defaultPrevented||(null==(a=(n=y.action).onClick)||a.call(n,e),ej()))},className:Z(null==K?void 0:K.actionButton,null==(g=null==y?void 0:y.classNames)?void 0:g.actionButton)},y.action.label):null))};function Ht(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let e=document.documentElement.getAttribute("dir");return"auto"!==e&&e?e:window.getComputedStyle(document.documentElement).direction}var Te=e=>{let{invert:n,position:a="bottom-right",hotkey:u=["altKey","KeyT"],expand:d,closeButton:f,className:p,offset:h,theme:m="light",richColors:v,duration:y,style:b,visibleToasts:_=3,toastOptions:w,dir:x=Ht(),gap:E=14,loadingIcon:S,icons:P,containerAriaLabel:R="Notifications",pauseWhenPageIsHidden:C,cn:O=ne}=e,[j,M]=i.useState([]),N=i.useMemo(()=>Array.from(new Set([a].concat(j.filter(e=>e.position).map(e=>e.position)))),[j,a]),[D,k]=i.useState([]),[L,F]=i.useState(!1),[W,H]=i.useState(!1),[V,z]=i.useState("system"!==m?m:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),K=i.useRef(null),$=u.join("+").replace(/Key/g,"").replace(/Digit/g,""),Y=i.useRef(null),Q=i.useRef(!1),Z=i.useCallback(e=>{var n;null!=(n=j.find(n=>n.id===e.id))&&n.delete||g.dismiss(e.id),M(n=>n.filter(({id:n})=>n!==e.id))},[j]);return i.useEffect(()=>g.subscribe(e=>{if(e.dismiss){M(n=>n.map(n=>n.id===e.id?{...n,delete:!0}:n));return}setTimeout(()=>{s.flushSync(()=>{M(n=>{let a=n.findIndex(n=>n.id===e.id);return -1!==a?[...n.slice(0,a),{...n[a],...e},...n.slice(a+1)]:[e,...n]})})})}),[]),i.useEffect(()=>{if("system"!==m){z(m);return}"system"===m&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?z("dark"):z("light")),"undefined"!=typeof window&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:e})=>{z(e?"dark":"light")})},[m]),i.useEffect(()=>{j.length<=1&&F(!1)},[j]),i.useEffect(()=>{let c=e=>{var n,a;u.every(n=>e[n]||e.code===n)&&(F(!0),null==(n=K.current)||n.focus()),"Escape"===e.code&&(document.activeElement===K.current||null!=(a=K.current)&&a.contains(document.activeElement))&&F(!1)};return document.addEventListener("keydown",c),()=>document.removeEventListener("keydown",c)},[u]),i.useEffect(()=>{if(K.current)return()=>{Y.current&&(Y.current.focus({preventScroll:!0}),Y.current=null,Q.current=!1)}},[K.current]),j.length?i.createElement("section",{"aria-label":`${R} ${$}`,tabIndex:-1},N.map((e,a)=>{var s;let[u,m]=e.split("-");return i.createElement("ol",{key:e,dir:"auto"===x?Ht():x,tabIndex:-1,ref:K,className:p,"data-sonner-toaster":!0,"data-theme":V,"data-y-position":u,"data-x-position":m,style:{"--front-toast-height":`${(null==(s=D[0])?void 0:s.height)||0}px`,"--offset":"number"==typeof h?`${h}px`:h||"32px","--width":"356px","--gap":`${E}px`,...b},onBlur:e=>{Q.current&&!e.currentTarget.contains(e.relatedTarget)&&(Q.current=!1,Y.current&&(Y.current.focus({preventScroll:!0}),Y.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||Q.current||(Q.current=!0,Y.current=e.relatedTarget)},onMouseEnter:()=>F(!0),onMouseMove:()=>F(!0),onMouseLeave:()=>{W||F(!1)},onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||H(!0)},onPointerUp:()=>H(!1)},j.filter(n=>!n.position&&0===a||n.position===e).map((a,s)=>{var u,p;return i.createElement(se,{key:a.id,icons:P,index:s,toast:a,defaultRichColors:v,duration:null!=(u=null==w?void 0:w.duration)?u:y,className:null==w?void 0:w.className,descriptionClassName:null==w?void 0:w.descriptionClassName,invert:n,visibleToasts:_,closeButton:null!=(p=null==w?void 0:w.closeButton)?p:f,interacting:W,position:e,style:null==w?void 0:w.style,unstyled:null==w?void 0:w.unstyled,classNames:null==w?void 0:w.classNames,cancelButtonStyle:null==w?void 0:w.cancelButtonStyle,actionButtonStyle:null==w?void 0:w.actionButtonStyle,removeToast:Z,toasts:j.filter(e=>e.position==a.position),heights:D.filter(e=>e.position==a.position),setHeights:k,expandByDefault:d,gap:E,loadingIcon:S,expanded:L,pauseWhenPageIsHidden:C,cn:O})}))})):null}},8126:(e,n,a)=>{"use strict";a.d(n,{m6:()=>_});let createClassGroupUtils=e=>{let n=createClassMap(e),{conflictingClassGroups:a,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:e=>{let a=e.split("-");return""===a[0]&&1!==a.length&&a.shift(),getGroupRecursive(a,n)||getGroupIdForArbitraryProperty(e)},getConflictingClassGroupIds:(e,n)=>{let s=a[e]||[];return n&&i[e]?[...s,...i[e]]:s}}},getGroupRecursive=(e,n)=>{if(0===e.length)return n.classGroupId;let a=e[0],i=n.nextPart.get(a),s=i?getGroupRecursive(e.slice(1),i):void 0;if(s)return s;if(0===n.validators.length)return;let u=e.join("-");return n.validators.find(({validator:e})=>e(u))?.classGroupId},i=/^\[(.+)\]$/,getGroupIdForArbitraryProperty=e=>{if(i.test(e)){let n=i.exec(e)[1],a=n?.substring(0,n.indexOf(":"));if(a)return"arbitrary.."+a}},createClassMap=e=>{let{theme:n,prefix:a}=e,i={nextPart:new Map,validators:[]},s=getPrefixedClassGroupEntries(Object.entries(e.classGroups),a);return s.forEach(([e,a])=>{processClassesRecursively(a,i,e,n)}),i},processClassesRecursively=(e,n,a,i)=>{e.forEach(e=>{if("string"==typeof e){let i=""===e?n:getPart(n,e);i.classGroupId=a;return}if("function"==typeof e){if(isThemeGetter(e)){processClassesRecursively(e(i),n,a,i);return}n.validators.push({validator:e,classGroupId:a});return}Object.entries(e).forEach(([e,s])=>{processClassesRecursively(s,getPart(n,e),a,i)})})},getPart=(e,n)=>{let a=e;return n.split("-").forEach(e=>{a.nextPart.has(e)||a.nextPart.set(e,{nextPart:new Map,validators:[]}),a=a.nextPart.get(e)}),a},isThemeGetter=e=>e.isThemeGetter,getPrefixedClassGroupEntries=(e,n)=>n?e.map(([e,a])=>{let i=a.map(e=>"string"==typeof e?n+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,a])=>[n+e,a])):e);return[e,i]}):e,createLruCache=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let n=0,a=new Map,i=new Map,update=(s,u)=>{a.set(s,u),++n>e&&(n=0,i=a,a=new Map)};return{get(e){let n=a.get(e);return void 0!==n?n:void 0!==(n=i.get(e))?(update(e,n),n):void 0},set(e,n){a.has(e)?a.set(e,n):update(e,n)}}},createParseClassName=e=>{let{separator:n,experimentalParseClassName:a}=e,i=1===n.length,s=n[0],u=n.length,parseClassName=e=>{let a;let d=[],f=0,p=0;for(let h=0;h<e.length;h++){let m=e[h];if(0===f){if(m===s&&(i||e.slice(h,h+u)===n)){d.push(e.slice(p,h)),p=h+u;continue}if("/"===m){a=h;continue}}"["===m?f++:"]"===m&&f--}let h=0===d.length?e:e.substring(p),m=h.startsWith("!"),g=m?h.substring(1):h,v=a&&a>p?a-p:void 0;return{modifiers:d,hasImportantModifier:m,baseClassName:g,maybePostfixModifierPosition:v}};return a?e=>a({className:e,parseClassName}):parseClassName},sortModifiers=e=>{if(e.length<=1)return e;let n=[],a=[];return e.forEach(e=>{let i="["===e[0];i?(n.push(...a.sort(),e),a=[]):a.push(e)}),n.push(...a.sort()),n},createConfigUtils=e=>({cache:createLruCache(e.cacheSize),parseClassName:createParseClassName(e),...createClassGroupUtils(e)}),s=/\s+/,mergeClassList=(e,n)=>{let{parseClassName:a,getClassGroupId:i,getConflictingClassGroupIds:u}=n,d=[],f=e.trim().split(s),p="";for(let e=f.length-1;e>=0;e-=1){let n=f[e],{modifiers:s,hasImportantModifier:h,baseClassName:m,maybePostfixModifierPosition:g}=a(n),v=!!g,y=i(v?m.substring(0,g):m);if(!y){if(!v||!(y=i(m))){p=n+(p.length>0?" "+p:p);continue}v=!1}let b=sortModifiers(s).join(":"),_=h?b+"!":b,w=_+y;if(d.includes(w))continue;d.push(w);let x=u(y,v);for(let e=0;e<x.length;++e){let n=x[e];d.push(_+n)}p=n+(p.length>0?" "+p:p)}return p};function twJoin(){let e,n,a=0,i="";for(;a<arguments.length;)(e=arguments[a++])&&(n=toValue(e))&&(i&&(i+=" "),i+=n);return i}let toValue=e=>{let n;if("string"==typeof e)return e;let a="";for(let i=0;i<e.length;i++)e[i]&&(n=toValue(e[i]))&&(a&&(a+=" "),a+=n);return a},fromTheme=e=>{let themeGetter=n=>n[e]||[];return themeGetter.isThemeGetter=!0,themeGetter},u=/^\[(?:([a-z-]+):)?(.+)\]$/i,d=/^\d+\/\d+$/,f=new Set(["px","full","screen"]),p=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,h=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,m=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,g=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,v=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,isLength=e=>isNumber(e)||f.has(e)||d.test(e),isArbitraryLength=e=>getIsArbitraryValue(e,"length",isLengthOnly),isNumber=e=>!!e&&!Number.isNaN(Number(e)),isArbitraryNumber=e=>getIsArbitraryValue(e,"number",isNumber),isInteger=e=>!!e&&Number.isInteger(Number(e)),isPercent=e=>e.endsWith("%")&&isNumber(e.slice(0,-1)),isArbitraryValue=e=>u.test(e),isTshirtSize=e=>p.test(e),y=new Set(["length","size","percentage"]),isArbitrarySize=e=>getIsArbitraryValue(e,y,isNever),isArbitraryPosition=e=>getIsArbitraryValue(e,"position",isNever),b=new Set(["image","url"]),isArbitraryImage=e=>getIsArbitraryValue(e,b,isImage),isArbitraryShadow=e=>getIsArbitraryValue(e,"",isShadow),isAny=()=>!0,getIsArbitraryValue=(e,n,a)=>{let i=u.exec(e);return!!i&&(i[1]?"string"==typeof n?i[1]===n:n.has(i[1]):a(i[2]))},isLengthOnly=e=>h.test(e)&&!m.test(e),isNever=()=>!1,isShadow=e=>g.test(e),isImage=e=>v.test(e);Symbol.toStringTag;let _=function(e){let n,a,i;let functionToCall=function(s){let u=[].reduce((e,n)=>n(e),e());return a=(n=createConfigUtils(u)).cache.get,i=n.cache.set,functionToCall=tailwindMerge,tailwindMerge(s)};function tailwindMerge(e){let s=a(e);if(s)return s;let u=mergeClassList(e,n);return i(e,u),u}return function(){return functionToCall(twJoin.apply(null,arguments))}}(()=>{let e=fromTheme("colors"),n=fromTheme("spacing"),a=fromTheme("blur"),i=fromTheme("brightness"),s=fromTheme("borderColor"),u=fromTheme("borderRadius"),d=fromTheme("borderSpacing"),f=fromTheme("borderWidth"),p=fromTheme("contrast"),h=fromTheme("grayscale"),m=fromTheme("hueRotate"),g=fromTheme("invert"),v=fromTheme("gap"),y=fromTheme("gradientColorStops"),b=fromTheme("gradientColorStopPositions"),_=fromTheme("inset"),w=fromTheme("margin"),x=fromTheme("opacity"),E=fromTheme("padding"),S=fromTheme("saturate"),P=fromTheme("scale"),R=fromTheme("sepia"),C=fromTheme("skew"),O=fromTheme("space"),j=fromTheme("translate"),getOverscroll=()=>["auto","contain","none"],getOverflow=()=>["auto","hidden","clip","visible","scroll"],getSpacingWithAutoAndArbitrary=()=>["auto",isArbitraryValue,n],getSpacingWithArbitrary=()=>[isArbitraryValue,n],getLengthWithEmptyAndArbitrary=()=>["",isLength,isArbitraryLength],getNumberWithAutoAndArbitrary=()=>["auto",isNumber,isArbitraryValue],getPositions=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],getLineStyles=()=>["solid","dashed","dotted","double","none"],getBlendModes=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],getAlign=()=>["start","end","center","between","around","evenly","stretch"],getZeroAndEmpty=()=>["","0",isArbitraryValue],getBreaks=()=>["auto","avoid","all","avoid-page","page","left","right","column"],getNumberAndArbitrary=()=>[isNumber,isArbitraryValue];return{cacheSize:500,separator:":",theme:{colors:[isAny],spacing:[isLength,isArbitraryLength],blur:["none","",isTshirtSize,isArbitraryValue],brightness:getNumberAndArbitrary(),borderColor:[e],borderRadius:["none","","full",isTshirtSize,isArbitraryValue],borderSpacing:getSpacingWithArbitrary(),borderWidth:getLengthWithEmptyAndArbitrary(),contrast:getNumberAndArbitrary(),grayscale:getZeroAndEmpty(),hueRotate:getNumberAndArbitrary(),invert:getZeroAndEmpty(),gap:getSpacingWithArbitrary(),gradientColorStops:[e],gradientColorStopPositions:[isPercent,isArbitraryLength],inset:getSpacingWithAutoAndArbitrary(),margin:getSpacingWithAutoAndArbitrary(),opacity:getNumberAndArbitrary(),padding:getSpacingWithArbitrary(),saturate:getNumberAndArbitrary(),scale:getNumberAndArbitrary(),sepia:getZeroAndEmpty(),skew:getNumberAndArbitrary(),space:getSpacingWithArbitrary(),translate:getSpacingWithArbitrary()},classGroups:{aspect:[{aspect:["auto","square","video",isArbitraryValue]}],container:["container"],columns:[{columns:[isTshirtSize]}],"break-after":[{"break-after":getBreaks()}],"break-before":[{"break-before":getBreaks()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...getPositions(),isArbitraryValue]}],overflow:[{overflow:getOverflow()}],"overflow-x":[{"overflow-x":getOverflow()}],"overflow-y":[{"overflow-y":getOverflow()}],overscroll:[{overscroll:getOverscroll()}],"overscroll-x":[{"overscroll-x":getOverscroll()}],"overscroll-y":[{"overscroll-y":getOverscroll()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[_]}],"inset-x":[{"inset-x":[_]}],"inset-y":[{"inset-y":[_]}],start:[{start:[_]}],end:[{end:[_]}],top:[{top:[_]}],right:[{right:[_]}],bottom:[{bottom:[_]}],left:[{left:[_]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",isInteger,isArbitraryValue]}],basis:[{basis:getSpacingWithAutoAndArbitrary()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",isArbitraryValue]}],grow:[{grow:getZeroAndEmpty()}],shrink:[{shrink:getZeroAndEmpty()}],order:[{order:["first","last","none",isInteger,isArbitraryValue]}],"grid-cols":[{"grid-cols":[isAny]}],"col-start-end":[{col:["auto",{span:["full",isInteger,isArbitraryValue]},isArbitraryValue]}],"col-start":[{"col-start":getNumberWithAutoAndArbitrary()}],"col-end":[{"col-end":getNumberWithAutoAndArbitrary()}],"grid-rows":[{"grid-rows":[isAny]}],"row-start-end":[{row:["auto",{span:[isInteger,isArbitraryValue]},isArbitraryValue]}],"row-start":[{"row-start":getNumberWithAutoAndArbitrary()}],"row-end":[{"row-end":getNumberWithAutoAndArbitrary()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",isArbitraryValue]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",isArbitraryValue]}],gap:[{gap:[v]}],"gap-x":[{"gap-x":[v]}],"gap-y":[{"gap-y":[v]}],"justify-content":[{justify:["normal",...getAlign()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...getAlign(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...getAlign(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[E]}],px:[{px:[E]}],py:[{py:[E]}],ps:[{ps:[E]}],pe:[{pe:[E]}],pt:[{pt:[E]}],pr:[{pr:[E]}],pb:[{pb:[E]}],pl:[{pl:[E]}],m:[{m:[w]}],mx:[{mx:[w]}],my:[{my:[w]}],ms:[{ms:[w]}],me:[{me:[w]}],mt:[{mt:[w]}],mr:[{mr:[w]}],mb:[{mb:[w]}],ml:[{ml:[w]}],"space-x":[{"space-x":[O]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[O]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",isArbitraryValue,n]}],"min-w":[{"min-w":[isArbitraryValue,n,"min","max","fit"]}],"max-w":[{"max-w":[isArbitraryValue,n,"none","full","min","max","fit","prose",{screen:[isTshirtSize]},isTshirtSize]}],h:[{h:[isArbitraryValue,n,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[isArbitraryValue,n,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[isArbitraryValue,n,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[isArbitraryValue,n,"auto","min","max","fit"]}],"font-size":[{text:["base",isTshirtSize,isArbitraryLength]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",isArbitraryNumber]}],"font-family":[{font:[isAny]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",isArbitraryValue]}],"line-clamp":[{"line-clamp":["none",isNumber,isArbitraryNumber]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",isLength,isArbitraryValue]}],"list-image":[{"list-image":["none",isArbitraryValue]}],"list-style-type":[{list:["none","disc","decimal",isArbitraryValue]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[x]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[x]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...getLineStyles(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",isLength,isArbitraryLength]}],"underline-offset":[{"underline-offset":["auto",isLength,isArbitraryValue]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:getSpacingWithArbitrary()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",isArbitraryValue]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",isArbitraryValue]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[x]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...getPositions(),isArbitraryPosition]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",isArbitrarySize]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},isArbitraryImage]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[b]}],"gradient-via-pos":[{via:[b]}],"gradient-to-pos":[{to:[b]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[u]}],"rounded-s":[{"rounded-s":[u]}],"rounded-e":[{"rounded-e":[u]}],"rounded-t":[{"rounded-t":[u]}],"rounded-r":[{"rounded-r":[u]}],"rounded-b":[{"rounded-b":[u]}],"rounded-l":[{"rounded-l":[u]}],"rounded-ss":[{"rounded-ss":[u]}],"rounded-se":[{"rounded-se":[u]}],"rounded-ee":[{"rounded-ee":[u]}],"rounded-es":[{"rounded-es":[u]}],"rounded-tl":[{"rounded-tl":[u]}],"rounded-tr":[{"rounded-tr":[u]}],"rounded-br":[{"rounded-br":[u]}],"rounded-bl":[{"rounded-bl":[u]}],"border-w":[{border:[f]}],"border-w-x":[{"border-x":[f]}],"border-w-y":[{"border-y":[f]}],"border-w-s":[{"border-s":[f]}],"border-w-e":[{"border-e":[f]}],"border-w-t":[{"border-t":[f]}],"border-w-r":[{"border-r":[f]}],"border-w-b":[{"border-b":[f]}],"border-w-l":[{"border-l":[f]}],"border-opacity":[{"border-opacity":[x]}],"border-style":[{border:[...getLineStyles(),"hidden"]}],"divide-x":[{"divide-x":[f]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[f]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[x]}],"divide-style":[{divide:getLineStyles()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...getLineStyles()]}],"outline-offset":[{"outline-offset":[isLength,isArbitraryValue]}],"outline-w":[{outline:[isLength,isArbitraryLength]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:getLengthWithEmptyAndArbitrary()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[x]}],"ring-offset-w":[{"ring-offset":[isLength,isArbitraryLength]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",isTshirtSize,isArbitraryShadow]}],"shadow-color":[{shadow:[isAny]}],opacity:[{opacity:[x]}],"mix-blend":[{"mix-blend":[...getBlendModes(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":getBlendModes()}],filter:[{filter:["","none"]}],blur:[{blur:[a]}],brightness:[{brightness:[i]}],contrast:[{contrast:[p]}],"drop-shadow":[{"drop-shadow":["","none",isTshirtSize,isArbitraryValue]}],grayscale:[{grayscale:[h]}],"hue-rotate":[{"hue-rotate":[m]}],invert:[{invert:[g]}],saturate:[{saturate:[S]}],sepia:[{sepia:[R]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[a]}],"backdrop-brightness":[{"backdrop-brightness":[i]}],"backdrop-contrast":[{"backdrop-contrast":[p]}],"backdrop-grayscale":[{"backdrop-grayscale":[h]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[m]}],"backdrop-invert":[{"backdrop-invert":[g]}],"backdrop-opacity":[{"backdrop-opacity":[x]}],"backdrop-saturate":[{"backdrop-saturate":[S]}],"backdrop-sepia":[{"backdrop-sepia":[R]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[d]}],"border-spacing-x":[{"border-spacing-x":[d]}],"border-spacing-y":[{"border-spacing-y":[d]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",isArbitraryValue]}],duration:[{duration:getNumberAndArbitrary()}],ease:[{ease:["linear","in","out","in-out",isArbitraryValue]}],delay:[{delay:getNumberAndArbitrary()}],animate:[{animate:["none","spin","ping","pulse","bounce",isArbitraryValue]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[P]}],"scale-x":[{"scale-x":[P]}],"scale-y":[{"scale-y":[P]}],rotate:[{rotate:[isInteger,isArbitraryValue]}],"translate-x":[{"translate-x":[j]}],"translate-y":[{"translate-y":[j]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",isArbitraryValue]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",isArbitraryValue]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":getSpacingWithArbitrary()}],"scroll-mx":[{"scroll-mx":getSpacingWithArbitrary()}],"scroll-my":[{"scroll-my":getSpacingWithArbitrary()}],"scroll-ms":[{"scroll-ms":getSpacingWithArbitrary()}],"scroll-me":[{"scroll-me":getSpacingWithArbitrary()}],"scroll-mt":[{"scroll-mt":getSpacingWithArbitrary()}],"scroll-mr":[{"scroll-mr":getSpacingWithArbitrary()}],"scroll-mb":[{"scroll-mb":getSpacingWithArbitrary()}],"scroll-ml":[{"scroll-ml":getSpacingWithArbitrary()}],"scroll-p":[{"scroll-p":getSpacingWithArbitrary()}],"scroll-px":[{"scroll-px":getSpacingWithArbitrary()}],"scroll-py":[{"scroll-py":getSpacingWithArbitrary()}],"scroll-ps":[{"scroll-ps":getSpacingWithArbitrary()}],"scroll-pe":[{"scroll-pe":getSpacingWithArbitrary()}],"scroll-pt":[{"scroll-pt":getSpacingWithArbitrary()}],"scroll-pr":[{"scroll-pr":getSpacingWithArbitrary()}],"scroll-pb":[{"scroll-pb":getSpacingWithArbitrary()}],"scroll-pl":[{"scroll-pl":getSpacingWithArbitrary()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",isArbitraryValue]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[isLength,isArbitraryLength,isArbitraryNumber]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},6158:(e,n,a)=>{"use strict";a.r(n),a.d(n,{__addDisposableResource:()=>__addDisposableResource,__assign:()=>__assign,__asyncDelegator:()=>__asyncDelegator,__asyncGenerator:()=>__asyncGenerator,__asyncValues:()=>__asyncValues,__await:()=>__await,__awaiter:()=>__awaiter,__classPrivateFieldGet:()=>__classPrivateFieldGet,__classPrivateFieldIn:()=>__classPrivateFieldIn,__classPrivateFieldSet:()=>__classPrivateFieldSet,__createBinding:()=>i,__decorate:()=>__decorate,__disposeResources:()=>__disposeResources,__esDecorate:()=>__esDecorate,__exportStar:()=>__exportStar,__extends:()=>__extends,__generator:()=>__generator,__importDefault:()=>__importDefault,__importStar:()=>__importStar,__makeTemplateObject:()=>__makeTemplateObject,__metadata:()=>__metadata,__param:()=>__param,__propKey:()=>__propKey,__read:()=>__read,__rest:()=>__rest,__rewriteRelativeImportExtension:()=>__rewriteRelativeImportExtension,__runInitializers:()=>__runInitializers,__setFunctionName:()=>__setFunctionName,__spread:()=>__spread,__spreadArray:()=>__spreadArray,__spreadArrays:()=>__spreadArrays,__values:()=>__values,default:()=>d});var extendStatics=function(e,n){return(extendStatics=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])})(e,n)};function __extends(e,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function __(){this.constructor=e}extendStatics(e,n),e.prototype=null===n?Object.create(n):(__.prototype=n.prototype,new __)}var __assign=function(){return(__assign=Object.assign||function(e){for(var n,a=1,i=arguments.length;a<i;a++)for(var s in n=arguments[a])Object.prototype.hasOwnProperty.call(n,s)&&(e[s]=n[s]);return e}).apply(this,arguments)};function __rest(e,n){var a={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>n.indexOf(i)&&(a[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>n.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(a[i[s]]=e[i[s]]);return a}function __decorate(e,n,a,i){var s,u=arguments.length,d=u<3?n:null===i?i=Object.getOwnPropertyDescriptor(n,a):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)d=Reflect.decorate(e,n,a,i);else for(var f=e.length-1;f>=0;f--)(s=e[f])&&(d=(u<3?s(d):u>3?s(n,a,d):s(n,a))||d);return u>3&&d&&Object.defineProperty(n,a,d),d}function __param(e,n){return function(a,i){n(a,i,e)}}function __esDecorate(e,n,a,i,s,u){function accept(e){if(void 0!==e&&"function"!=typeof e)throw TypeError("Function expected");return e}for(var d,f=i.kind,p="getter"===f?"get":"setter"===f?"set":"value",h=!n&&e?i.static?e:e.prototype:null,m=n||(h?Object.getOwnPropertyDescriptor(h,i.name):{}),g=!1,v=a.length-1;v>=0;v--){var y={};for(var b in i)y[b]="access"===b?{}:i[b];for(var b in i.access)y.access[b]=i.access[b];y.addInitializer=function(e){if(g)throw TypeError("Cannot add initializers after decoration has completed");u.push(accept(e||null))};var _=(0,a[v])("accessor"===f?{get:m.get,set:m.set}:m[p],y);if("accessor"===f){if(void 0===_)continue;if(null===_||"object"!=typeof _)throw TypeError("Object expected");(d=accept(_.get))&&(m.get=d),(d=accept(_.set))&&(m.set=d),(d=accept(_.init))&&s.unshift(d)}else(d=accept(_))&&("field"===f?s.unshift(d):m[p]=d)}h&&Object.defineProperty(h,i.name,m),g=!0}function __runInitializers(e,n,a){for(var i=arguments.length>2,s=0;s<n.length;s++)a=i?n[s].call(e,a):n[s].call(e);return i?a:void 0}function __propKey(e){return"symbol"==typeof e?e:"".concat(e)}function __setFunctionName(e,n,a){return"symbol"==typeof n&&(n=n.description?"[".concat(n.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:a?"".concat(a," ",n):n})}function __metadata(e,n){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,n)}function __awaiter(e,n,a,i){return new(a||(a=Promise))(function(s,u){function fulfilled(e){try{step(i.next(e))}catch(e){u(e)}}function rejected(e){try{step(i.throw(e))}catch(e){u(e)}}function step(e){var n;e.done?s(e.value):((n=e.value)instanceof a?n:new a(function(e){e(n)})).then(fulfilled,rejected)}step((i=i.apply(e,n||[])).next())})}function __generator(e,n){var a,i,s,u={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},d=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return d.next=verb(0),d.throw=verb(1),d.return=verb(2),"function"==typeof Symbol&&(d[Symbol.iterator]=function(){return this}),d;function verb(f){return function(p){return function(f){if(a)throw TypeError("Generator is already executing.");for(;d&&(d=0,f[0]&&(u=0)),u;)try{if(a=1,i&&(s=2&f[0]?i.return:f[0]?i.throw||((s=i.return)&&s.call(i),0):i.next)&&!(s=s.call(i,f[1])).done)return s;switch(i=0,s&&(f=[2&f[0],s.value]),f[0]){case 0:case 1:s=f;break;case 4:return u.label++,{value:f[1],done:!1};case 5:u.label++,i=f[1],f=[0];continue;case 7:f=u.ops.pop(),u.trys.pop();continue;default:if(!(s=(s=u.trys).length>0&&s[s.length-1])&&(6===f[0]||2===f[0])){u=0;continue}if(3===f[0]&&(!s||f[1]>s[0]&&f[1]<s[3])){u.label=f[1];break}if(6===f[0]&&u.label<s[1]){u.label=s[1],s=f;break}if(s&&u.label<s[2]){u.label=s[2],u.ops.push(f);break}s[2]&&u.ops.pop(),u.trys.pop();continue}f=n.call(e,u)}catch(e){f=[6,e],i=0}finally{a=s=0}if(5&f[0])throw f[1];return{value:f[0]?f[1]:void 0,done:!0}}([f,p])}}}var i=Object.create?function(e,n,a,i){void 0===i&&(i=a);var s=Object.getOwnPropertyDescriptor(n,a);(!s||("get"in s?!n.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return n[a]}}),Object.defineProperty(e,i,s)}:function(e,n,a,i){void 0===i&&(i=a),e[i]=n[a]};function __exportStar(e,n){for(var a in e)"default"===a||Object.prototype.hasOwnProperty.call(n,a)||i(n,e,a)}function __values(e){var n="function"==typeof Symbol&&Symbol.iterator,a=n&&e[n],i=0;if(a)return a.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function __read(e,n){var a="function"==typeof Symbol&&e[Symbol.iterator];if(!a)return e;var i,s,u=a.call(e),d=[];try{for(;(void 0===n||n-- >0)&&!(i=u.next()).done;)d.push(i.value)}catch(e){s={error:e}}finally{try{i&&!i.done&&(a=u.return)&&a.call(u)}finally{if(s)throw s.error}}return d}function __spread(){for(var e=[],n=0;n<arguments.length;n++)e=e.concat(__read(arguments[n]));return e}function __spreadArrays(){for(var e=0,n=0,a=arguments.length;n<a;n++)e+=arguments[n].length;for(var i=Array(e),s=0,n=0;n<a;n++)for(var u=arguments[n],d=0,f=u.length;d<f;d++,s++)i[s]=u[d];return i}function __spreadArray(e,n,a){if(a||2==arguments.length)for(var i,s=0,u=n.length;s<u;s++)!i&&s in n||(i||(i=Array.prototype.slice.call(n,0,s)),i[s]=n[s]);return e.concat(i||Array.prototype.slice.call(n))}function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}function __asyncGenerator(e,n,a){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var i,s=a.apply(e,n||[]),u=[];return i=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),verb("next"),verb("throw"),verb("return",function(e){return function(n){return Promise.resolve(n).then(e,reject)}}),i[Symbol.asyncIterator]=function(){return this},i;function verb(e,n){s[e]&&(i[e]=function(n){return new Promise(function(a,i){u.push([e,n,a,i])>1||resume(e,n)})},n&&(i[e]=n(i[e])))}function resume(e,n){try{var a;(a=s[e](n)).value instanceof __await?Promise.resolve(a.value.v).then(fulfill,reject):settle(u[0][2],a)}catch(e){settle(u[0][3],e)}}function fulfill(e){resume("next",e)}function reject(e){resume("throw",e)}function settle(e,n){e(n),u.shift(),u.length&&resume(u[0][0],u[0][1])}}function __asyncDelegator(e){var n,a;return n={},verb("next"),verb("throw",function(e){throw e}),verb("return"),n[Symbol.iterator]=function(){return this},n;function verb(i,s){n[i]=e[i]?function(n){return(a=!a)?{value:__await(e[i](n)),done:!1}:s?s(n):n}:s}}function __asyncValues(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,a=e[Symbol.asyncIterator];return a?a.call(e):(e=__values(e),n={},verb("next"),verb("throw"),verb("return"),n[Symbol.asyncIterator]=function(){return this},n);function verb(a){n[a]=e[a]&&function(n){return new Promise(function(i,s){(function(e,n,a,i){Promise.resolve(i).then(function(n){e({value:n,done:a})},n)})(i,s,(n=e[a](n)).done,n.value)})}}}function __makeTemplateObject(e,n){return Object.defineProperty?Object.defineProperty(e,"raw",{value:n}):e.raw=n,e}var s=Object.create?function(e,n){Object.defineProperty(e,"default",{enumerable:!0,value:n})}:function(e,n){e.default=n};function __importStar(e){if(e&&e.__esModule)return e;var n={};if(null!=e)for(var a in e)"default"!==a&&Object.prototype.hasOwnProperty.call(e,a)&&i(n,e,a);return s(n,e),n}function __importDefault(e){return e&&e.__esModule?e:{default:e}}function __classPrivateFieldGet(e,n,a,i){if("a"===a&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof n?e!==n||!i:!n.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===a?i:"a"===a?i.call(e):i?i.value:n.get(e)}function __classPrivateFieldSet(e,n,a,i,s){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof n?e!==n||!s:!n.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?s.call(e,a):s?s.value=a:n.set(e,a),a}function __classPrivateFieldIn(e,n){if(null===n||"object"!=typeof n&&"function"!=typeof n)throw TypeError("Cannot use 'in' operator on non-object");return"function"==typeof e?n===e:e.has(n)}function __addDisposableResource(e,n,a){if(null!=n){var i,s;if("object"!=typeof n&&"function"!=typeof n)throw TypeError("Object expected.");if(a){if(!Symbol.asyncDispose)throw TypeError("Symbol.asyncDispose is not defined.");i=n[Symbol.asyncDispose]}if(void 0===i){if(!Symbol.dispose)throw TypeError("Symbol.dispose is not defined.");i=n[Symbol.dispose],a&&(s=i)}if("function"!=typeof i)throw TypeError("Object not disposable.");s&&(i=function(){try{s.call(this)}catch(e){return Promise.reject(e)}}),e.stack.push({value:n,dispose:i,async:a})}else a&&e.stack.push({async:!0});return n}var u="function"==typeof SuppressedError?SuppressedError:function(e,n,a){var i=Error(a);return i.name="SuppressedError",i.error=e,i.suppressed=n,i};function __disposeResources(e){function fail(n){e.error=e.hasError?new u(n,e.error,"An error was suppressed during disposal."):n,e.hasError=!0}var n,a=0;return function next(){for(;n=e.stack.pop();)try{if(!n.async&&1===a)return a=0,e.stack.push(n),Promise.resolve().then(next);if(n.dispose){var i=n.dispose.call(n.value);if(n.async)return a|=2,Promise.resolve(i).then(next,function(e){return fail(e),next()})}else a|=1}catch(e){fail(e)}if(1===a)return e.hasError?Promise.reject(e.error):Promise.resolve();if(e.hasError)throw e.error}()}function __rewriteRelativeImportExtension(e,n){return"string"==typeof e&&/^\.\.?\//.test(e)?e.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(e,a,i,s,u){return a?n?".jsx":".js":!i||s&&u?i+s+"."+u.toLowerCase()+"js":e}):e}let d={__extends,__assign,__rest,__decorate,__param,__esDecorate,__runInitializers,__propKey,__setFunctionName,__metadata,__awaiter,__generator,__createBinding:i,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources,__rewriteRelativeImportExtension}}};