exports.id=447,exports.ids=[447],exports.modules={102:(e,t,a)=>{Promise.resolve().then(a.bind(a,3710)),Promise.resolve().then(a.bind(a,9086))},256:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,3724,23)),Promise.resolve().then(a.t.bind(a,5365,23)),Promise.resolve().then(a.t.bind(a,4900,23)),Promise.resolve().then(a.t.bind(a,4714,23)),Promise.resolve().then(a.t.bind(a,5392,23)),Promise.resolve().then(a.t.bind(a,8898,23))},5303:()=>{},3710:(e,t,a)=>{"use strict";a.r(t),a.d(t,{Providers:()=>Providers});var s=a(784),r=a(465),o=a(9885),n=a(9203),i=a(1971),l=a(6206),d=a(8699);let c=n.zt,p=o.forwardRef(({className:e,...t},a)=>s.jsx(n.l_,{ref:a,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));p.displayName=n.l_.displayName;let m=(0,i.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),u=o.forwardRef(({className:e,variant:t,...a},r)=>s.jsx(n.fC,{ref:r,className:(0,d.cn)(m({variant:t}),e),...a}));u.displayName=n.fC.displayName;let f=o.forwardRef(({className:e,...t},a)=>s.jsx(n.aU,{ref:a,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));f.displayName=n.aU.displayName;let g=o.forwardRef(({className:e,...t},a)=>s.jsx(n.x8,{ref:a,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:s.jsx(l.Z,{className:"h-4 w-4"})}));g.displayName=n.x8.displayName;let h=o.forwardRef(({className:e,...t},a)=>s.jsx(n.Dx,{ref:a,className:(0,d.cn)("text-sm font-semibold",e),...t}));h.displayName=n.Dx.displayName;let x=o.forwardRef(({className:e,...t},a)=>s.jsx(n.dk,{ref:a,className:(0,d.cn)("text-sm opacity-90",e),...t}));function Toaster(){let{toasts:e}=(0,r.pm)();return(0,s.jsxs)(c,{children:[e.map(function({id:e,title:t,description:a,action:r,...o}){return(0,s.jsxs)(u,{...o,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&s.jsx(h,{children:t}),a&&s.jsx(x,{children:a})]}),r,s.jsx(g,{})]},e)}),s.jsx(p,{})]})}x.displayName=n.dk.displayName;var v=a(1072),b=a(9941);let sonner_Toaster=({...e})=>{let{theme:t="system"}=(0,v.useTheme)();return s.jsx(b.x7,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var y=a(8691);let N=y.zt;y.fC,y.xz;let j=o.forwardRef(({className:e,sideOffset:t=4,...a},r)=>s.jsx(y.VY,{ref:r,sideOffset:t,className:(0,d.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}));j.displayName=y.VY.displayName;var w=a(3162),T=a(4070);function Providers({children:e}){let[t]=(0,o.useState)(()=>new w.S);return s.jsx(T.aH,{client:t,children:(0,s.jsxs)(N,{children:[e,s.jsx(Toaster,{}),s.jsx(sonner_Toaster,{})]})})}},9086:(e,t,a)=>{"use strict";a.r(t),a.d(t,{AppointmentProvider:()=>AppointmentProvider,useAppointment:()=>useAppointment});var s=a(784),r=a(9885),o=a.n(r),n=a(5555),i=a(3770),l=a(3618),d=a(1971),c=a(8699);let p=(0,d.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),m=r.forwardRef(({className:e,...t},a)=>s.jsx(l.f,{ref:a,className:(0,c.cn)(p(),e),...t}));m.displayName=l.f.displayName;let u=r.forwardRef(({className:e,...t},a)=>s.jsx("textarea",{className:(0,c.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));u.displayName="Textarea";var f=a(5985),g=a(9458),h=a(4678),x=a(1264);let v=f.fC;f.ZA;let b=f.B4,y=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(f.xz,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,s.jsx(f.JO,{asChild:!0,children:s.jsx(g.Z,{className:"h-4 w-4 opacity-50"})})]}));y.displayName=f.xz.displayName;let N=r.forwardRef(({className:e,...t},a)=>s.jsx(f.u_,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(h.Z,{className:"h-4 w-4"})}));N.displayName=f.u_.displayName;let j=r.forwardRef(({className:e,...t},a)=>s.jsx(f.$G,{ref:a,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(g.Z,{className:"h-4 w-4"})}));j.displayName=f.$G.displayName;let w=r.forwardRef(({className:e,children:t,position:a="popper",...r},o)=>s.jsx(f.h_,{children:(0,s.jsxs)(f.VY,{ref:o,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[s.jsx(N,{}),s.jsx(f.l_,{className:(0,c.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),s.jsx(j,{})]})}));w.displayName=f.VY.displayName;let T=r.forwardRef(({className:e,...t},a)=>s.jsx(f.__,{ref:a,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));T.displayName=f.__.displayName;let S=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(f.ck,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(f.wU,{children:s.jsx(x.Z,{className:"h-4 w-4"})})}),s.jsx(f.eT,{children:t})]}));S.displayName=f.ck.displayName;let A=r.forwardRef(({className:e,...t},a)=>s.jsx(f.Z0,{ref:a,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));A.displayName=f.Z0.displayName;var P=a(516),C=a(3680),D=a(7371),R=a(5441),k=a(169),V=a(4571);let E=["Vaccinations","Health check-ups","Surgical procedures","Dental care","Diagnostic testing","Emergency care","Preventative care","Parasite control","Local and international travel","Wash & Grooming"],I=["8:00 AM","9:00 AM","10:00 AM","11:00 AM","12:00 PM","1:00 PM","2:00 PM","3:00 PM","4:00 PM","5:00 PM","6:00 PM"];function AppointmentModal({isOpen:e,onClose:t}){console.log("AppointmentModal render - isOpen:",e),o().useEffect(()=>{e&&console.log("Modal is now open!")},[e]);let[a,l]=(0,r.useState)({ownerName:"",phone:"",email:"",petName:"",petType:"",petAge:"",service:"",preferredDate:"",preferredTime:"",message:""}),[d,c]=(0,r.useState)(!1),handleInputChange=(e,t)=>{l(a=>({...a,[e]:t}))},formatWhatsAppMessage=e=>{let t=`
🐾 *CVETS Appointment Request* 🐾

👤 *Owner Details:*
Name: ${e.ownerName}
Phone: ${e.phone}
Email: ${e.email}

🐕 *Pet Details:*
Pet Name: ${e.petName}
Pet Type: ${e.petType}
Pet Age: ${e.petAge}

🏥 *Appointment Details:*
Service: ${e.service}
Preferred Date: ${e.preferredDate}
Preferred Time: ${e.preferredTime}

💬 *Additional Message:*
${e.message||"No additional message"}

---
Please confirm this appointment or suggest an alternative time. Thank you!
    `.trim();return encodeURIComponent(t)},handleSubmit=async e=>{e.preventDefault(),console.log("Form submitted with data:",a),c(!0);try{let e=["ownerName","phone","petName","service","preferredDate"].filter(e=>!a[e]);if(e.length>0){alert("Please fill in all required fields"),c(!1);return}let s=formatWhatsAppMessage(a),r=`https://wa.me/254718376311?text=${s}`;console.log("WhatsApp URL:",r),window.open(r,"_blank"),l({ownerName:"",phone:"",email:"",petName:"",petType:"",petAge:"",service:"",preferredDate:"",preferredTime:"",message:""}),t(),alert("Appointment request sent! You will be redirected to WhatsApp to complete the booking.")}catch(e){console.error("Error submitting appointment:",e),alert("There was an error submitting your appointment. Please try again.")}finally{c(!1)}};return e&&console.log("Modal should be visible now"),s.jsx(P.Vq,{open:e,onOpenChange:t,children:(0,s.jsxs)(P.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto bg-white border border-gray-200 shadow-2xl",children:[(0,s.jsxs)(P.fK,{className:"pb-6",children:[s.jsx(P.$N,{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 bg-clip-text text-transparent text-center",children:"Book Your Pet's Appointment"}),s.jsx("p",{className:"text-center text-gray-600 mt-2",children:"Fill out the form below and we'll contact you via WhatsApp to confirm your appointment"})]}),(0,s.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center space-x-2",children:[s.jsx(C.Z,{className:"w-5 h-5 text-blue-600"}),s.jsx("span",{children:"Owner Information"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m,{htmlFor:"ownerName",className:"text-gray-700 font-medium",children:"Full Name *"}),s.jsx(i.I,{id:"ownerName",type:"text",value:a.ownerName,onChange:e=>handleInputChange("ownerName",e.target.value),placeholder:"Enter your full name",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m,{htmlFor:"phone",className:"text-gray-700 font-medium",children:"Phone Number *"}),s.jsx(i.I,{id:"phone",type:"tel",value:a.phone,onChange:e=>handleInputChange("phone",e.target.value),placeholder:"0718376311",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[s.jsx(m,{htmlFor:"email",className:"text-gray-700 font-medium",children:"Email Address"}),s.jsx(i.I,{id:"email",type:"email",value:a.email,onChange:e=>handleInputChange("email",e.target.value),placeholder:"<EMAIL>",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center space-x-2",children:[s.jsx(D.Z,{className:"w-5 h-5 text-pink-600"}),s.jsx("span",{children:"Pet Information"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m,{htmlFor:"petName",className:"text-gray-700 font-medium",children:"Pet Name *"}),s.jsx(i.I,{id:"petName",type:"text",value:a.petName,onChange:e=>handleInputChange("petName",e.target.value),placeholder:"Enter pet's name",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m,{htmlFor:"petType",className:"text-gray-700 font-medium",children:"Pet Type"}),s.jsx(i.I,{id:"petType",type:"text",value:a.petType,onChange:e=>handleInputChange("petType",e.target.value),placeholder:"Dog, Cat, Bird, etc.",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m,{htmlFor:"petAge",className:"text-gray-700 font-medium",children:"Pet Age"}),s.jsx(i.I,{id:"petAge",type:"text",value:a.petAge,onChange:e=>handleInputChange("petAge",e.target.value),placeholder:"2 years, 6 months, etc.",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center space-x-2",children:[s.jsx(R.Z,{className:"w-5 h-5 text-blue-600"}),s.jsx("span",{children:"Appointment Details"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m,{htmlFor:"service",className:"text-gray-700 font-medium",children:"Service Required *"}),(0,s.jsxs)(v,{value:a.service,onValueChange:e=>handleInputChange("service",e),children:[s.jsx(y,{className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",children:s.jsx(b,{placeholder:"Select a service"})}),s.jsx(w,{children:E.map(e=>s.jsx(S,{value:e,children:e},e))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m,{htmlFor:"preferredDate",className:"text-gray-700 font-medium",children:"Preferred Date *"}),s.jsx(i.I,{id:"preferredDate",type:"date",value:a.preferredDate,onChange:e=>handleInputChange("preferredDate",e.target.value),className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",min:new Date().toISOString().split("T")[0],required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m,{htmlFor:"preferredTime",className:"text-gray-700 font-medium",children:"Preferred Time"}),(0,s.jsxs)(v,{value:a.preferredTime,onValueChange:e=>handleInputChange("preferredTime",e),children:[s.jsx(y,{className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",children:s.jsx(b,{placeholder:"Select time"})}),s.jsx(w,{children:I.map(e=>s.jsx(S,{value:e,children:e},e))})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center space-x-2",children:[s.jsx(k.Z,{className:"w-5 h-5 text-pink-600"}),s.jsx("span",{children:"Additional Information"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(m,{htmlFor:"message",className:"text-gray-700 font-medium",children:"Additional Message or Special Requirements"}),s.jsx(u,{id:"message",value:a.message,onChange:e=>handleInputChange("message",e.target.value),placeholder:"Please describe any specific concerns, symptoms, or special requirements for your pet...",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80 min-h-[100px]",rows:4})]})]}),(0,s.jsxs)("div",{className:"pt-4 flex gap-4",children:[s.jsx(n.z,{type:"button",variant:"outline",onClick:t,className:"flex-1 border-gray-200 text-gray-700 hover:bg-gray-50",children:"Cancel"}),s.jsx(n.z,{type:"submit",disabled:d,className:"flex-1 bg-gradient-to-r from-blue-600 to-pink-600 hover:from-blue-700 hover:to-pink-700 text-white shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:d?(0,s.jsxs)("span",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),s.jsx("span",{children:"Sending..."})]}):(0,s.jsxs)("span",{className:"flex items-center space-x-2",children:[s.jsx(V.Z,{className:"w-4 h-4"}),s.jsx("span",{children:"Send to WhatsApp"})]})})]})]})]})})}let M=(0,r.createContext)(void 0);function useAppointment(){let e=(0,r.useContext)(M);if(void 0===e)throw Error("useAppointment must be used within an AppointmentProvider");return e}function AppointmentProvider({children:e}){let[t,a]=(0,r.useState)(!1),closeAppointmentModal=()=>a(!1);return(0,s.jsxs)(M.Provider,{value:{openAppointmentModal:()=>{console.log("Opening appointment modal"),a(!0)},closeAppointmentModal,isModalOpen:t},children:[e,s.jsx(AppointmentModal,{isOpen:t,onClose:closeAppointmentModal})]})}},5555:(e,t,a)=>{"use strict";a.d(t,{z:()=>d});var s=a(784),r=a(9885),o=a(1085),n=a(1971),i=a(8699);let l=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef(({className:e,variant:t,size:a,asChild:r=!1,...n},d)=>{let c=r?o.g7:"button";return s.jsx(c,{className:(0,i.cn)(l({variant:t,size:a,className:e})),ref:d,...n})});d.displayName="Button"},516:(e,t,a)=>{"use strict";a.d(t,{$N:()=>m,Vq:()=>l,cZ:()=>p,fK:()=>DialogHeader});var s=a(784),r=a(9885),o=a(6705),n=a(6206),i=a(8699);let l=o.fC;o.xz;let d=o.h_;o.x8;let c=r.forwardRef(({className:e,...t},a)=>s.jsx(o.aV,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));c.displayName=o.aV.displayName;let p=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(d,{children:[s.jsx(c,{}),(0,s.jsxs)(o.VY,{ref:r,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,s.jsxs)(o.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[s.jsx(n.Z,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=o.VY.displayName;let DialogHeader=({className:e,...t})=>s.jsx("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});DialogHeader.displayName="DialogHeader";let m=r.forwardRef(({className:e,...t},a)=>s.jsx(o.Dx,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));m.displayName=o.Dx.displayName;let u=r.forwardRef(({className:e,...t},a)=>s.jsx(o.dk,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));u.displayName=o.dk.displayName},3770:(e,t,a)=>{"use strict";a.d(t,{I:()=>n});var s=a(784),r=a(9885),o=a(8699);let n=r.forwardRef(({className:e,type:t,...a},r)=>s.jsx("input",{type:t,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...a}));n.displayName="Input"},465:(e,t,a)=>{"use strict";a.d(t,{pm:()=>useToast});var s=a(9885);let r=0,o=new Map,addToRemoveQueue=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),dispatch({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},reducer=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?addToRemoveQueue(a):e.toasts.forEach(e=>{addToRemoveQueue(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},n=[],i={toasts:[]};function dispatch(e){i=reducer(i,e),n.forEach(e=>{e(i)})}function toast({...e}){let t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),dismiss=()=>dispatch({type:"DISMISS_TOAST",toastId:t});return dispatch({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||dismiss()}}}),{id:t,dismiss,update:e=>dispatch({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function useToast(){let[e,t]=s.useState(i);return s.useEffect(()=>(n.push(t),()=>{let e=n.indexOf(t);e>-1&&n.splice(e,1)}),[e]),{...e,toast,dismiss:e=>dispatch({type:"DISMISS_TOAST",toastId:e})}}},8699:(e,t,a)=>{"use strict";a.d(t,{BK:()=>smoothScrollToElement,QR:()=>handleMenuNavigation,cn:()=>cn,fW:()=>smoothScrollToAppointmentForm});var s=a(566),r=a(8126);function cn(...e){return(0,r.m6)((0,s.W)(e))}function smoothScrollToElement(e,t=80){let a=document.getElementById(e);if(a){let e=a.getBoundingClientRect().top+window.pageYOffset;window.scrollTo({top:e-t,behavior:"smooth"})}}function smoothScrollToAppointmentForm(){smoothScrollToElement("appointment-form")}function handleMenuNavigation(e){window.location.href=e}},8275:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>RootLayout,metadata:()=>g});var s=a(4656),r=a(1326),o=a.n(r),n=a(5153);let i=(0,n.createProxy)(String.raw`/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/providers.tsx`),{__esModule:l,$$typeof:d}=i;i.default;let c=i.Providers,p=(0,n.createProxy)(String.raw`/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/components/AppointmentProvider.tsx`),{__esModule:m,$$typeof:u}=p;p.default,p.useAppointment;let f=p.AppointmentProvider;a(8864);let g={metadataBase:new URL("https://cvets-veterinary.vercel.app"),title:{default:"cvets",template:"%s | cvets"},description:"CVETS Veterinary Services provides comprehensive pet care including health check-ups, surgical procedures, dental care, and emergency services. Led by Dr Cynthia.",keywords:"veterinary, pet care, animal health, Dr Cynthia, CVETS, veterinary clinic",authors:[{name:"CVETS Veterinary Services"}],creator:"CVETS Veterinary Services",publisher:"CVETS Veterinary Services",robots:{index:!0,follow:!0},openGraph:{title:"CVETS - Professional Veterinary Services",description:"CVETS Veterinary Services provides comprehensive pet care including health check-ups, surgical procedures, dental care, and emergency services. Led by Dr Cynthia.",type:"website",locale:"en_KE",url:"https://cvets-veterinary.vercel.app",siteName:"CVETS Veterinary Services",images:[{url:"https://lovable.dev/opengraph-image-p98pqg.png",width:1200,height:630,alt:"CVETS Veterinary Services - Professional Pet Care"}]},twitter:{card:"summary_large_image",title:"CVETS - Professional Veterinary Services",description:"CVETS Veterinary Services provides comprehensive pet care including health check-ups, surgical procedures, dental care, and emergency services.",site:"@cvets",creator:"@cvets",images:["https://lovable.dev/opengraph-image-p98pqg.png"]},icons:{icon:"/favicon.ico",shortcut:"/favicon-16x16.png",apple:"/apple-touch-icon.png"},manifest:"/site.webmanifest"};function RootLayout({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:o().className,children:s.jsx(c,{children:s.jsx(f,{children:e})})})})}},4293:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>NotFound});var s=a(4656);function NotFound(){return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50",children:(0,s.jsxs)("div",{className:"text-center space-y-6",children:[s.jsx("h1",{className:"text-6xl font-bold text-slate-900",children:"404"}),s.jsx("h2",{className:"text-2xl font-semibold text-slate-700",children:"Page Not Found"}),s.jsx("p",{className:"text-lg text-slate-600 max-w-md mx-auto",children:"Sorry, we couldn't find the page you're looking for."}),s.jsx("a",{href:"/",className:"inline-block bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105",children:"Return Home"})]})})}},8864:()=>{}};