"use strict";exports.id=816,exports.ids=[816],exports.modules={6593:(e,s,t)=>{t.d(s,{Z:()=>components_Footer});var r=t(784),a=t(9885),l=t(1440),n=t.n(l),i=t(4571),c=t(169),d=t(5441),o=t(8699),x=t(516),m=t(5555),h=t(3770),p=t(465);let components_AdminLogin=({open:e,onOpenChange:s})=>{let[t,l]=(0,a.useState)(""),[n,i]=(0,a.useState)(""),[c,d]=(0,a.useState)(!1),{toast:o}=(0,p.pm)(),handleLogin=async e=>{e.preventDefault(),d(!0),"admin"===t&&"cvets123"===n?(localStorage.setItem("cvets_admin","true"),o({title:"Login successful",description:"Welcome back, admin!"}),s(!1),l(""),i("")):o({title:"Login failed",description:"Invalid credentials. Please try again.",variant:"destructive"}),d(!1)};return r.jsx(x.Vq,{open:e,onOpenChange:s,children:(0,r.jsxs)(x.cZ,{className:"sm:max-w-md",children:[r.jsx(x.fK,{children:r.jsx(x.$N,{children:"Admin Login"})}),(0,r.jsxs)("form",{onSubmit:handleLogin,className:"space-y-4",children:[r.jsx("div",{children:r.jsx(h.I,{type:"text",placeholder:"Username",value:t,onChange:e=>l(e.target.value),required:!0})}),r.jsx("div",{children:r.jsx(h.I,{type:"password",placeholder:"Password",value:n,onChange:e=>i(e.target.value),required:!0})}),r.jsx(m.z,{type:"submit",className:"w-full",disabled:c,children:c?"Logging in...":"Login"}),r.jsx("p",{className:"text-xs text-muted-foreground text-center",children:"Demo: admin / cvets123"})]})]})})},components_Footer=()=>{let[e,s]=(0,a.useState)(0),[t,l]=(0,a.useState)(!1);return(0,r.jsxs)(r.Fragment,{children:[r.jsx("footer",{className:"bg-slate-900 text-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[r.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer transition-transform hover:scale-105",onClick:()=>{s(e=>{let s=e+1;return 4===s?(l(!0),0):s})},children:r.jsx("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-xl font-bold",children:"CVETS Veterinary Services"}),r.jsx("p",{className:"text-sm text-slate-300",children:"Professional Pet Care"})]})]}),r.jsx("p",{className:"text-slate-300 mb-4 max-w-md",children:"Providing compassionate, professional veterinary care for your beloved pets. Your pet's health and wellbeing are our top priorities."}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-slate-300",children:[r.jsx(i.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"+254 718 376 311"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-slate-300",children:[r.jsx(c.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"WhatsApp: +254 718 376 311"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),(0,r.jsxs)("ul",{className:"space-y-2",children:[r.jsx("li",{children:r.jsx(n(),{href:"/services",className:"text-slate-300 hover:text-white transition-colors",children:"Our Services"})}),r.jsx("li",{children:r.jsx(n(),{href:"/about",className:"text-slate-300 hover:text-white transition-colors",children:"About Us"})}),r.jsx("li",{children:r.jsx(n(),{href:"/blog",className:"text-slate-300 hover:text-white transition-colors",children:"Pet Care Tips"})}),r.jsx("li",{children:r.jsx("button",{onClick:o.fW,className:"text-slate-300 hover:text-white transition-colors cursor-pointer",children:"Book Appointment"})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-lg font-semibold mb-4",children:"Service Hours"}),(0,r.jsxs)("div",{className:"space-y-2 text-slate-300",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(d.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Open 7 Days a Week"})]}),r.jsx("p",{children:"Monday - Sunday: 8AM - 6PM"}),r.jsx("p",{className:"text-red-300 font-medium",children:"Emergency Services Available"}),(0,r.jsxs)("a",{href:"https://wa.me/254718376311",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-2 text-green-400 hover:text-green-300 transition-colors mt-2",children:[r.jsx(c.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"WhatsApp Us"})]})]})]})]}),r.jsx("div",{className:"border-t border-slate-700 mt-8 pt-8 text-center text-slate-400",children:r.jsx("p",{children:"\xa9 2024 CVETS Veterinary Services. All rights reserved."})})]})}),r.jsx(components_AdminLogin,{open:t,onOpenChange:l})]})}},8282:(e,s,t)=>{t.d(s,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var r=t(784),a=t(9885),l=t(1440),n=t.n(l),i=t(7114),c=t(4571),d=t(6206),o=t(7382),x=t(5555),m=t(9086),h=t(8699);let __WEBPACK_DEFAULT_EXPORT__=()=>{let[e,s]=(0,a.useState)(!1),t=(0,i.usePathname)(),{openAppointmentModal:l}=(0,m.useAppointment)(),p=[{name:"Home",href:"/"},{name:"Services",href:"/services"},{name:"About",href:"/about"},{name:"Products",href:"/products"},{name:"Blog",href:"/blog"},{name:"Contact",href:"/contact"}],isActive=e=>t===e;(0,a.useEffect)(()=>{let e=window.location.hash;if(e){let s=e.substring(1);setTimeout(()=>{(0,h.BK)(s)},100)}},[t]);let handleNavClick=(e,t)=>{e.preventDefault(),s(!1),(0,h.QR)(t)};return r.jsx("header",{className:"bg-white shadow-sm border-b border-border sticky top-0 z-50",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[r.jsx("div",{className:"flex items-center",children:(0,r.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-xl font-bold text-foreground",children:"CVETS Veterinary Services"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Professional Pet Care"})]})]})}),r.jsx("nav",{className:"hidden md:flex space-x-8",children:p.map(e=>r.jsx("a",{href:e.href,onClick:s=>handleNavClick(s,e.href),className:`px-3 py-2 text-sm font-medium transition-colors duration-200 cursor-pointer ${isActive(e.href)?"text-primary border-b-2 border-primary":"text-muted-foreground hover:text-primary"}`,children:e.name},e.name))}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[r.jsx(c.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"0718376311"})]}),r.jsx(x.z,{onClick:l,className:"bg-blue-600 hover:bg-blue-700",children:"Book Now"})]}),r.jsx("div",{className:"md:hidden",children:r.jsx("button",{onClick:()=>s(!e),className:"p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted",children:e?r.jsx(d.Z,{className:"w-6 h-6"}):r.jsx(o.Z,{className:"w-6 h-6"})})})]}),e&&r.jsx("div",{className:"md:hidden py-4 border-t border-border",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[p.map(e=>r.jsx("a",{href:e.href,onClick:s=>handleNavClick(s,e.href),className:`px-3 py-2 text-base font-medium transition-colors duration-200 cursor-pointer ${isActive(e.href)?"text-primary bg-muted":"text-muted-foreground hover:text-primary hover:bg-muted"}`,children:e.name},e.name)),(0,r.jsxs)("div",{className:"px-3 py-2 text-sm text-muted-foreground flex items-center space-x-2",children:[r.jsx(c.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"0718376311"})]}),r.jsx("div",{className:"px-3 py-2",children:r.jsx(x.z,{onClick:()=>{s(!1),l()},className:"w-full bg-blue-600 hover:bg-blue-700",children:"Book Now"})})]})})]})})}}};