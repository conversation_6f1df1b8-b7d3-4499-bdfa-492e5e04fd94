"use strict";exports.id=599,exports.ids=[599],exports.modules={599:(e,a,r)=>{r.d(a,{Ol:()=>o,Zb:()=>l,aY:()=>c,ll:()=>f});var d=r(784),s=r(9885),t=r(8699);let l=s.forwardRef(({className:e,...a},r)=>d.jsx("div",{ref:r,className:(0,t.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...a}));l.displayName="Card";let o=s.forwardRef(({className:e,...a},r)=>d.jsx("div",{ref:r,className:(0,t.cn)("flex flex-col space-y-1.5 p-6",e),...a}));o.displayName="CardHeader";let f=s.forwardRef(({className:e,...a},r)=>d.jsx("h3",{ref:r,className:(0,t.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));f.displayName="CardTitle";let i=s.forwardRef(({className:e,...a},r)=>d.jsx("p",{ref:r,className:(0,t.cn)("text-sm text-muted-foreground",e),...a}));i.displayName="CardDescription";let c=s.forwardRef(({className:e,...a},r)=>d.jsx("div",{ref:r,className:(0,t.cn)("p-6 pt-0",e),...a}));c.displayName="CardContent";let m=s.forwardRef(({className:e,...a},r)=>d.jsx("div",{ref:r,className:(0,t.cn)("flex items-center p-6 pt-0",e),...a}));m.displayName="CardFooter"}};