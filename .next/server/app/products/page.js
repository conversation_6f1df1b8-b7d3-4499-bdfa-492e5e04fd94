(()=>{var e={};e.id=286,e.ids=[286],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1794:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=s(7096),r=s(6132),l=s(7284),n=s.n(l),o=s(2564),i={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);s.d(t,i);let c=["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,8008)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/products/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,8275)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4293)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found.tsx"]}],d=["/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/products/page.tsx"],x="/products/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8728:(e,t,s)=>{Promise.resolve().then(s.bind(s,6346))},6346:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ProductsPage});var a=s(784),r=s(9885),l=s(5555),n=s(599),o=s(1890),i=s(8282),c=s(6593),d=s(7004),x=s(6253),p=s(4706),m=s(5894),u=s(7371);function ProductsPage(){let[e,t]=(0,r.useState)([]),[s,h]=(0,r.useState)(!0),[g,b]=(0,r.useState)("All");(0,r.useEffect)(()=>{let fetchProducts=async()=>{try{let e=await fetch("/data/products.json"),s=await e.json();t(s)}catch(e){console.error("Error fetching products:",e)}finally{h(!1)}};fetchProducts()},[]);let v=["All",...Array.from(new Set(e.map(e=>e.category)))],j="All"===g?e:e.filter(e=>e.category===g),handleWhatsAppOrder=e=>{let t=`Hi! I'm interested in ordering the ${e.name}. Could you please provide more information about availability and delivery?`,s=`https://wa.me/254718376311?text=${encodeURIComponent(t)}`;window.open(s,"_blank")};return s?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[a.jsx(i.Z,{}),a.jsx("div",{className:"pt-20 pb-16 px-4 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading products..."})]})}),a.jsx(c.Z,{})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[a.jsx(i.Z,{}),(0,a.jsxs)("section",{className:"relative pt-20 pb-16 px-4 overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20"}),a.jsx("div",{className:"relative max-w-7xl mx-auto text-center",children:a.jsx("div",{className:"space-y-8",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium",children:[a.jsx(d.Z,{className:"w-4 h-4"}),"Quality Veterinary Products"]}),(0,a.jsxs)("h1",{className:"text-5xl lg:text-6xl font-bold text-gray-900 leading-tight",children:["Premium Products for",a.jsx("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-pink-500",children:" Your Pet's Health"})]}),a.jsx("p",{className:"text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto",children:"Discover our carefully selected range of veterinary products designed to keep your pets healthy, happy, and comfortable."})]})})})]}),a.jsx("section",{className:"py-8 px-4",children:a.jsx("div",{className:"max-w-7xl mx-auto",children:a.jsx("div",{className:"flex flex-wrap justify-center gap-4",children:v.map(e=>a.jsx(l.z,{variant:g===e?"default":"outline",onClick:()=>b(e),className:`rounded-full px-6 py-2 transition-all duration-300 ${g===e?"bg-gradient-to-r from-blue-600 to-pink-600 text-white":"border-gray-200 text-gray-700 hover:bg-blue-50"}`,children:e},e))})})}),a.jsx("section",{className:"py-16 px-4",children:a.jsx("div",{className:"max-w-7xl mx-auto",children:a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8",children:j.map(e=>(0,a.jsxs)(n.Zb,{className:"group relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl",children:[(0,a.jsxs)("div",{className:"relative h-48 overflow-hidden rounded-t-3xl",children:[a.jsx("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),a.jsx("div",{className:"absolute top-4 right-4",children:e.inStock?(0,a.jsxs)(o.C,{className:"bg-green-100 text-green-800 border-green-200",children:[a.jsx(x.Z,{className:"w-3 h-3 mr-1"}),"In Stock"]}):(0,a.jsxs)(o.C,{className:"bg-red-100 text-red-800 border-red-200",children:[a.jsx(p.Z,{className:"w-3 h-3 mr-1"}),"Out of Stock"]})}),a.jsx("div",{className:"absolute top-4 left-4",children:a.jsx(o.C,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:e.category})})]}),a.jsx(n.Ol,{className:"pb-2",children:a.jsx(n.ll,{className:"text-lg font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-300",children:e.name})}),(0,a.jsxs)(n.aY,{className:"space-y-4",children:[a.jsx("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h4",{className:"font-semibold text-gray-800 text-sm",children:"Features:"}),a.jsx("ul",{className:"space-y-1",children:e.features.slice(0,3).map((e,t)=>(0,a.jsxs)("li",{className:"flex items-center space-x-2 text-xs text-gray-600",children:[a.jsx(x.Z,{className:"w-3 h-3 text-green-600 flex-shrink-0"}),a.jsx("span",{children:e})]},t))})]}),a.jsx(l.z,{onClick:()=>handleWhatsAppOrder(e),disabled:!e.inStock,className:`w-full rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ${e.inStock?"bg-gradient-to-r from-blue-600 to-pink-600 hover:from-blue-700 hover:to-pink-700 text-white":"bg-gray-300 text-gray-500 cursor-not-allowed"}`,children:e.inStock?(0,a.jsxs)("span",{className:"flex items-center space-x-2",children:[a.jsx(m.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"Order via WhatsApp"})]}):a.jsx("span",{children:"Out of Stock"})})]})]},e.id))})})}),(0,a.jsxs)("section",{className:"py-20 relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700"}),a.jsx("div",{className:"absolute inset-0 bg-black/20"}),a.jsx("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,a.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white leading-tight",children:["Need Help Choosing",a.jsx("br",{}),a.jsx("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent",children:"The Right Product?"})]}),a.jsx("p",{className:"text-xl text-blue-100 leading-relaxed",children:"Contact Nolari for personalized product recommendations for your pet's specific needs"}),a.jsx(l.z,{onClick:()=>{let e=`https://wa.me/254718376311?text=${encodeURIComponent("Hi Nolari! I need help choosing the right products for my pet. Could you please provide some recommendations?")}`;window.open(e,"_blank")},className:"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 text-lg font-semibold",children:(0,a.jsxs)("span",{className:"flex items-center space-x-2",children:[a.jsx(u.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Get Expert Advice"})]})})]})})]}),a.jsx(c.Z,{})]})}},8008:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>l,default:()=>i});var a=s(5153);let r=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/products/page.tsx`),{__esModule:l,$$typeof:n}=r,o=r.default,i=o}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[918,176,381,447,816,599,890],()=>__webpack_exec__(1794));module.exports=s})();