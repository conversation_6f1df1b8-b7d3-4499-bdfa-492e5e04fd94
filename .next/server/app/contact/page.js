(()=>{var e={};e.id=327,e.ids=[327],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1523:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>x,originalPathname:()=>d,pages:()=>p,routeModule:()=>u,tree:()=>l});var r=s(7096),a=s(6132),n=s(7284),o=s.n(n),c=s(2564),i={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>c[e]);s.d(t,i);let l=["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,2629)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/contact/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,8275)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4293)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found.tsx"]}],p=["/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/contact/page.tsx"],d="/contact/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9437:(e,t,s)=>{Promise.resolve().then(s.bind(s,8259))},8259:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ContactPage});var r=s(784),a=s(8282),n=s(6593);function ContactPage(){return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[r.jsx(a.Z,{}),r.jsx("div",{className:"pt-20 pb-16 px-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[r.jsx("h1",{className:"text-4xl font-bold text-slate-900 mb-4",children:"Contact Us"}),r.jsx("p",{className:"text-xl text-slate-600",children:"Get in touch with CVETS Veterinary Services"}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("p",{className:"text-lg text-gray-700",children:"Phone: 0718376311"}),r.jsx("p",{className:"text-lg text-gray-700",children:"Email: <EMAIL>"})]})]})}),r.jsx(n.Z,{})]})}},2629:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>i});var r=s(5153);let a=(0,r.createProxy)(String.raw`/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/contact/page.tsx`),{__esModule:n,$$typeof:o}=a,c=a.default,i=c}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[918,176,447,816],()=>__webpack_exec__(1523));module.exports=s})();