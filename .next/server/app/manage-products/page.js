(()=>{var e={};e.id=166,e.ids=[166],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4357:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>p,routeModule:()=>x,tree:()=>l});var s=r(7096),a=r(6132),n=r(7284),o=r.n(n),i=r(2564),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l=["",{children:["manage-products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5897)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/manage-products/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8275)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,4293)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found.tsx"]}],p=["/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/manage-products/page.tsx"],d="/manage-products/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/manage-products/page",pathname:"/manage-products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},1792:(e,t,r)=>{Promise.resolve().then(r.bind(r,6333))},6333:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ManageProductsPage});var s=r(784),a=r(8282),n=r(6593);function ManageProductsPage(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50",children:[s.jsx(a.Z,{}),s.jsx("div",{className:"pt-20 pb-16 px-4",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[s.jsx("h1",{className:"text-4xl font-bold text-slate-900 mb-4",children:"Manage Products"}),s.jsx("p",{className:"text-xl text-slate-600",children:"Manage your veterinary products"})]})}),s.jsx(n.Z,{})]})}},5897:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>c});var s=r(5153);let a=(0,s.createProxy)(String.raw`/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/manage-products/page.tsx`),{__esModule:n,$$typeof:o}=a,i=a.default,c=i}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[918,176,447,816],()=>__webpack_exec__(4357));module.exports=r})();