"use strict";(()=>{var e={};e.id=165,e.ids=[165],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5979:(e,t,n)=>{n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>l});var r=n(7096),o=n(6132),s=n(7284),a=n.n(s),i=n(2564),p={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);n.d(t,p);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,4293)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found.tsx"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,8275)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.bind(n,4293)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found.tsx"]}],d=[],u="/_not-found",c={require:n,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),n=t.X(0,[918,447],()=>__webpack_exec__(5979));module.exports=n})();