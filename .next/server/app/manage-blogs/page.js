(()=>{var e={};e.id=800,e.ids=[800],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},9154:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>p,routeModule:()=>x,tree:()=>c});var a=s(7096),r=s(6132),n=s(7284),o=s.n(n),i=s(2564),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c=["",{children:["manage-blogs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7172)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/manage-blogs/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,8275)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4293)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found.tsx"]}],p=["/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/manage-blogs/page.tsx"],d="/manage-blogs/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/manage-blogs/page",pathname:"/manage-blogs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6451:(e,t,s)=>{Promise.resolve().then(s.bind(s,5291))},5291:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ManageBlogsPage});var a=s(784),r=s(8282),n=s(6593);function ManageBlogsPage(){return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50",children:[a.jsx(r.Z,{}),a.jsx("div",{className:"pt-20 pb-16 px-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[a.jsx("h1",{className:"text-4xl font-bold text-slate-900 mb-4",children:"Manage Blogs"}),a.jsx("p",{className:"text-xl text-slate-600",children:"Manage your blog posts"})]})}),a.jsx(n.Z,{})]})}},7172:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>l});var a=s(5153);let r=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/manage-blogs/page.tsx`),{__esModule:n,$$typeof:o}=r,i=r.default,l=i}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[918,176,447,816],()=>__webpack_exec__(9154));module.exports=s})();