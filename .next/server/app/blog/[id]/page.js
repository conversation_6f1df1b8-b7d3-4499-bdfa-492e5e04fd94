(()=>{var e={};e.id=548,e.ids=[548],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2447:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=t(7096),r=t(6132),l=t(7284),n=t.n(l),i=t(2564),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let c=["",{children:["blog",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3479)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,8275)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found.tsx"]}],d=["/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/[id]/page.tsx"],x="/blog/[id]/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/blog/[id]/page",pathname:"/blog/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5463:(e,s,t)=>{Promise.resolve().then(t.bind(t,7160))},7160:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>BlogPostPage});var a=t(784),r=t(9885),l=t(1890),n=t(5555),i=t(1440),o=t.n(i),c=t(8282),d=t(6593),x=t(9803),m=t(517),p=t(5441),h=t(3680),g=t(6386),u=t(7371);function BlogPostPage({params:e}){let[s,t]=(0,r.useState)(null),[i,b]=(0,r.useState)(!0),[j,v]=(0,r.useState)("");return((0,r.useEffect)(()=>{let getParams=async()=>{let s=await e;v(s.id)};getParams()},[e]),(0,r.useEffect)(()=>{if(!j)return;let fetchPost=async()=>{try{let e=await fetch("/data/blogs.json"),s=await e.json(),a=s.find(e=>e.id===parseInt(j));t(a||null)}catch(e){console.error("Error fetching blog post:",e)}finally{b(!1)}};fetchPost()},[j]),i)?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[a.jsx(c.Z,{}),a.jsx("div",{className:"pt-20 pb-16 px-4 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading article..."})]})}),a.jsx(d.Z,{})]}):s?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[a.jsx(c.Z,{}),(0,a.jsxs)("section",{className:"relative pt-20 pb-16 px-4 overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20"}),(0,a.jsxs)("div",{className:"relative max-w-4xl mx-auto",children:[a.jsx("div",{className:"mb-8",children:a.jsx(o(),{href:"/blog",children:(0,a.jsxs)(n.z,{variant:"outline",className:"border-gray-200 text-gray-700 hover:bg-blue-50",children:[a.jsx(x.Z,{className:"w-4 h-4 mr-2"}),"Back to Blog"]})})}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(l.C,{className:"bg-blue-100 text-blue-800",children:s.category}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-gray-500 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(m.Z,{className:"w-4 h-4"}),a.jsx("span",{children:(e=>{let s=new Date(e);return s.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})(s.date)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(p.Z,{className:"w-4 h-4"}),a.jsx("span",{children:s.readTime})]})]})]}),a.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 leading-tight",children:s.title}),a.jsx("p",{className:"text-xl text-gray-600 leading-relaxed",children:s.excerpt}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(h.Z,{className:"w-5 h-5 text-gray-400"}),a.jsx("span",{className:"text-gray-600 font-medium",children:s.author})]}),(0,a.jsxs)(n.z,{onClick:()=>{navigator.share&&s?navigator.share({title:s.title,text:s.excerpt,url:window.location.href}):(navigator.clipboard.writeText(window.location.href),alert("Link copied to clipboard!"))},variant:"outline",className:"border-gray-200 text-gray-700 hover:bg-blue-50",children:[a.jsx(g.Z,{className:"w-4 h-4 mr-2"}),"Share"]})]})]})]})]}),a.jsx("section",{className:"px-4 pb-16",children:a.jsx("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"relative h-64 md:h-96 rounded-3xl overflow-hidden shadow-2xl",children:[a.jsx("img",{src:s.image,alt:s.title,className:"w-full h-full object-cover"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]})})}),a.jsx("section",{className:"px-4 pb-16",children:a.jsx("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl p-8 md:p-12",children:[a.jsx("div",{className:"prose prose-lg max-w-none",children:a.jsx("div",{className:"text-gray-700 leading-relaxed space-y-6",children:s.content.split("\n").map((e,s)=>e.trim()&&a.jsx("p",{className:"text-lg leading-relaxed",children:e},s))})}),(0,a.jsxs)("div",{className:"mt-12 pt-8 border-t border-gray-200",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Tags"}),a.jsx("div",{className:"flex flex-wrap gap-2",children:s.tags.map((e,s)=>a.jsx(l.C,{variant:"secondary",className:"bg-gray-100 text-gray-600",children:e},s))})]})]})})}),(0,a.jsxs)("section",{className:"py-20 relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700"}),a.jsx("div",{className:"absolute inset-0 bg-black/20"}),a.jsx("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,a.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white leading-tight",children:["Have Questions About",a.jsx("br",{}),a.jsx("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent",children:"Your Pet's Health?"})]}),a.jsx("p",{className:"text-xl text-blue-100 leading-relaxed",children:"Contact Nolari for personalized advice and professional veterinary care"}),a.jsx(n.z,{onClick:()=>{let e=`Hi Nolari! I just read your article "${s.title}" and have some questions about my pet's health. Could you please help me?`,t=`https://wa.me/254718376311?text=${encodeURIComponent(e)}`;window.open(t,"_blank")},className:"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 text-lg font-semibold",children:(0,a.jsxs)("span",{className:"flex items-center space-x-2",children:[a.jsx(u.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Ask Nolari"})]})})]})})]}),a.jsx(d.Z,{})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[a.jsx(c.Z,{}),a.jsx("div",{className:"pt-20 pb-16 px-4",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[a.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Article Not Found"}),a.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"The article you're looking for doesn't exist."}),a.jsx(o(),{href:"/blog",children:(0,a.jsxs)(n.z,{className:"bg-gradient-to-r from-blue-600 to-pink-600 text-white",children:[a.jsx(x.Z,{className:"w-4 h-4 mr-2"}),"Back to Blog"]})})]})}),a.jsx(d.Z,{})]})}},3479:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>n,__esModule:()=>l,default:()=>o});var a=t(5153);let r=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/[id]/page.tsx`),{__esModule:l,$$typeof:n}=r,i=r.default,o=i}};var s=require("../../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[918,176,936,447,816,890],()=>__webpack_exec__(2447));module.exports=t})();