globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/blog/[id]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"2426":{"*":{"id":"3384","name":"*","chunks":[],"async":false}},"3170":{"*":{"id":"4714","name":"*","chunks":[],"async":false}},"3728":{"*":{"id":"3724","name":"*","chunks":[],"async":false}},"5925":{"*":{"id":"9086","name":"*","chunks":[],"async":false}},"6662":{"*":{"id":"6683","name":"*","chunks":[],"async":false}},"6833":{"*":{"id":"3710","name":"*","chunks":[],"async":false}},"6954":{"*":{"id":"4900","name":"*","chunks":[],"async":false}},"7264":{"*":{"id":"5392","name":"*","chunks":[],"async":false}},"8297":{"*":{"id":"8898","name":"*","chunks":[],"async":false}},"9232":{"*":{"id":"7160","name":"*","chunks":[],"async":false}},"9928":{"*":{"id":"5365","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/app-router.js":{"id":3728,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/esm/client/components/app-router.js":{"id":3728,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/error-boundary.js":{"id":9928,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":9928,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/layout-router.js":{"id":6954,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/esm/client/components/layout-router.js":{"id":6954,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/not-found-boundary.js":{"id":3170,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":3170,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/render-from-template-context.js":{"id":7264,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":7264,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":8297,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":8297,"name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":3925,"name":"*","chunks":["442","static/chunks/442-4621eb6f47920d0a.js","584","static/chunks/584-c7f8ea6ea12cfb49.js","581","static/chunks/581-db29025c8cab965d.js","185","static/chunks/app/layout-8b45c930d85695aa.js"],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/providers.tsx":{"id":6833,"name":"*","chunks":["442","static/chunks/442-4621eb6f47920d0a.js","584","static/chunks/584-c7f8ea6ea12cfb49.js","581","static/chunks/581-db29025c8cab965d.js","185","static/chunks/app/layout-8b45c930d85695aa.js"],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/index.css":{"id":302,"name":"*","chunks":["442","static/chunks/442-4621eb6f47920d0a.js","584","static/chunks/584-c7f8ea6ea12cfb49.js","581","static/chunks/581-db29025c8cab965d.js","185","static/chunks/app/layout-8b45c930d85695aa.js"],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/components/AppointmentProvider.tsx":{"id":5925,"name":"*","chunks":["442","static/chunks/442-4621eb6f47920d0a.js","584","static/chunks/584-c7f8ea6ea12cfb49.js","581","static/chunks/581-db29025c8cab965d.js","185","static/chunks/app/layout-8b45c930d85695aa.js"],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page.tsx":{"id":6662,"name":"*","chunks":["442","static/chunks/442-4621eb6f47920d0a.js","169","static/chunks/169-646916bef6f26d61.js","581","static/chunks/581-db29025c8cab965d.js","747","static/chunks/747-29f40d16d9b618b4.js","931","static/chunks/app/page-90cd169747fdbe69.js"],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx":{"id":2426,"name":"*","chunks":["442","static/chunks/442-4621eb6f47920d0a.js","169","static/chunks/169-646916bef6f26d61.js","581","static/chunks/581-db29025c8cab965d.js","747","static/chunks/747-29f40d16d9b618b4.js","404","static/chunks/app/blog/page-5726b66e60db1b17.js"],"async":false},"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/[id]/page.tsx":{"id":9232,"name":"*","chunks":["442","static/chunks/442-4621eb6f47920d0a.js","169","static/chunks/169-646916bef6f26d61.js","581","static/chunks/581-db29025c8cab965d.js","747","static/chunks/747-29f40d16d9b618b4.js","548","static/chunks/app/blog/%5Bid%5D/page-91d1f940a605fde2.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found":[],"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/layout":["static/css/ba381b3bf71932af.css"],"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/page":[],"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page":[],"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/[id]/page":[]}}