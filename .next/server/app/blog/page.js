(()=>{var e={};e.id=404,e.ids=[404],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4964:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=t(7096),r=t(6132),l=t(7284),i=t.n(l),n=t(2564),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c=["",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3416)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,8275)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,4293)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found.tsx"]}],d=["/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx"],x="/blog/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/blog/page",pathname:"/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5993:(e,s,t)=>{Promise.resolve().then(t.bind(t,3384))},3384:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>BlogPage});var a=t(784),r=t(9885),l=t(5555),i=t(599),n=t(1890),o=t(1440),c=t.n(o),d=t(8282),x=t(6593),m=t(8937),h=t(517),p=t(5441),g=t(3680),u=t(7631),b=t(7371);function BlogPage(){let[e,s]=(0,r.useState)([]),[t,o]=(0,r.useState)(!0),[j,v]=(0,r.useState)("All");(0,r.useEffect)(()=>{let fetchPosts=async()=>{try{let e=await fetch("/data/blogs.json"),t=await e.json();s(t)}catch(e){console.error("Error fetching blog posts:",e)}finally{o(!1)}};fetchPosts()},[]);let f=["All",...Array.from(new Set(e.map(e=>e.category)))],N="All"===j?e:e.filter(e=>e.category===j),formatDate=e=>{let s=new Date(e);return s.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})};return t?(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[a.jsx(d.Z,{}),a.jsx("div",{className:"pt-20 pb-16 px-4 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"Loading blog posts..."})]})}),a.jsx(x.Z,{})]}):(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[a.jsx(d.Z,{}),(0,a.jsxs)("section",{className:"relative pt-20 pb-16 px-4 overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20"}),a.jsx("div",{className:"relative max-w-7xl mx-auto text-center",children:a.jsx("div",{className:"space-y-8",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium",children:[a.jsx(m.Z,{className:"w-4 h-4"}),"Veterinary Insights & Tips"]}),(0,a.jsxs)("h1",{className:"text-5xl lg:text-6xl font-bold text-gray-900 leading-tight",children:["Expert Advice for",a.jsx("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-pink-500",children:" Pet Care"})]}),a.jsx("p",{className:"text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto",children:"Stay informed with the latest veterinary insights, pet care tips, and health advice from Nolari."})]})})})]}),a.jsx("section",{className:"py-8 px-4",children:a.jsx("div",{className:"max-w-7xl mx-auto",children:a.jsx("div",{className:"flex flex-wrap justify-center gap-4",children:f.map(e=>a.jsx(l.z,{variant:j===e?"default":"outline",onClick:()=>v(e),className:`rounded-full px-6 py-2 transition-all duration-300 ${j===e?"bg-gradient-to-r from-blue-600 to-pink-600 text-white":"border-gray-200 text-gray-700 hover:bg-blue-50"}`,children:e},e))})})}),N.length>0&&a.jsx("section",{className:"py-16 px-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[a.jsx("div",{className:"text-center mb-12",children:a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Featured Article"})}),a.jsx(i.Zb,{className:"group relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-2xl rounded-3xl hover:shadow-3xl transition-all duration-500",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2",children:[(0,a.jsxs)("div",{className:"relative h-64 lg:h-full",children:[a.jsx("img",{src:N[0].image,alt:N[0].title,className:"w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-pink-600/20"})]}),(0,a.jsxs)(i.aY,{className:"p-12 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(n.C,{className:"bg-blue-100 text-blue-800",children:N[0].category}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500 text-sm",children:[a.jsx(h.Z,{className:"w-4 h-4"}),a.jsx("span",{children:formatDate(N[0].date)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-gray-500 text-sm",children:[a.jsx(p.Z,{className:"w-4 h-4"}),a.jsx("span",{children:N[0].readTime})]})]}),a.jsx("h3",{className:"text-3xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-300",children:N[0].title}),a.jsx("p",{className:"text-gray-600 leading-relaxed text-lg",children:N[0].excerpt}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(g.Z,{className:"w-5 h-5 text-gray-400"}),a.jsx("span",{className:"text-gray-600 font-medium",children:N[0].author})]}),a.jsx(c(),{href:`/blog/${N[0].id}`,children:a.jsx(l.z,{className:"bg-gradient-to-r from-blue-600 to-pink-600 hover:from-blue-700 hover:to-pink-700 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300",children:(0,a.jsxs)("span",{className:"flex items-center space-x-2",children:[a.jsx("span",{children:"Read Full Article"}),a.jsx(u.Z,{className:"w-4 h-4"})]})})})]})]})})]})}),a.jsx("section",{className:"py-16 px-4",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[a.jsx("div",{className:"text-center mb-12",children:a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Latest Articles"})}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:N.slice(1).map(e=>(0,a.jsxs)(i.Zb,{className:"group relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl",children:[(0,a.jsxs)("div",{className:"relative h-48 overflow-hidden rounded-t-3xl",children:[a.jsx("img",{src:e.image,alt:e.title,className:"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"}),a.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),a.jsx("div",{className:"absolute top-4 left-4",children:a.jsx(n.C,{className:"bg-blue-100 text-blue-800",children:e.category})})]}),(0,a.jsxs)(i.Ol,{className:"pb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500 mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(h.Z,{className:"w-3 h-3"}),a.jsx("span",{children:formatDate(e.date)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[a.jsx(p.Z,{className:"w-3 h-3"}),a.jsx("span",{children:e.readTime})]})]}),a.jsx(i.ll,{className:"text-xl font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2",children:e.title})]}),(0,a.jsxs)(i.aY,{className:"space-y-4",children:[a.jsx("p",{className:"text-gray-600 text-sm leading-relaxed line-clamp-3",children:e.excerpt}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[a.jsx(g.Z,{className:"w-4 h-4"}),a.jsx("span",{children:e.author})]}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e.tags.slice(0,3).map((e,s)=>a.jsx(n.C,{variant:"secondary",className:"text-xs bg-gray-100 text-gray-600",children:e},s))}),a.jsx(c(),{href:`/blog/${e.id}`,children:a.jsx(l.z,{className:"w-full bg-gradient-to-r from-blue-600 to-pink-600 hover:from-blue-700 hover:to-pink-700 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300",children:(0,a.jsxs)("span",{className:"flex items-center space-x-2",children:[a.jsx("span",{children:"Read More"}),a.jsx(u.Z,{className:"w-4 h-4"})]})})})]})]},e.id))})]})}),(0,a.jsxs)("section",{className:"py-20 relative overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700"}),a.jsx("div",{className:"absolute inset-0 bg-black/20"}),a.jsx("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,a.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white leading-tight",children:["Stay Updated with",a.jsx("br",{}),a.jsx("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent",children:"Pet Care Tips"})]}),a.jsx("p",{className:"text-xl text-blue-100 leading-relaxed",children:"Get the latest veterinary insights and pet care advice delivered straight to your WhatsApp"}),a.jsx(l.z,{onClick:()=>{let e=`https://wa.me/254718376311?text=${encodeURIComponent("Hi Nolari! I'd like to stay updated with your latest pet care tips and veterinary insights. Please add me to your updates list.")}`;window.open(e,"_blank")},className:"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 text-lg font-semibold",children:(0,a.jsxs)("span",{className:"flex items-center space-x-2",children:[a.jsx(b.Z,{className:"w-5 h-5"}),a.jsx("span",{children:"Subscribe for Updates"})]})})]})})]}),a.jsx(x.Z,{})]})}},3416:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>l,default:()=>o});var a=t(5153);let r=(0,a.createProxy)(String.raw`/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/blog/page.tsx`),{__esModule:l,$$typeof:i}=r,n=r.default,o=n}};var s=require("../../webpack-runtime.js");s.C(e);var __webpack_exec__=e=>s(s.s=e),t=s.X(0,[918,176,880,447,816,599,890],()=>__webpack_exec__(4964));module.exports=t})();