1:HL["/_next/static/media/e4af272ccee01ff0-s.p.woff2","font",{"crossOrigin":"","type":"font/woff2"}]
2:HL["/_next/static/css/d57437081b84912a.css","style",{"crossOrigin":""}]
0:["9zx9lXP-P8Mlnspxd6iCq",[[["",{"children":["manage-blogs",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],"$L3",[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/d57437081b84912a.css","precedence":"next","crossOrigin":""}]],"$L4"]]]]
5:I[6833,["442","static/chunks/442-4621eb6f47920d0a.js","584","static/chunks/584-c7f8ea6ea12cfb49.js","581","static/chunks/581-a9fe7333d45c07fc.js","185","static/chunks/app/layout-d5bfd3d83fa677ef.js"],"Providers"]
6:I[5925,["442","static/chunks/442-4621eb6f47920d0a.js","584","static/chunks/584-c7f8ea6ea12cfb49.js","581","static/chunks/581-a9fe7333d45c07fc.js","185","static/chunks/app/layout-d5bfd3d83fa677ef.js"],"AppointmentProvider"]
7:I[6954,[],""]
8:I[7264,[],""]
a:I[8297,[],""]
b:I[3026,["442","static/chunks/442-4621eb6f47920d0a.js","169","static/chunks/169-646916bef6f26d61.js","581","static/chunks/581-a9fe7333d45c07fc.js","747","static/chunks/747-29f40d16d9b618b4.js","800","static/chunks/app/manage-blogs/page-473a7f5ec6b9f23e.js"],""]
3:[null,["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__className_e8ce0c","children":["$","$L5",null,{"children":["$","$L6",null,{"children":["$","$L7",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","template":["$","$L8",null,{}],"templateStyles":"$undefined","notFound":["$","div",null,{"className":"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50","children":["$","div",null,{"className":"text-center space-y-6","children":[["$","h1",null,{"className":"text-6xl font-bold text-slate-900","children":"404"}],["$","h2",null,{"className":"text-2xl font-semibold text-slate-700","children":"Page Not Found"}],["$","p",null,{"className":"text-lg text-slate-600 max-w-md mx-auto","children":"Sorry, we couldn't find the page you're looking for."}],["$","a",null,{"href":"/","className":"inline-block bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-3 rounded-xl font-medium transition-all duration-300 hover:scale-105","children":"Return Home"}]]}]}],"notFoundStyles":[],"childProp":{"current":["$","$L7",null,{"parallelRouterKey":"children","segmentPath":["children","manage-blogs","children"],"loading":"$undefined","loadingStyles":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","template":["$","$L8",null,{}],"templateStyles":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","childProp":{"current":["$L9",["$","$La",null,{"propsForComponent":{"params":{}},"Component":"$b","isStaticGeneration":true}],null],"segment":"__PAGE__"},"styles":[]}],"segment":"manage-blogs"},"styles":[]}]}]}]}]}],null]
4:[["$","meta","0",{"charSet":"utf-8"}],["$","title","1",{"children":"cvets"}],["$","meta","2",{"name":"description","content":"CVETS Veterinary Services provides comprehensive pet care including health check-ups, surgical procedures, dental care, and emergency services. Led by Dr Cynthia."}],["$","meta","3",{"name":"author","content":"CVETS Veterinary Services"}],["$","link","4",{"rel":"manifest","href":"/site.webmanifest"}],["$","meta","5",{"name":"keywords","content":"veterinary, pet care, animal health, Dr Cynthia, CVETS, veterinary clinic"}],["$","meta","6",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","7",{"name":"creator","content":"CVETS Veterinary Services"}],["$","meta","8",{"name":"publisher","content":"CVETS Veterinary Services"}],["$","meta","9",{"name":"robots","content":"index, follow"}],["$","meta","10",{"property":"og:title","content":"CVETS - Professional Veterinary Services"}],["$","meta","11",{"property":"og:description","content":"CVETS Veterinary Services provides comprehensive pet care including health check-ups, surgical procedures, dental care, and emergency services. Led by Dr Cynthia."}],["$","meta","12",{"property":"og:url","content":"https://cvets-veterinary.vercel.app/"}],["$","meta","13",{"property":"og:site_name","content":"CVETS Veterinary Services"}],["$","meta","14",{"property":"og:locale","content":"en_KE"}],["$","meta","15",{"property":"og:image","content":"https://lovable.dev/opengraph-image-p98pqg.png"}],["$","meta","16",{"property":"og:image:width","content":"1200"}],["$","meta","17",{"property":"og:image:height","content":"630"}],["$","meta","18",{"property":"og:image:alt","content":"CVETS Veterinary Services - Professional Pet Care"}],["$","meta","19",{"property":"og:type","content":"website"}],["$","meta","20",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","21",{"name":"twitter:site","content":"@cvets"}],["$","meta","22",{"name":"twitter:creator","content":"@cvets"}],["$","meta","23",{"name":"twitter:title","content":"CVETS - Professional Veterinary Services"}],["$","meta","24",{"name":"twitter:description","content":"CVETS Veterinary Services provides comprehensive pet care including health check-ups, surgical procedures, dental care, and emergency services."}],["$","meta","25",{"name":"twitter:image","content":"https://lovable.dev/opengraph-image-p98pqg.png"}],["$","link","26",{"rel":"shortcut icon","href":"/favicon-16x16.png"}],["$","link","27",{"rel":"icon","href":"/favicon.ico"}],["$","link","28",{"rel":"apple-touch-icon","href":"/apple-touch-icon.png"}],["$","meta","29",{"name":"next-size-adjust"}]]
9:null
