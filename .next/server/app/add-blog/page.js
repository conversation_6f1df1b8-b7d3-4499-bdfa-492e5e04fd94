(()=>{var e={};e.id=100,e.ids=[100],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},209:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(7096),a=s(6132),n=s(7284),o=s.n(n),i=s(2564),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d=["",{children:["add-blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,235)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/add-blog/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,8275)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,4293)),"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/not-found.tsx"]}],c=["/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/add-blog/page.tsx"],p="/add-blog/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/add-blog/page",pathname:"/add-blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8928:(e,t,s)=>{Promise.resolve().then(s.bind(s,7891))},7891:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>AddBlogPage});var r=s(784),a=s(8282),n=s(6593);function AddBlogPage(){return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50",children:[r.jsx(a.Z,{}),r.jsx("div",{className:"pt-20 pb-16 px-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[r.jsx("h1",{className:"text-4xl font-bold text-slate-900 mb-4",children:"Add Blog Post"}),r.jsx("p",{className:"text-xl text-slate-600",children:"Create a new blog post"})]})}),r.jsx(n.Z,{})]})}},235:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>l});var r=s(5153);let a=(0,r.createProxy)(String.raw`/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/app/add-blog/page.tsx`),{__esModule:n,$$typeof:o}=a,i=a.default,l=i}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[918,176,447,816],()=>__webpack_exec__(209));module.exports=s})();