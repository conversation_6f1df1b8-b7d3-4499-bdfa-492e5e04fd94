(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[286],{4530:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r.Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7431:function(e,t,a){Promise.resolve().then(a.bind(a,8148))},8148:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return ProductsPage}});var r=a(7437),s=a(2265),l=a(3611),n=a(9598),o=a(708),i=a(3827),c=a(8266),d=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,d.Z)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["path",{d:"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7",key:"yx3hmr"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);var m=a(4530);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,d.Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),h=(0,d.Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var p=a(7810);function ProductsPage(){let[e,t]=(0,s.useState)([]),[a,d]=(0,s.useState)(!0),[f,g]=(0,s.useState)("All");(0,s.useEffect)(()=>{let fetchProducts=async()=>{try{let e=await fetch("/data/products.json"),a=await e.json();t(a)}catch(e){console.error("Error fetching products:",e)}finally{d(!1)}};fetchProducts()},[]);let b=["All",...Array.from(new Set(e.map(e=>e.category)))],v="All"===f?e:e.filter(e=>e.category===f),handleWhatsAppOrder=e=>{let t="Hi! I'm interested in ordering the ".concat(e.name,". Could you please provide more information about availability and delivery?"),a="https://wa.me/".concat("254718376311","?text=").concat(encodeURIComponent(t));window.open(a,"_blank")};return a?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[(0,r.jsx)(i.Z,{}),(0,r.jsx)("div",{className:"pt-20 pb-16 px-4 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading products..."})]})}),(0,r.jsx)(c.Z,{})]}):(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[(0,r.jsx)(i.Z,{}),(0,r.jsxs)("section",{className:"relative pt-20 pb-16 px-4 overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20"}),(0,r.jsx)("div",{className:"relative max-w-7xl mx-auto text-center",children:(0,r.jsx)("div",{className:"space-y-8",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium",children:[(0,r.jsx)(x,{className:"w-4 h-4"}),"Quality Veterinary Products"]}),(0,r.jsxs)("h1",{className:"text-5xl lg:text-6xl font-bold text-gray-900 leading-tight",children:["Premium Products for",(0,r.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-pink-500",children:" Your Pet's Health"})]}),(0,r.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto",children:"Discover our carefully selected range of veterinary products designed to keep your pets healthy, happy, and comfortable."})]})})})]}),(0,r.jsx)("section",{className:"py-8 px-4",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsx)("div",{className:"flex flex-wrap justify-center gap-4",children:b.map(e=>(0,r.jsx)(l.z,{variant:f===e?"default":"outline",onClick:()=>g(e),className:"rounded-full px-6 py-2 transition-all duration-300 ".concat(f===e?"bg-gradient-to-r from-blue-600 to-pink-600 text-white":"border-gray-200 text-gray-700 hover:bg-blue-50"),children:e},e))})})}),(0,r.jsx)("section",{className:"py-16 px-4",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8",children:v.map(e=>(0,r.jsxs)(n.Zb,{className:"group relative overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl",children:[(0,r.jsxs)("div",{className:"relative h-48 overflow-hidden rounded-t-3xl",children:[(0,r.jsx)("img",{src:e.image,alt:e.name,className:"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),(0,r.jsx)("div",{className:"absolute top-4 right-4",children:e.inStock?(0,r.jsxs)(o.C,{className:"bg-green-100 text-green-800 border-green-200",children:[(0,r.jsx)(m.Z,{className:"w-3 h-3 mr-1"}),"In Stock"]}):(0,r.jsxs)(o.C,{className:"bg-red-100 text-red-800 border-red-200",children:[(0,r.jsx)(u,{className:"w-3 h-3 mr-1"}),"Out of Stock"]})}),(0,r.jsx)("div",{className:"absolute top-4 left-4",children:(0,r.jsx)(o.C,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:e.category})})]}),(0,r.jsx)(n.Ol,{className:"pb-2",children:(0,r.jsx)(n.ll,{className:"text-lg font-bold text-gray-800 group-hover:text-blue-600 transition-colors duration-300",children:e.name})}),(0,r.jsxs)(n.aY,{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed",children:e.description}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-800 text-sm",children:"Features:"}),(0,r.jsx)("ul",{className:"space-y-1",children:e.features.slice(0,3).map((e,t)=>(0,r.jsxs)("li",{className:"flex items-center space-x-2 text-xs text-gray-600",children:[(0,r.jsx)(m.Z,{className:"w-3 h-3 text-green-600 flex-shrink-0"}),(0,r.jsx)("span",{children:e})]},t))})]}),(0,r.jsx)(l.z,{onClick:()=>handleWhatsAppOrder(e),disabled:!e.inStock,className:"w-full rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ".concat(e.inStock?"bg-gradient-to-r from-blue-600 to-pink-600 hover:from-blue-700 hover:to-pink-700 text-white":"bg-gray-300 text-gray-500 cursor-not-allowed"),children:e.inStock?(0,r.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Order via WhatsApp"})]}):(0,r.jsx)("span",{children:"Out of Stock"})})]})]},e.id))})})}),(0,r.jsxs)("section",{className:"py-20 relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,r.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,r.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white leading-tight",children:["Need Help Choosing",(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent",children:"The Right Product?"})]}),(0,r.jsx)("p",{className:"text-xl text-blue-100 leading-relaxed",children:"Contact Nolari for personalized product recommendations for your pet's specific needs"}),(0,r.jsx)(l.z,{onClick:()=>{let e="https://wa.me/254718376311?text=".concat(encodeURIComponent("Hi Nolari! I need help choosing the right products for my pet. Could you please provide some recommendations?"));window.open(e,"_blank")},className:"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 text-lg font-semibold",children:(0,r.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.Z,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Get Expert Advice"})]})})]})})]}),(0,r.jsx)(c.Z,{})]})}},708:function(e,t,a){"use strict";a.d(t,{C:function(){return Badge}});var r=a(7437);a(2265);var s=a(6061),l=a(1628);let n=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Badge(e){let{className:t,variant:a,...s}=e;return(0,r.jsx)("div",{className:(0,l.cn)(n({variant:a}),t),...s})}},9598:function(e,t,a){"use strict";a.d(t,{Ol:function(){return o},Zb:function(){return n},aY:function(){return d},ll:function(){return i}});var r=a(7437),s=a(2265),l=a(1628);let n=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});n.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...s})});o.displayName="CardHeader";let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})});i.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",a),...s})});d.displayName="CardContent";let x=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",a),...s})});x.displayName="CardFooter"}},function(e){e.O(0,[442,169,581,747,971,472,744],function(){return e(e.s=7431)}),_N_E=e.O()}]);