(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[327],{8731:function(e,t,s){Promise.resolve().then(s.bind(s,9936))},9936:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return ContactPage}});var n=s(7437),a=s(3827),c=s(8266);function ContactPage(){return(0,n.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[(0,n.jsx)(a.Z,{}),(0,n.jsx)("div",{className:"pt-20 pb-16 px-4",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,n.jsx)("h1",{className:"text-4xl font-bold text-slate-900 mb-4",children:"Contact Us"}),(0,n.jsx)("p",{className:"text-xl text-slate-600",children:"Get in touch with CVETS Veterinary Services"}),(0,n.jsxs)("div",{className:"mt-8",children:[(0,n.jsx)("p",{className:"text-lg text-gray-700",children:"Phone: 0718376311"}),(0,n.jsx)("p",{className:"text-lg text-gray-700",children:"Email: <EMAIL>"})]})]})}),(0,n.jsx)(c.Z,{})]})}}},function(e){e.O(0,[442,169,581,747,971,472,744],function(){return e(e.s=8731)}),_N_E=e.O()}]);