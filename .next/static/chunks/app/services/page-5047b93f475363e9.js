(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[469],{4530:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2695:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Scissors",[["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M8.12 8.12 12 12",key:"1alkpv"}],["path",{d:"M20 4 8.12 15.88",key:"xgtan2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M14.8 14.8 20 20",key:"ptml3r"}]])},7607:function(e,s,t){"use strict";t.d(s,{Z:function(){return r}});var a=t(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a.Z)("Stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]])},4433:function(e,s,t){Promise.resolve().then(t.bind(t,4646))},4646:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return ServicesPage}});var a=t(7437),r=t(2265),i=t(3611),l=t(9598),n=t(3827),o=t(8266),c=t(5925),d=t(1628),m=t(7607),x=t(2695),u=t(7810),h=t(6141),p=t(4530);function ServicesPage(){let{openAppointmentModal:e}=(0,c.useAppointment)();(0,r.useEffect)(()=>{let e=window.location.hash;if(e){let s=e.substring(1);setTimeout(()=>{(0,d.BK)(s)},100)}},[]);let s=[{category:"Medical Care",gradient:"from-blue-50 to-cyan-50",icon:m.Z,items:[{name:"Health Check-ups",description:"Comprehensive physical examinations to assess your pet's overall health and detect early signs of illness.",duration:"45-60 minutes",features:["Physical examination","Weight and vital signs","Health assessment","Preventive care recommendations"]},{name:"Diagnostic Testing",description:"Advanced diagnostic services including blood work, urinalysis, and other laboratory tests.",duration:"30-45 minutes",features:["Blood chemistry panels","Complete blood count","Urinalysis","Parasite screening"]},{name:"Emergency Care",description:"Immediate medical attention for urgent health situations and critical care needs.",duration:"Variable",features:["24/7 availability","Urgent care","Stabilization","Emergency treatment"]}]},{category:"Surgical Services",gradient:"from-green-50 to-emerald-50",icon:x.Z,items:[{name:"Spaying & Neutering",description:"Professional sterilization procedures to prevent unwanted pregnancies and health issues.",duration:"1-2 hours",features:["Pre-surgical examination","Anesthesia monitoring","Post-operative care","Pain management"]},{name:"Soft Tissue Surgery",description:"Various surgical procedures for treating injuries, masses, and other conditions.",duration:"1-3 hours",features:["Tumor removal","Wound repair","Abscess treatment","Foreign body removal"]}]}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[(0,a.jsx)(n.Z,{}),(0,a.jsxs)("section",{className:"relative pt-20 pb-16 px-4 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600/10 via-cyan-500/5 to-slate-100/20"}),(0,a.jsx)("div",{className:"relative max-w-7xl mx-auto text-center",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium",children:[(0,a.jsx)(u.Z,{className:"w-4 h-4"}),"Professional Veterinary Services"]}),(0,a.jsxs)("h1",{className:"text-5xl lg:text-6xl font-bold text-slate-900 leading-tight",children:["Comprehensive Care for",(0,a.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600",children:" Your Pet"})]}),(0,a.jsx)("p",{className:"text-xl text-slate-600 leading-relaxed max-w-3xl mx-auto",children:"CVETS offers a full range of veterinary services delivered with compassion and expertise. From routine check-ups to emergency care, we're here for your pet's every need."})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[(0,a.jsxs)(i.z,{onClick:e,size:"lg",className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:[(0,a.jsx)("span",{children:"Book Appointment"}),(0,a.jsx)(h.Z,{className:"w-5 h-5 ml-2"})]}),(0,a.jsx)(i.z,{asChild:!0,variant:"outline",size:"lg",className:"border-2 border-slate-200 bg-white/70 backdrop-blur-sm text-slate-700 hover:bg-slate-50 hover:border-slate-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300",children:(0,a.jsx)("a",{href:"tel:0718376311",className:"flex items-center space-x-2",children:(0,a.jsx)("span",{children:"Emergency: 0718376311"})})})]})]})})]}),(0,a.jsxs)("section",{id:"services-list",className:"py-32 relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50"}),(0,a.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"space-y-20",children:s.map((s,t)=>(0,a.jsxs)("div",{className:"space-y-12",children:[(0,a.jsx)("div",{className:"text-center space-y-4",children:(0,a.jsxs)("div",{className:"inline-flex items-center space-x-3 bg-gradient-to-r ".concat(s.gradient," rounded-2xl px-8 py-4"),children:[(0,a.jsx)(s.icon,{className:"w-8 h-8 text-slate-700"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-slate-800",children:s.category})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:s.items.map((s,t)=>(0,a.jsxs)(l.Zb,{className:"group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl",children:[(0,a.jsxs)(l.Ol,{className:"pb-4",children:[(0,a.jsx)(l.ll,{className:"text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300",children:s.name}),(0,a.jsx)("p",{className:"text-slate-600 leading-relaxed",children:s.description})]}),(0,a.jsxs)(l.aY,{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsx)("div",{className:"space-y-1",children:(0,a.jsx)("p",{className:"text-sm text-slate-500",children:s.duration})})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-semibold text-slate-800",children:"Includes:"}),(0,a.jsx)("ul",{className:"space-y-2",children:s.features.map((e,s)=>(0,a.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.Z,{className:"w-4 h-4 text-green-600 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-slate-600 text-sm",children:e})]},s))})]}),(0,a.jsx)(i.z,{onClick:e,className:"w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300",children:"Book This Service"})]})]},t))})]},t))})})]}),(0,a.jsx)(o.Z,{})]})}},9598:function(e,s,t){"use strict";t.d(s,{Ol:function(){return n},Zb:function(){return l},aY:function(){return d},ll:function(){return o}});var a=t(7437),r=t(2265),i=t(1628);let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});l.displayName="Card";let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...r})});n.displayName="CardHeader";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",t),...r})});d.displayName="CardContent";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"}},function(e){e.O(0,[442,169,581,747,971,472,744],function(){return e(e.s=4433)}),_N_E=e.O()}]);