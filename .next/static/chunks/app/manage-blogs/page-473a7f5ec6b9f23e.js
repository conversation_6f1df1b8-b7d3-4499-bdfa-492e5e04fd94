(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[800],{6806:function(e,s,n){Promise.resolve().then(n.bind(n,3026))},3026:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return ManageBlogsPage}});var t=n(7437),a=n(3827),l=n(8266);function ManageBlogsPage(){return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50",children:[(0,t.jsx)(a.Z,{}),(0,t.jsx)("div",{className:"pt-20 pb-16 px-4",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-slate-900 mb-4",children:"Manage Blogs"}),(0,t.jsx)("p",{className:"text-xl text-slate-600",children:"Manage your blog posts"})]})}),(0,t.jsx)(l.Z,{})]})}}},function(e){e.O(0,[442,169,581,747,971,472,744],function(){return e(e.s=6806)}),_N_E=e.O()}]);