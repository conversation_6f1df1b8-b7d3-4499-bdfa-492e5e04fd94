(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[548],{8203:function(e,a,t){"use strict";t.d(a,{Z:function(){return r}});var s=t(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},1456:function(e,a,t){Promise.resolve().then(t.bind(t,9232))},9232:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return BlogPostPage}});var s=t(7437),r=t(2265),l=t(708),n=t(3611),i=t(1396),c=t.n(i),o=t(3827),d=t(8266),x=t(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,x.Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var h=t(8203),u=t(6141),g=t(7972);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let p=(0,x.Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var b=t(7810);function BlogPostPage(e){let{params:a}=e,[t,i]=(0,r.useState)(null),[x,f]=(0,r.useState)(!0),[j,v]=(0,r.useState)("");return((0,r.useEffect)(()=>{let getParams=async()=>{let e=await a;v(e.id)};getParams()},[a]),(0,r.useEffect)(()=>{if(!j)return;let fetchPost=async()=>{try{let e=await fetch("/data/blogs.json"),a=await e.json(),t=a.find(e=>e.id===parseInt(j));i(t||null)}catch(e){console.error("Error fetching blog post:",e)}finally{f(!1)}};fetchPost()},[j]),x)?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[(0,s.jsx)(o.Z,{}),(0,s.jsx)("div",{className:"pt-20 pb-16 px-4 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Loading article..."})]})}),(0,s.jsx)(d.Z,{})]}):t?(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[(0,s.jsx)(o.Z,{}),(0,s.jsxs)("section",{className:"relative pt-20 pb-16 px-4 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20"}),(0,s.jsxs)("div",{className:"relative max-w-4xl mx-auto",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)(c(),{href:"/blog",children:(0,s.jsxs)(n.z,{variant:"outline",className:"border-gray-200 text-gray-700 hover:bg-blue-50",children:[(0,s.jsx)(m,{className:"w-4 h-4 mr-2"}),"Back to Blog"]})})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(l.C,{className:"bg-blue-100 text-blue-800",children:t.category}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-gray-500 text-sm",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(h.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:(e=>{let a=new Date(e);return a.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})(t.date)})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(u.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:t.readTime})]})]})]}),(0,s.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 leading-tight",children:t.title}),(0,s.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:t.excerpt}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(g.Z,{className:"w-5 h-5 text-gray-400"}),(0,s.jsx)("span",{className:"text-gray-600 font-medium",children:t.author})]}),(0,s.jsxs)(n.z,{onClick:()=>{navigator.share&&t?navigator.share({title:t.title,text:t.excerpt,url:window.location.href}):(navigator.clipboard.writeText(window.location.href),alert("Link copied to clipboard!"))},variant:"outline",className:"border-gray-200 text-gray-700 hover:bg-blue-50",children:[(0,s.jsx)(p,{className:"w-4 h-4 mr-2"}),"Share"]})]})]})]})]}),(0,s.jsx)("section",{className:"px-4 pb-16",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"relative h-64 md:h-96 rounded-3xl overflow-hidden shadow-2xl",children:[(0,s.jsx)("img",{src:t.image,alt:t.title,className:"w-full h-full object-cover"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]})})}),(0,s.jsx)("section",{className:"px-4 pb-16",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl p-8 md:p-12",children:[(0,s.jsx)("div",{className:"prose prose-lg max-w-none",children:(0,s.jsx)("div",{className:"text-gray-700 leading-relaxed space-y-6",children:t.content.split("\n").map((e,a)=>e.trim()&&(0,s.jsx)("p",{className:"text-lg leading-relaxed",children:e},a))})}),(0,s.jsxs)("div",{className:"mt-12 pt-8 border-t border-gray-200",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Tags"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:t.tags.map((e,a)=>(0,s.jsx)(l.C,{variant:"secondary",className:"bg-gray-100 text-gray-600",children:e},a))})]})]})})}),(0,s.jsxs)("section",{className:"py-20 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,s.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white leading-tight",children:["Have Questions About",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent",children:"Your Pet's Health?"})]}),(0,s.jsx)("p",{className:"text-xl text-blue-100 leading-relaxed",children:"Contact Nolari for personalized advice and professional veterinary care"}),(0,s.jsx)(n.z,{onClick:()=>{let e='Hi Nolari! I just read your article "'.concat(t.title,"\" and have some questions about my pet's health. Could you please help me?"),a="https://wa.me/254718376311?text=".concat(encodeURIComponent(e));window.open(a,"_blank")},className:"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 text-lg font-semibold",children:(0,s.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,s.jsx)(b.Z,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Ask Nolari"})]})})]})})]}),(0,s.jsx)(d.Z,{})]}):(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[(0,s.jsx)(o.Z,{}),(0,s.jsx)("div",{className:"pt-20 pb-16 px-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Article Not Found"}),(0,s.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"The article you're looking for doesn't exist."}),(0,s.jsx)(c(),{href:"/blog",children:(0,s.jsxs)(n.z,{className:"bg-gradient-to-r from-blue-600 to-pink-600 text-white",children:[(0,s.jsx)(m,{className:"w-4 h-4 mr-2"}),"Back to Blog"]})})]})}),(0,s.jsx)(d.Z,{})]})}},708:function(e,a,t){"use strict";t.d(a,{C:function(){return Badge}});var s=t(7437);t(2265);var r=t(6061),l=t(1628);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Badge(e){let{className:a,variant:t,...r}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:t}),a),...r})}}},function(e){e.O(0,[442,169,581,747,971,472,744],function(){return e(e.s=1456)}),_N_E=e.O()}]);