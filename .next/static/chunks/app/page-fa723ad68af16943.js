(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{8291:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},8203:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},2695:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("Scissors",[["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M8.12 8.12 12 12",key:"1alkpv"}],["path",{d:"M20 4 8.12 15.88",key:"xgtan2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M14.8 14.8 20 20",key:"ptml3r"}]])},9036:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},7607:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("Stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]])},7594:function(e,t,a){Promise.resolve().then(a.bind(a,6662))},6662:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return HomePage}});var s=a(7437),r=a(2265),i=a(1396),l=a.n(i),o=a(3611),n=a(9598),c=a(9036),d=a(7607),h=a(2695),p=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,p.Z)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]]),x=(0,p.Z)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]),u=(0,p.Z)("Siren",[["path",{d:"M7 18v-6a5 5 0 1 1 10 0v6",key:"pcx96s"}],["path",{d:"M5 21a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2z",key:"1b4s83"}],["path",{d:"M21 12h1",key:"jtio3y"}],["path",{d:"M18.5 4.5 18 5",key:"g5sp9y"}],["path",{d:"M2 12h1",key:"1uaihz"}],["path",{d:"M12 2v1",key:"11qlp1"}],["path",{d:"m4.929 4.929.707.707",key:"1i51kw"}],["path",{d:"M12 12v6",key:"3ahymv"}]]),g=(0,p.Z)("Bug",[["path",{d:"m8 2 1.88 1.88",key:"fmnt4t"}],["path",{d:"M14.12 3.88 16 2",key:"qol33r"}],["path",{d:"M9 7.13v-1a3.003 3.003 0 1 1 6 0v1",key:"d7y7pr"}],["path",{d:"M12 20c-3.3 0-6-2.7-6-6v-3a4 4 0 0 1 4-4h4a4 4 0 0 1 4 4v3c0 3.3-2.7 6-6 6",key:"xs1cw7"}],["path",{d:"M12 20v-9",key:"1qisl0"}],["path",{d:"M6.53 9C4.6 8.8 3 7.1 3 5",key:"32zzws"}],["path",{d:"M6 13H2",key:"82j7cp"}],["path",{d:"M3 21c0-2.1 1.7-3.9 3.8-4",key:"4p0ekp"}],["path",{d:"M20.97 5c0 2.1-1.6 3.8-3.5 4",key:"18gb23"}],["path",{d:"M22 13h-4",key:"1jl80f"}],["path",{d:"M17.2 17c2.1.1 3.8 1.9 3.8 4",key:"k3fwyw"}]]),f=(0,p.Z)("Plane",[["path",{d:"M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z",key:"1v9wt8"}]]),v=(0,p.Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var y=a(8203),b=a(2741),w=a(6141),j=a(7810),k=a(8291),N=a(3827),M=a(8266),C=a(5925),Z=a(1628);function HomePage(){let{openAppointmentModal:e}=(0,C.useAppointment)();(0,r.useEffect)(()=>{let e=window.location.hash;if(e){let t=e.substring(1);setTimeout(()=>{(0,Z.BK)(t)},100)}},[]);let t=[{title:"Vaccinations",description:"Essential vaccination programs to protect your pet from common diseases and maintain their immunity.",image:"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?auto=format&fit=crop&w=400&h=300",icon:c.Z},{title:"Health Check-ups",description:"Comprehensive health examinations to ensure your pet's optimal wellbeing and early detection of health issues.",image:"https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=400&h=300",icon:d.Z},{title:"Surgical Procedures",description:"Professional surgical services including spaying, neutering, and other necessary procedures.",image:"https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=400&h=300",icon:h.Z},{title:"Dental Care",description:"Complete dental health services including cleaning, extractions, and oral health maintenance.",image:"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=400&h=300",icon:m},{title:"Diagnostic Testing",description:"Advanced diagnostic services including blood work, X-rays, and laboratory testing.",image:"https://images.unsplash.com/photo-**********-5c350d0d3c56?auto=format&fit=crop&w=400&h=300",icon:x},{title:"Emergency Care",description:"24/7 emergency veterinary services for urgent medical situations and critical care.",image:"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?auto=format&fit=crop&w=400&h=300",icon:u},{title:"Preventative Care",description:"Comprehensive preventive treatments to keep your pets healthy and prevent future health issues.",image:"https://images.unsplash.com/photo-**********-03cce0bbc87b?auto=format&fit=crop&w=400&h=300",icon:c.Z},{title:"Parasite Control",description:"Comprehensive parasite prevention and treatment programs for fleas, ticks, and worms.",image:"https://images.unsplash.com/photo-1583337130417-3346a1be7dee?auto=format&fit=crop&w=400&h=300",icon:g},{title:"Local and International Travel",description:"Health certificates and travel documentation for pets traveling domestically or internationally.",image:"https://images.unsplash.com/photo-1436491865332-7a61a109cc05?auto=format&fit=crop&w=400&h=300",icon:f},{title:"Wash & Grooming",description:"Professional grooming services including bathing, nail trimming, and coat care.",image:"https://images.unsplash.com/photo-**********-8cc77767d783?auto=format&fit=crop&w=400&h=300",icon:m}],a=[{icon:v,title:"Home Visits",description:"No stressful car rides or waiting rooms. We come to you.",gradient:"from-blue-100 to-blue-200"},{icon:y.Z,title:"Flexible Scheduling",description:"Book appointments that fit your busy schedule.",gradient:"from-pink-100 to-pink-200"},{icon:b.Z,title:"24/7 Support",description:"Emergency care available around the clock.",gradient:"from-gray-100 to-gray-200"},{icon:w.Z,title:"Quick Response",description:"Fast response times for urgent situations.",gradient:"from-blue-100 to-pink-100"}];return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[(0,s.jsx)(N.Z,{}),(0,s.jsxs)("section",{className:"relative pt-20 pb-16 px-4 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-400/10 via-pink-400/5 to-gray-100/20"}),(0,s.jsx)("div",{className:"relative max-w-7xl mx-auto",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium",children:[(0,s.jsx)(j.Z,{className:"w-4 h-4"}),"Professional Veterinary Care"]}),(0,s.jsxs)("h1",{className:"text-5xl lg:text-6xl font-bold text-gray-900 leading-tight",children:["Your Pet's Health is Our",(0,s.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-pink-500",children:" Priority"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:"CVETS Veterinary Services provides comprehensive, compassionate care for your beloved pets. Led by Dr Cynthia, we offer mobile veterinary services bringing professional care directly to your home."})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6",children:[(0,s.jsxs)(o.z,{onClick:e,size:"lg",className:"bg-gradient-to-r from-blue-500 to-pink-500 hover:from-blue-600 hover:to-pink-600 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:[(0,s.jsx)("span",{children:"Book Appointment"}),(0,s.jsx)(k.Z,{className:"w-5 h-5 ml-2"})]}),(0,s.jsx)(o.z,{asChild:!0,variant:"outline",size:"lg",className:"border-2 border-gray-200 bg-white/70 backdrop-blur-sm text-gray-700 hover:bg-gray-50 hover:border-gray-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300",children:(0,s.jsx)(l(),{href:"/services",children:"View Services"})})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400 to-pink-400 rounded-3xl transform rotate-3 opacity-20"}),(0,s.jsxs)("div",{className:"relative bg-white rounded-3xl shadow-2xl overflow-hidden",children:[(0,s.jsx)("img",{src:"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?auto=format&fit=crop&w=600&h=400",alt:"Veterinarian examining a pet",className:"w-full h-96 object-cover"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]})]})]})})]}),(0,s.jsxs)("section",{id:"features",className:"py-32 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-pink-50/30"}),(0,s.jsxs)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-20 space-y-6",children:[(0,s.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-pink-100 rounded-2xl px-8 py-4",children:[(0,s.jsx)(c.Z,{className:"w-6 h-6 text-blue-600"}),(0,s.jsx)("span",{className:"text-lg font-semibold text-gray-700",children:"Why Choose Us"})]}),(0,s.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 bg-clip-text text-transparent leading-tight",children:["Why Choose Mobile",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-pink-600 bg-clip-text text-transparent",children:"Veterinary Care?"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Experience the convenience and comfort of professional veterinary services delivered directly to your home with unprecedented care and attention."})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:a.map((e,t)=>(0,s.jsx)(n.Zb,{className:"group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl",children:(0,s.jsxs)(n.aY,{className:"p-8 text-center space-y-6",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-gradient-to-r ".concat(e.gradient," rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300"),children:(0,s.jsx)(e.icon,{className:"w-10 h-10 text-slate-700"})}),(0,s.jsx)("h3",{className:"text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300",children:e.title}),(0,s.jsx)("p",{className:"text-slate-600 leading-relaxed",children:e.description})]})},t))})]})]}),(0,s.jsxs)("section",{id:"services",className:"py-32 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-100/50 via-blue-50/50 to-pink-50/50"}),(0,s.jsxs)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-20 space-y-6",children:[(0,s.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-gradient-to-r from-pink-100 to-blue-100 rounded-2xl px-8 py-4",children:[(0,s.jsx)(j.Z,{className:"w-6 h-6 text-pink-600"}),(0,s.jsx)("span",{className:"text-lg font-semibold text-gray-700",children:"Our Services"})]}),(0,s.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-pink-900 to-gray-900 bg-clip-text text-transparent leading-tight",children:["Comprehensive Care",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"bg-gradient-to-r from-pink-600 to-blue-600 bg-clip-text text-transparent",children:"Tailored to Your Pet"})]}),(0,s.jsx)("p",{className:"text-xl text-gray-600 leading-relaxed",children:"Professional veterinary services designed with your pet's comfort and health in mind"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8",children:t.map((e,t)=>(0,s.jsxs)(n.Zb,{className:"group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl",children:[(0,s.jsxs)("div",{className:"relative h-48 overflow-hidden rounded-t-3xl",children:[(0,s.jsx)("img",{src:e.image,alt:e.title,className:"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"}),(0,s.jsx)("div",{className:"absolute top-4 right-4 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg",children:(0,s.jsx)(e.icon,{className:"w-6 h-6 text-blue-600"})})]}),(0,s.jsxs)(n.aY,{className:"p-8 space-y-4",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300",children:e.title}),(0,s.jsx)("p",{className:"text-slate-600 leading-relaxed",children:e.description})]})]},t))}),(0,s.jsx)("div",{className:"text-center mt-16",children:(0,s.jsx)(o.z,{asChild:!0,size:"lg",className:"bg-gradient-to-r from-pink-600 to-blue-700 hover:from-pink-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:(0,s.jsxs)(l(),{href:"/services",className:"flex items-center space-x-2",children:[(0,s.jsx)("span",{children:"View All Services"}),(0,s.jsx)(k.Z,{className:"w-5 h-5"})]})})})]})]}),(0,s.jsxs)("section",{className:"py-20 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 via-pink-600 to-blue-700"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,s.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white leading-tight",children:["Ready to Schedule Your Pet's",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent",children:"Appointment?"})]}),(0,s.jsx)("p",{className:"text-xl text-blue-100 leading-relaxed",children:"Book now for convenient, stress-free veterinary care that puts your pet's comfort first"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[(0,s.jsxs)(o.z,{onClick:e,size:"lg",className:"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:[(0,s.jsx)("span",{children:"Book Online"}),(0,s.jsx)(y.Z,{className:"w-5 h-5 ml-2"})]}),(0,s.jsx)(o.z,{asChild:!0,size:"lg",variant:"outline",className:"text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:(0,s.jsxs)("a",{href:"tel:0718376311",className:"flex items-center space-x-2",children:[(0,s.jsx)(b.Z,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Call 0718376311"})]})})]})]})})]}),(0,s.jsx)(M.Z,{})]})}},9598:function(e,t,a){"use strict";a.d(t,{Ol:function(){return o},Zb:function(){return l},aY:function(){return d},ll:function(){return n}});var s=a(7437),r=a(2265),i=a(1628);let l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});l.displayName="Card";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...r})});o.displayName="CardHeader";let n=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});n.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",a),...r})});d.displayName="CardContent";let h=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",a),...r})});h.displayName="CardFooter"}},function(e){e.O(0,[442,169,581,747,971,472,744],function(){return e(e.s=7594)}),_N_E=e.O()}]);