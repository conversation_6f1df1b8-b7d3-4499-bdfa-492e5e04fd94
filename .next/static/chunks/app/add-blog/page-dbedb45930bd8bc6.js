(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[100],{3417:function(e,t,s){Promise.resolve().then(s.bind(s,2973))},2973:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return AddBlogPage}});var n=s(7437),a=s(3827),l=s(8266);function AddBlogPage(){return(0,n.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50",children:[(0,n.jsx)(a.Z,{}),(0,n.jsx)("div",{className:"pt-20 pb-16 px-4",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,n.jsx)("h1",{className:"text-4xl font-bold text-slate-900 mb-4",children:"Add Blog Post"}),(0,n.jsx)("p",{className:"text-xl text-slate-600",children:"Create a new blog post"})]})}),(0,n.jsx)(l.Z,{})]})}}},function(e){e.O(0,[442,169,581,747,971,472,744],function(){return e(e.s=3417)}),_N_E=e.O()}]);