(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[166],{1454:function(e,t,n){Promise.resolve().then(n.bind(n,7356))},7356:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return ManageProductsPage}});var s=n(7437),a=n(3827),r=n(8266);function ManageProductsPage(){return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-cyan-50",children:[(0,s.jsx)(a.Z,{}),(0,s.jsx)("div",{className:"pt-20 pb-16 px-4",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-slate-900 mb-4",children:"Manage Products"}),(0,s.jsx)("p",{className:"text-xl text-slate-600",children:"Manage your veterinary products"})]})}),(0,s.jsx)(r.Z,{})]})}}},function(e){e.O(0,[442,169,581,747,971,472,744],function(){return e(e.s=1454)}),_N_E=e.O()}]);