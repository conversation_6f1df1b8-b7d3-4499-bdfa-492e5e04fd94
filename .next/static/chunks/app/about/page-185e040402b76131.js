(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[301],{8203:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9036:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},7607:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s.Z)("Stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]])},1595:function(e,t,a){Promise.resolve().then(a.bind(a,8989))},8989:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return AboutPage}});var s=a(7437),r=a(2265),l=a(3611),i=a(9598),n=a(708),o=a(3827),c=a(8266),d=a(5925),x=a(1628),m=a(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,m.Z)("GraduationCap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),u=(0,m.Z)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var p=a(7810),f=a(9036),v=a(7607),g=a(8203),b=a(2741);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,m.Z)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);function AboutPage(){let{openAppointmentModal:e}=(0,d.useAppointment)();(0,r.useEffect)(()=>{let e=window.location.hash;if(e){let t=e.substring(1);setTimeout(()=>{(0,x.BK)(t)},100)}},[]);let t=[{title:"Doctor of Veterinary Medicine",institution:"University of Veterinary Sciences",year:"2012",icon:h,color:"from-blue-100 to-cyan-100"},{title:"Mobile Veterinary Certification",institution:"Kenya Veterinary Association",year:"2014",icon:u,color:"from-green-100 to-emerald-100"},{title:"Emergency Care Specialist",institution:"Veterinary Emergency Board",year:"2016",icon:p.Z,color:"from-red-100 to-rose-100"}],a=[{title:"500+ Happy Pets Treated",description:"Successfully provided care to over 500 pets in the community",icon:p.Z,color:"text-red-600"},{title:"10+ Years Experience",description:"Over a decade of dedicated veterinary practice",icon:u,color:"text-blue-600"},{title:"24/7 Emergency Care",description:"Round-the-clock availability for emergency situations",icon:f.Z,color:"text-green-600"},{title:"Mobile Service Pioneer",description:"Leading mobile veterinary services in the region",icon:v.Z,color:"text-purple-600"}];return(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-gray-50",children:[(0,s.jsx)(o.Z,{}),(0,s.jsxs)("section",{className:"relative pt-20 pb-16 px-4 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-600/10 via-cyan-500/5 to-slate-100/20"}),(0,s.jsx)("div",{className:"relative max-w-7xl mx-auto",children:(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-12 items-center",children:[(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"inline-flex items-center gap-2 bg-blue-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium",children:[(0,s.jsx)(p.Z,{className:"w-4 h-4"}),"Meet Our Veterinarian"]}),(0,s.jsx)("h1",{className:"text-5xl lg:text-6xl font-bold text-slate-900 leading-tight",children:(0,s.jsx)("span",{className:"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600",children:"Dr Cynthia"})}),(0,s.jsx)("p",{className:"text-xl text-slate-600 leading-relaxed",children:"A passionate veterinarian dedicated to providing exceptional mobile veterinary care. With over 10 years of experience, Dr Cynthia brings professional, compassionate care directly to your home."})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6",children:[(0,s.jsxs)(l.z,{onClick:e,size:"lg",className:"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:[(0,s.jsx)("span",{children:"Book Appointment"}),(0,s.jsx)(g.Z,{className:"w-5 h-5 ml-2"})]}),(0,s.jsx)(l.z,{asChild:!0,variant:"outline",size:"lg",className:"border-2 border-slate-200 bg-white/70 backdrop-blur-sm text-slate-700 hover:bg-slate-50 hover:border-slate-300 px-8 py-4 rounded-2xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300",children:(0,s.jsxs)("a",{href:"tel:0718376311",className:"flex items-center space-x-2",children:[(0,s.jsx)(b.Z,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Call Now"})]})})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-3xl transform rotate-3 opacity-20"}),(0,s.jsxs)("div",{className:"relative bg-white rounded-3xl shadow-2xl overflow-hidden",children:[(0,s.jsx)("img",{src:"https://images.unsplash.com/photo-1582562124811-c09040d0a901?auto=format&fit=crop&w=600&h=400",alt:"Dr Cynthia",className:"w-full h-96 object-cover"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]})]})]})})]}),(0,s.jsxs)("section",{className:"py-32 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-50/30 via-white/30 to-green-50/30"}),(0,s.jsxs)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"text-center mb-20 space-y-6",children:[(0,s.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-gradient-to-r from-blue-100 to-green-100 rounded-2xl px-8 py-4",children:[(0,s.jsx)(h,{className:"w-6 h-6 text-blue-600"}),(0,s.jsx)("span",{className:"text-lg font-semibold text-slate-700",children:"Qualifications"})]}),(0,s.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent leading-tight",children:["Professional",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent",children:"Credentials"})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:t.map((e,t)=>(0,s.jsx)(i.Zb,{className:"group relative overflow-hidden bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 ease-out hover:-translate-y-2 rounded-3xl",children:(0,s.jsxs)(i.aY,{className:"p-8 text-center space-y-6",children:[(0,s.jsx)("div",{className:"w-20 h-20 bg-gradient-to-r ".concat(e.color," rounded-2xl flex items-center justify-center mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300"),children:(0,s.jsx)(e.icon,{className:"w-10 h-10 text-slate-700"})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-slate-800 group-hover:text-blue-600 transition-colors duration-300",children:e.title}),(0,s.jsx)("p",{className:"text-slate-600",children:e.institution}),(0,s.jsx)(n.C,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:e.year})]})]})},t))})]})]}),(0,s.jsxs)("section",{className:"py-20 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50"}),(0,s.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:a.map((e,t)=>(0,s.jsx)(i.Zb,{className:"text-center bg-white/70 backdrop-blur-sm border-0 shadow-lg hover:shadow-xl transition-all duration-300 rounded-3xl hover:-translate-y-1",children:(0,s.jsxs)(i.aY,{className:"p-8 space-y-4",children:[(0,s.jsx)(e.icon,{className:"w-12 h-12 mx-auto ".concat(e.color)}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-lg font-bold text-slate-800",children:e.title}),(0,s.jsx)("p",{className:"text-slate-600 text-sm",children:e.description})]})]})},t))})})]}),(0,s.jsxs)("section",{id:"about-story",className:"py-32 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-slate-100/50 via-blue-50/50 to-green-50/50"}),(0,s.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,s.jsx)("div",{className:"text-center mb-16 space-y-6",children:(0,s.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 bg-clip-text text-transparent",children:["My Journey to",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent",children:"Mobile Veterinary Care"})]})}),(0,s.jsx)(i.Zb,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2",children:[(0,s.jsxs)("div",{className:"relative h-64 lg:h-full",children:[(0,s.jsx)("img",{src:"https://images.unsplash.com/photo-1535268647677-300dbf3d78d1?auto=format&fit=crop&w=600&h=400",alt:"Veterinarian with pets",className:"w-full h-full object-cover"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-green-600/20"})]}),(0,s.jsxs)(i.aY,{className:"p-12 space-y-6",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-slate-800",children:"A Passion Born from Love"}),(0,s.jsxs)("div",{className:"space-y-4 text-slate-600 leading-relaxed",children:[(0,s.jsx)("p",{children:"My journey into veterinary medicine began with a simple love for animals and a desire to make a difference in their lives. After completing my Doctor of Veterinary Medicine degree in 2012, I quickly realized that traditional clinic visits could be stressful for both pets and their families."}),(0,s.jsx)("p",{children:"This realization led me to pursue mobile veterinary services, bringing professional care directly to the comfort of your home. Over the past decade, I've had the privilege of caring for over 500 beloved pets, building lasting relationships with families throughout our community."}),(0,s.jsx)("p",{children:"My commitment to excellence drives me to stay current with the latest veterinary practices and technologies, ensuring that every pet receives the highest standard of care in the familiar environment of their own home."})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 pt-4",children:[(0,s.jsx)("div",{className:"flex space-x-1",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,s.jsx)(y,{className:"w-5 h-5 fill-yellow-400 text-yellow-400"},t))}),(0,s.jsx)("span",{className:"text-slate-600 font-medium",children:"Trusted by 500+ pet families"})]})]})]})})]})})]}),(0,s.jsxs)("section",{className:"py-20 relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,s.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto space-y-8",children:[(0,s.jsxs)("h2",{className:"text-4xl md:text-5xl font-bold text-white leading-tight",children:["Ready to Experience",(0,s.jsx)("br",{}),(0,s.jsx)("span",{className:"bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent",children:"Compassionate Care?"})]}),(0,s.jsx)("p",{className:"text-xl text-blue-100 leading-relaxed",children:"Schedule your pet's appointment today and discover the convenience of professional veterinary care at home"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[(0,s.jsxs)(l.z,{onClick:e,size:"lg",className:"bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:[(0,s.jsx)("span",{children:"Book Appointment"}),(0,s.jsx)(g.Z,{className:"w-5 h-5 ml-2"})]}),(0,s.jsx)(l.z,{asChild:!0,size:"lg",variant:"outline",className:"text-white border-white/50 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:(0,s.jsxs)("a",{href:"tel:0718376311",className:"flex items-center space-x-2",children:[(0,s.jsx)(b.Z,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Call 0718376311"})]})})]})]})})]}),(0,s.jsx)(c.Z,{})]})}},708:function(e,t,a){"use strict";a.d(t,{C:function(){return Badge}});var s=a(7437);a(2265);var r=a(6061),l=a(1628);let i=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Badge(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{className:(0,l.cn)(i({variant:a}),t),...r})}},9598:function(e,t,a){"use strict";a.d(t,{Ol:function(){return n},Zb:function(){return i},aY:function(){return d},ll:function(){return o}});var s=a(7437),r=a(2265),l=a(1628);let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});i.displayName="Card";let n=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...r})});n.displayName="CardHeader";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});o.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});c.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",a),...r})});d.displayName="CardContent";let x=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",a),...r})});x.displayName="CardFooter"}},function(e){e.O(0,[442,169,581,747,971,472,744],function(){return e(e.s=1595)}),_N_E=e.O()}]);