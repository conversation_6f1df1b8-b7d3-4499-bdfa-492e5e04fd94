(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[179],{4878:function(r,n){"use strict";function getDeploymentIdQueryOrEmptyString(){return""}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"getDeploymentIdQueryOrEmptyString",{enumerable:!0,get:function(){return getDeploymentIdQueryOrEmptyString}})},37:function(){"trimStart"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),"trimEnd"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),"description"in Symbol.prototype||Object.defineProperty(Symbol.prototype,"description",{configurable:!0,get:function(){var r=/\((.*)\)/.exec(this.toString());return r?r[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(r,n){return n=this.concat.apply([],this),r>1&&n.some(Array.isArray)?n.flat(r-1):n},Array.prototype.flatMap=function(r,n){return this.map(r,n).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(r){if("function"!=typeof r)return this.then(r,r);var n=this.constructor||Promise;return this.then(function(o){return n.resolve(r()).then(function(){return o})},function(o){return n.resolve(r()).then(function(){throw o})})}),Object.fromEntries||(Object.fromEntries=function(r){return Array.from(r).reduce(function(r,n){return r[n[0]]=n[1],r},{})}),Array.prototype.at||(Array.prototype.at=function(r){var n=Math.trunc(r)||0;if(n<0&&(n+=this.length),!(n<0||n>=this.length))return this[n]})},5864:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"addBasePath",{enumerable:!0,get:function(){return addBasePath}});let l=o(3776),u=o(2955);function addBasePath(r,n){return(0,u.normalizePathTrailingSlash)((0,l.addPathPrefix)(r,""))}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},2048:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"addLocale",{enumerable:!0,get:function(){return addLocale}}),o(2955);let addLocale=function(r){for(var n=arguments.length,o=Array(n>1?n-1:0),l=1;l<n;l++)o[l-1]=arguments[l];return r};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},4287:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"detectDomainLocale",{enumerable:!0,get:function(){return detectDomainLocale}});let detectDomainLocale=function(){for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o]};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},6948:function(r,n){"use strict";let o;Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{addMessageListener:function(){return addMessageListener},sendMessage:function(){return sendMessage},connectHMR:function(){return connectHMR}});let l=[];function addMessageListener(r){l.push(r)}function sendMessage(r){if(o&&o.readyState===o.OPEN)return o.send(r)}let u=0;function connectHMR(r){!function init(){let n;function handleDisconnect(){if(o.onerror=null,o.onclose=null,o.close(),++u>25){window.location.reload();return}clearTimeout(n),n=setTimeout(init,u>5?5e3:1e3)}o&&o.close();let{hostname:s,port:d}=location,f=function(r){let n=location.protocol;try{n=new URL(r).protocol}catch(r){}return"http:"===n?"ws":"wss"}(r.assetPrefix||""),h=r.assetPrefix.replace(/^\/+/,""),g=f+"://"+s+":"+d+(h?"/"+h:"");h.startsWith("http")&&(g=f+"://"+h.split("://")[1]),(o=new window.WebSocket(""+g+r.path)).onopen=function(){u=0,window.console.log("[HMR] connected")},o.onerror=handleDisconnect,o.onclose=handleDisconnect,o.onmessage=function(r){let n=JSON.parse(r.data);for(let r of l)r(n)}}()}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},5213:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"hasBasePath",{enumerable:!0,get:function(){return hasBasePath}});let l=o(6315);function hasBasePath(r){return(0,l.pathHasPrefix)(r,"")}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},4477:function(r,n){"use strict";let o;Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{DOMAttributeNames:function(){return l},isEqualNode:function(){return isEqualNode},default:function(){return initHeadManager}});let l={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function reactElementToDOM(r){let{type:n,props:o}=r,u=document.createElement(n);for(let r in o){if(!o.hasOwnProperty(r)||"children"===r||"dangerouslySetInnerHTML"===r||void 0===o[r])continue;let s=l[r]||r.toLowerCase();"script"===n&&("async"===s||"defer"===s||"noModule"===s)?u[s]=!!o[r]:u.setAttribute(s,o[r])}let{children:s,dangerouslySetInnerHTML:d}=o;return d?u.innerHTML=d.__html||"":s&&(u.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):""),u}function isEqualNode(r,n){if(r instanceof HTMLElement&&n instanceof HTMLElement){let o=n.getAttribute("nonce");if(o&&!r.getAttribute("nonce")){let l=n.cloneNode(!0);return l.setAttribute("nonce",""),l.nonce=o,o===r.nonce&&r.isEqualNode(l)}}return r.isEqualNode(n)}function initHeadManager(){return{mountedInstances:new Set,updateHead:r=>{let n={};r.forEach(r=>{if("link"===r.type&&r.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'+r.props["data-href"]+'"]'))return;r.props.href=r.props["data-href"],r.props["data-href"]=void 0}let o=n[r.type]||[];o.push(r),n[r.type]=o});let l=n.title?n.title[0]:null,u="";if(l){let{children:r}=l.props;u="string"==typeof r?r:Array.isArray(r)?r.join(""):""}u!==document.title&&(document.title=u),["meta","base","link","style","script"].forEach(r=>{o(r,n[r]||[])})}}}o=(r,n)=>{let o=document.getElementsByTagName("head")[0],l=o.querySelector("meta[name=next-head-count]"),u=Number(l.content),s=[];for(let n=0,o=l.previousElementSibling;n<u;n++,o=(null==o?void 0:o.previousElementSibling)||null){var d;(null==o?void 0:null==(d=o.tagName)?void 0:d.toLowerCase())===r&&s.push(o)}let f=n.map(reactElementToDOM).filter(r=>{for(let n=0,o=s.length;n<o;n++){let o=s[n];if(isEqualNode(o,r))return s.splice(n,1),!1}return!0});s.forEach(r=>{var n;return null==(n=r.parentNode)?void 0:n.removeChild(r)}),f.forEach(r=>o.insertBefore(r,l)),l.content=(u-s.length+f.length).toString()},("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},223:function(r,n,o){"use strict";let l,u,s,d,f,h,g,_,y,P,b,E;Object.defineProperty(n,"__esModule",{value:!0});let S=o(1757);Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{version:function(){return J},router:function(){return l},emitter:function(){return Z},initialize:function(){return initialize},hydrate:function(){return hydrate}});let R=o(8754);o(37);let w=R._(o(7294)),O=R._(o(745)),j=o(7768),A=R._(o(3745)),M=o(637),C=o(9987),I=o(8011),x=o(4828),N=o(510),L=o(1306),D=o(5091),k=R._(o(4477)),F=R._(o(7375)),U=R._(o(3129)),H=o(9513),B=o(4751),W=o(676),q=o(4955),z=o(7786),G=o(5213),V=o(1158),X=o(7178),Y=o(8197),K=R._(o(2565)),Q=R._(o(2295)),$=R._(o(6077)),J="13.5.11",Z=(0,A.default)(),looseToArray=r=>[].slice.call(r),ee=!1;let Container=class Container extends w.default.Component{componentDidCatch(r,n){this.props.fn(r,n)}componentDidMount(){this.scrollToHash(),l.isSsr&&(u.isFallback||u.nextExport&&((0,I.isDynamicRoute)(l.pathname)||location.search||ee)||u.props&&u.props.__N_SSG&&(location.search||ee))&&l.replace(l.pathname+"?"+String((0,x.assign)((0,x.urlQueryToSearchParams)(l.query),new URLSearchParams(location.search))),s,{_h:1,shallow:!u.isFallback&&!ee}).catch(r=>{if(!r.cancelled)throw r})}componentDidUpdate(){this.scrollToHash()}scrollToHash(){let{hash:r}=location;if(!(r=r&&r.substring(1)))return;let n=document.getElementById(r);n&&setTimeout(()=>n.scrollIntoView(),0)}render(){return this.props.children}};async function initialize(r){void 0===r&&(r={}),Q.default.onSpanEnd($.default),u=JSON.parse(document.getElementById("__NEXT_DATA__").textContent),window.__NEXT_DATA__=u,E=u.defaultLocale;let n=u.assetPrefix||"";if(self.__next_set_public_path__(""+n+"/_next/"),(0,N.setConfig)({serverRuntimeConfig:{},publicRuntimeConfig:u.runtimeConfig||{}}),s=(0,L.getURL)(),(0,G.hasBasePath)(s)&&(s=(0,z.removeBasePath)(s)),u.scriptLoader){let{initScriptLoader:r}=o(2288);r(u.scriptLoader)}d=new F.default(u.buildId,n);let register=r=>{let[n,o]=r;return d.routeLoader.onEntrypoint(n,o)};return window.__NEXT_P&&window.__NEXT_P.map(r=>setTimeout(()=>register(r),0)),window.__NEXT_P=[],window.__NEXT_P.push=register,(h=(0,k.default)()).getIsSsr=()=>l.isSsr,f=document.getElementById("__next"),{assetPrefix:n}}function renderApp(r,n){return w.default.createElement(r,n)}function AppContainer(r){var n;let{children:o}=r,u=w.default.useMemo(()=>(0,X.adaptForAppRouterInstance)(l),[]);return w.default.createElement(Container,{fn:r=>renderError({App:y,err:r}).catch(r=>console.error("Error rendering page: ",r))},w.default.createElement(V.AppRouterContext.Provider,{value:u},w.default.createElement(Y.SearchParamsContext.Provider,{value:(0,X.adaptForSearchParams)(l)},w.default.createElement(X.PathnameContextProviderAdapter,{router:l,isAutoExport:null!=(n=self.__NEXT_DATA__.autoExport)&&n},w.default.createElement(Y.PathParamsContext.Provider,{value:(0,X.adaptForPathParams)(l)},w.default.createElement(M.RouterContext.Provider,{value:(0,B.makePublicRouterInstance)(l)},w.default.createElement(j.HeadManagerContext.Provider,{value:h},w.default.createElement(q.ImageConfigContext.Provider,{value:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}},o))))))))}let wrapApp=r=>n=>{let o={...n,Component:b,err:u.err,router:l};return w.default.createElement(AppContainer,null,renderApp(r,o))};function renderError(r){let{App:n,err:f}=r;return console.error(f),console.error("A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred"),d.loadPage("/_error").then(l=>{let{page:u,styleSheets:s}=l;return(null==g?void 0:g.Component)===u?Promise.resolve().then(()=>S._(o(4570))).then(l=>Promise.resolve().then(()=>S._(o(8955))).then(o=>(n=o.default,r.App=n,l))).then(r=>({ErrorComponent:r.default,styleSheets:[]})):{ErrorComponent:u,styleSheets:s}}).then(o=>{var d;let{ErrorComponent:h,styleSheets:g}=o,_=wrapApp(n),y={Component:h,AppTree:_,router:l,ctx:{err:f,pathname:u.page,query:u.query,asPath:s,AppTree:_}};return Promise.resolve((null==(d=r.props)?void 0:d.err)?r.props:(0,L.loadGetInitialProps)(n,y)).then(n=>doRender({...r,err:f,Component:h,styleSheets:g,props:n}))})}function Head(r){let{callback:n}=r;return w.default.useLayoutEffect(()=>n(),[n]),null}let et={navigationStart:"navigationStart",beforeRender:"beforeRender",afterRender:"afterRender",afterHydrate:"afterHydrate",routeChange:"routeChange"},er={hydration:"Next.js-hydration",beforeHydration:"Next.js-before-hydration",routeChangeToRender:"Next.js-route-change-to-render",render:"Next.js-render"},en=null,ea=!0;function clearMarks(){[et.beforeRender,et.afterHydrate,et.afterRender,et.routeChange].forEach(r=>performance.clearMarks(r))}function markHydrateComplete(){if(!L.ST)return;performance.mark(et.afterHydrate);let r=performance.getEntriesByName(et.beforeRender,"mark").length;r&&(performance.measure(er.beforeHydration,et.navigationStart,et.beforeRender),performance.measure(er.hydration,et.beforeRender,et.afterHydrate)),P&&performance.getEntriesByName(er.hydration).forEach(P),clearMarks()}function markRenderComplete(){if(!L.ST)return;performance.mark(et.afterRender);let r=performance.getEntriesByName(et.routeChange,"mark");if(!r.length)return;let n=performance.getEntriesByName(et.beforeRender,"mark").length;n&&(performance.measure(er.routeChangeToRender,r[0].name,et.beforeRender),performance.measure(er.render,et.beforeRender,et.afterRender),P&&(performance.getEntriesByName(er.render).forEach(P),performance.getEntriesByName(er.routeChangeToRender).forEach(P))),clearMarks(),[er.routeChangeToRender,er.render].forEach(r=>performance.clearMeasures(r))}function Root(r){let{callbacks:n,children:o}=r;return w.default.useLayoutEffect(()=>n.forEach(r=>r()),[n]),w.default.useEffect(()=>{(0,U.default)(P)},[]),o}function doRender(r){let n,{App:o,Component:u,props:s,err:d}=r,h="initial"in r?void 0:r.styleSheets;u=u||g.Component,s=s||g.props;let y={...s,Component:u,err:d,router:l};g=y;let P=!1,b=new Promise((r,o)=>{_&&_(),n=()=>{_=null,r()},_=()=>{P=!0,_=null;let r=Error("Cancel rendering route");r.cancelled=!0,o(r)}});function onRootCommit(){n()}!function(){if(!h)return;let r=looseToArray(document.querySelectorAll("style[data-n-href]")),n=new Set(r.map(r=>r.getAttribute("data-n-href"))),o=document.querySelector("noscript[data-n-css]"),l=null==o?void 0:o.getAttribute("data-n-css");h.forEach(r=>{let{href:o,text:u}=r;if(!n.has(o)){let r=document.createElement("style");r.setAttribute("data-n-href",o),r.setAttribute("media","x"),l&&r.setAttribute("nonce",l),document.head.appendChild(r),r.appendChild(document.createTextNode(u))}})}();let E=w.default.createElement(w.default.Fragment,null,w.default.createElement(Head,{callback:function(){if(h&&!P){let r=new Set(h.map(r=>r.href)),n=looseToArray(document.querySelectorAll("style[data-n-href]")),o=n.map(r=>r.getAttribute("data-n-href"));for(let l=0;l<o.length;++l)r.has(o[l])?n[l].removeAttribute("media"):n[l].setAttribute("media","x");let l=document.querySelector("noscript[data-n-css]");l&&h.forEach(r=>{let{href:n}=r,o=document.querySelector('style[data-n-href="'+n+'"]');o&&(l.parentNode.insertBefore(o,l.nextSibling),l=o)}),looseToArray(document.querySelectorAll("link[data-n-p]")).forEach(r=>{r.parentNode.removeChild(r)})}if(r.scroll){let{x:n,y:o}=r.scroll;(0,C.handleSmoothScroll)(()=>{window.scrollTo(n,o)})}}}),w.default.createElement(AppContainer,null,renderApp(o,y),w.default.createElement(D.Portal,{type:"next-route-announcer"},w.default.createElement(H.RouteAnnouncer,null))));return!function(r,n){L.ST&&performance.mark(et.beforeRender);let o=n(ea?markHydrateComplete:markRenderComplete);if(en){let r=w.default.startTransition;r(()=>{en.render(o)})}else en=O.default.hydrateRoot(r,o,{onRecoverableError:K.default}),ea=!1}(f,r=>w.default.createElement(Root,{callbacks:[r,onRootCommit]},E)),b}async function render(r){if(r.err){await renderError(r);return}try{await doRender(r)}catch(o){let n=(0,W.getProperError)(o);if(n.cancelled)throw n;await renderError({...r,err:n})}}async function hydrate(r){let n=u.err;try{let r=await d.routeLoader.whenEntrypoint("/_app");if("error"in r)throw r.error;let{component:n,exports:o}=r;y=n,o&&o.reportWebVitals&&(P=r=>{let n,{id:l,name:u,startTime:s,value:d,duration:f,entryType:h,entries:g,attribution:_}=r,y=Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12);g&&g.length&&(n=g[0].startTime);let P={id:l||y,name:u,startTime:s||n,value:null==d?f:d,label:"mark"===h||"measure"===h?"custom":"web-vital"};_&&(P.attribution=_),o.reportWebVitals(P)});let l=await d.routeLoader.whenEntrypoint(u.page);if("error"in l)throw l.error;b=l.component}catch(r){n=(0,W.getProperError)(r)}window.__NEXT_PRELOADREADY&&await window.__NEXT_PRELOADREADY(u.dynamicIds),l=(0,B.createRouter)(u.page,u.query,s,{initialProps:u.props,pageLoader:d,App:y,Component:b,wrapApp,err:n,isFallback:!!u.isFallback,subscription:(r,n,o)=>render(Object.assign({},r,{App:n,scroll:o})),locale:u.locale,locales:u.locales,defaultLocale:E,domainLocales:u.domainLocales,isPreview:u.isPreview}),ee=await l._initialMatchesMiddlewarePromise;let o={App:y,initial:!0,Component:b,props:u.props,err:n};(null==r?void 0:r.beforeRender)&&await r.beforeRender(),render(o)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},6653:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),o(1867);let l=o(223);window.next={version:l.version,get router(){return l.router},emitter:l.emitter},(0,l.initialize)({}).then(()=>(0,l.hydrate)()).catch(console.error),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},2955:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return normalizePathTrailingSlash}});let l=o(6082),u=o(891),normalizePathTrailingSlash=r=>{if(!r.startsWith("/"))return r;let{pathname:n,query:o,hash:s}=(0,u.parsePath)(r);return""+(0,l.removeTrailingSlash)(n)+o+s};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},2565:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return onRecoverableError}});let l=o(4600);function onRecoverableError(r){let n="function"==typeof reportError?reportError:r=>{window.console.error(r)};r.digest!==l.NEXT_DYNAMIC_NO_SSR_CODE&&n(r)}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},7375:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return PageLoader}});let l=o(8754),u=o(5864),s=o(9381),d=l._(o(8052)),f=o(2048),h=o(8011),g=o(9823),_=o(6082),y=o(7159);o(729);let PageLoader=class PageLoader{getPageList(){return(0,y.getClientBuildManifest)().then(r=>r.sortedPages)}getMiddleware(){return window.__MIDDLEWARE_MATCHERS=[],window.__MIDDLEWARE_MATCHERS}getDataHref(r){let{asPath:n,href:o,locale:l}=r,{pathname:y,query:P,search:b}=(0,g.parseRelativeUrl)(o),{pathname:E}=(0,g.parseRelativeUrl)(n),S=(0,_.removeTrailingSlash)(y);if("/"!==S[0])throw Error('Route name should start with a "/", got "'+S+'"');return(r=>{let n=(0,d.default)((0,_.removeTrailingSlash)((0,f.addLocale)(r,l)),".json");return(0,u.addBasePath)("/_next/data/"+this.buildId+n+b,!0)})(r.skipInterpolation?E:(0,h.isDynamicRoute)(S)?(0,s.interpolateAs)(y,E,P).result:S)}_isSsg(r){return this.promisedSsgManifest.then(n=>n.has(r))}loadPage(r){return this.routeLoader.loadRoute(r).then(r=>{if("component"in r)return{page:r.component,mod:r.exports,styleSheets:r.styles.map(r=>({href:r.href,text:r.content}))};throw r.error})}prefetch(r){return this.routeLoader.prefetch(r)}constructor(r,n){this.routeLoader=(0,y.createRouteLoader)(n),this.buildId=r,this.assetPrefix=n,this.promisedSsgManifest=new Promise(r=>{window.__SSG_MANIFEST?r(window.__SSG_MANIFEST):window.__SSG_MANIFEST_CB=()=>{r(window.__SSG_MANIFEST)}})}};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},3129:function(r,n,o){"use strict";let l;Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return _default}});let u=["CLS","FCP","FID","INP","LCP","TTFB"];location.href;let s=!1;function onReport(r){l&&l(r)}let _default=r=>{if(l=r,!s)for(let r of(s=!0,u))try{let n;n||(n=o(8018)),n["on"+r](onReport)}catch(n){console.warn("Failed to track "+r+" web-vital",n)}};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},5091:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"Portal",{enumerable:!0,get:function(){return Portal}});let l=o(7294),u=o(3935),Portal=r=>{let{children:n,type:o}=r,[s,d]=(0,l.useState)(null);return(0,l.useEffect)(()=>{let r=document.createElement(o);return document.body.appendChild(r),d(r),()=>{document.body.removeChild(r)}},[o]),s?(0,u.createPortal)(n,s):null};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},7786:function(r,n,o){"use strict";function removeBasePath(r){return r}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"removeBasePath",{enumerable:!0,get:function(){return removeBasePath}}),o(5213),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},3099:function(r,n,o){"use strict";function removeLocale(r,n){return r}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"removeLocale",{enumerable:!0,get:function(){return removeLocale}}),o(891),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},3767:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{requestIdleCallback:function(){return o},cancelIdleCallback:function(){return l}});let o="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(r){let n=Date.now();return self.setTimeout(function(){r({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-n))}})},1)},l="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(r){return clearTimeout(r)};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},4785:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"resolveHref",{enumerable:!0,get:function(){return resolveHref}});let l=o(4828),u=o(3833),s=o(9302),d=o(1306),f=o(2955),h=o(4131),g=o(1232),_=o(9381);function resolveHref(r,n,o){let y;let P="string"==typeof n?n:(0,u.formatWithValidation)(n),b=P.match(/^[a-zA-Z]{1,}:\/\//),E=b?P.slice(b[0].length):P,S=E.split("?");if((S[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+P+"' passed to next/router in page: '"+r.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let n=(0,d.normalizeRepeatedSlashes)(E);P=(b?b[0]:"")+n}if(!(0,h.isLocalURL)(P))return o?[P]:P;try{y=new URL(P.startsWith("#")?r.asPath:r.pathname,"http://n")}catch(r){y=new URL("/","http://n")}try{let r=new URL(P,y);r.pathname=(0,f.normalizePathTrailingSlash)(r.pathname);let n="";if((0,g.isDynamicRoute)(r.pathname)&&r.searchParams&&o){let o=(0,l.searchParamsToUrlQuery)(r.searchParams),{result:d,params:f}=(0,_.interpolateAs)(r.pathname,r.pathname,o);d&&(n=(0,u.formatWithValidation)({pathname:d,hash:r.hash,query:(0,s.omit)(o,f)}))}let d=r.origin===y.origin?r.href.slice(r.origin.length):r.href;return o?[d,n||d]:d}catch(r){return o?[P]:P}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},9513:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{RouteAnnouncer:function(){return RouteAnnouncer},default:function(){return f}});let l=o(8754),u=l._(o(7294)),s=o(4751),d={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",top:0,width:"1px",whiteSpace:"nowrap",wordWrap:"normal"},RouteAnnouncer=()=>{let{asPath:r}=(0,s.useRouter)(),[n,o]=u.default.useState(""),l=u.default.useRef(r);return u.default.useEffect(()=>{if(l.current!==r){if(l.current=r,document.title)o(document.title);else{var n;let l=document.querySelector("h1"),u=null!=(n=null==l?void 0:l.innerText)?n:null==l?void 0:l.textContent;o(u||r)}}},[r]),u.default.createElement("p",{"aria-live":"assertive",id:"__next-route-announcer__",role:"alert",style:d},n)},f=RouteAnnouncer;("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},7159:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{markAssetError:function(){return markAssetError},isAssetError:function(){return isAssetError},getClientBuildManifest:function(){return getClientBuildManifest},createRouteLoader:function(){return createRouteLoader}}),o(8754),o(8052);let l=o(4581),u=o(3767),s=o(4878);function withFuture(r,n,o){let l,u=n.get(r);if(u)return"future"in u?u.future:Promise.resolve(u);let s=new Promise(r=>{l=r});return n.set(r,u={resolve:l,future:s}),o?o().then(r=>(l(r),r)).catch(o=>{throw n.delete(r),o}):s}let d=Symbol("ASSET_LOAD_ERROR");function markAssetError(r){return Object.defineProperty(r,d,{})}function isAssetError(r){return r&&d in r}let f=function(r){try{return r=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||r.relList.supports("prefetch")}catch(r){return!1}}(),getAssetQueryString=()=>(0,s.getDeploymentIdQueryOrEmptyString)();function resolvePromiseWithTimeout(r,n,o){return new Promise((l,s)=>{let d=!1;r.then(r=>{d=!0,l(r)}).catch(s),(0,u.requestIdleCallback)(()=>setTimeout(()=>{d||s(o)},n))})}function getClientBuildManifest(){if(self.__BUILD_MANIFEST)return Promise.resolve(self.__BUILD_MANIFEST);let r=new Promise(r=>{let n=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{r(self.__BUILD_MANIFEST),n&&n()}});return resolvePromiseWithTimeout(r,3800,markAssetError(Error("Failed to load client build manifest")))}function getFilesForRoute(r,n){return getClientBuildManifest().then(o=>{if(!(n in o))throw markAssetError(Error("Failed to lookup route: "+n));let u=o[n].map(n=>r+"/_next/"+encodeURI(n));return{scripts:u.filter(r=>r.endsWith(".js")).map(r=>(0,l.__unsafeCreateTrustedScriptURL)(r)+getAssetQueryString()),css:u.filter(r=>r.endsWith(".css")).map(r=>r+getAssetQueryString())}})}function createRouteLoader(r){let n=new Map,o=new Map,l=new Map,s=new Map;function maybeExecuteScript(r){{var n;let l=o.get(r.toString());return l||(document.querySelector('script[src^="'+r+'"]')?Promise.resolve():(o.set(r.toString(),l=new Promise((o,l)=>{(n=document.createElement("script")).onload=o,n.onerror=()=>l(markAssetError(Error("Failed to load script: "+r))),n.crossOrigin=void 0,n.src=r,document.body.appendChild(n)})),l))}}function fetchStyleSheet(r){let n=l.get(r);return n||l.set(r,n=fetch(r).then(n=>{if(!n.ok)throw Error("Failed to load stylesheet: "+r);return n.text().then(n=>({href:r,content:n}))}).catch(r=>{throw markAssetError(r)})),n}return{whenEntrypoint:r=>withFuture(r,n),onEntrypoint(r,o){(o?Promise.resolve().then(()=>o()).then(r=>({component:r&&r.default||r,exports:r}),r=>({error:r})):Promise.resolve(void 0)).then(o=>{let l=n.get(r);l&&"resolve"in l?o&&(n.set(r,o),l.resolve(o)):(o?n.set(r,o):n.delete(r),s.delete(r))})},loadRoute(o,l){return withFuture(o,s,()=>{let u;return resolvePromiseWithTimeout(getFilesForRoute(r,o).then(r=>{let{scripts:l,css:u}=r;return Promise.all([n.has(o)?[]:Promise.all(l.map(maybeExecuteScript)),Promise.all(u.map(fetchStyleSheet))])}).then(r=>this.whenEntrypoint(o).then(n=>({entrypoint:n,styles:r[1]}))),3800,markAssetError(Error("Route did not complete loading: "+o))).then(r=>{let{entrypoint:n,styles:o}=r,l=Object.assign({styles:o},n);return"error"in n?n:l}).catch(r=>{if(l)throw r;return{error:r}}).finally(()=>null==u?void 0:u())})},prefetch(n){let o;return(o=navigator.connection)&&(o.saveData||/2g/.test(o.effectiveType))?Promise.resolve():getFilesForRoute(r,n).then(r=>Promise.all(f?r.scripts.map(r=>{var n,o,l;return n=r.toString(),o="script",new Promise((r,u)=>{let s='\n      link[rel="prefetch"][href^="'+n+'"],\n      link[rel="preload"][href^="'+n+'"],\n      script[src^="'+n+'"]';if(document.querySelector(s))return r();l=document.createElement("link"),o&&(l.as=o),l.rel="prefetch",l.crossOrigin=void 0,l.onload=r,l.onerror=()=>u(markAssetError(Error("Failed to prefetch: "+n))),l.href=n,document.head.appendChild(l)})}):[])).then(()=>{(0,u.requestIdleCallback)(()=>this.loadRoute(n,!0).catch(()=>{}))}).catch(()=>{})}}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},4751:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{Router:function(){return s.default},default:function(){return P},withRouter:function(){return h.default},useRouter:function(){return useRouter},createRouter:function(){return createRouter},makePublicRouterInstance:function(){return makePublicRouterInstance}});let l=o(8754),u=l._(o(7294)),s=l._(o(7797)),d=o(637),f=l._(o(676)),h=l._(o(7727)),g={router:null,readyCallbacks:[],ready(r){if(this.router)return r();this.readyCallbacks.push(r)}},_=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],y=["push","replace","reload","back","prefetch","beforePopState"];function getRouter(){if(!g.router)throw Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n');return g.router}Object.defineProperty(g,"events",{get:()=>s.default.events}),_.forEach(r=>{Object.defineProperty(g,r,{get(){let n=getRouter();return n[r]}})}),y.forEach(r=>{g[r]=function(){for(var n=arguments.length,o=Array(n),l=0;l<n;l++)o[l]=arguments[l];let u=getRouter();return u[r](...o)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(r=>{g.ready(()=>{s.default.events.on(r,function(){for(var n=arguments.length,o=Array(n),l=0;l<n;l++)o[l]=arguments[l];let u="on"+r.charAt(0).toUpperCase()+r.substring(1);if(g[u])try{g[u](...o)}catch(r){console.error("Error when running the Router event: "+u),console.error((0,f.default)(r)?r.message+"\n"+r.stack:r+"")}})})});let P=g;function useRouter(){let r=u.default.useContext(d.RouterContext);if(!r)throw Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted");return r}function createRouter(){for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];return g.router=new s.default(...n),g.readyCallbacks.forEach(r=>r()),g.readyCallbacks=[],g.router}function makePublicRouterInstance(r){let n={};for(let o of _){if("object"==typeof r[o]){n[o]=Object.assign(Array.isArray(r[o])?[]:{},r[o]);continue}n[o]=r[o]}return n.events=s.default.events,y.forEach(o=>{n[o]=function(){for(var n=arguments.length,l=Array(n),u=0;u<n;u++)l[u]=arguments[u];return r[o](...l)}}),n}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},2288:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{handleClientScriptLoad:function(){return handleClientScriptLoad},initScriptLoader:function(){return initScriptLoader},default:function(){return b}});let l=o(8754),u=o(1757),s=l._(o(3935)),d=u._(o(7294)),f=o(7768),h=o(4477),g=o(3767),_=new Map,y=new Set,P=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],insertStylesheets=r=>{if(s.default.preinit){r.forEach(r=>{s.default.preinit(r,{as:"style"})});return}{let n=document.head;r.forEach(r=>{let o=document.createElement("link");o.type="text/css",o.rel="stylesheet",o.href=r,n.appendChild(o)})}},loadScript=r=>{let{src:n,id:o,onLoad:l=()=>{},onReady:u=null,dangerouslySetInnerHTML:s,children:d="",strategy:f="afterInteractive",onError:g,stylesheets:b}=r,E=o||n;if(E&&y.has(E))return;if(_.has(n)){y.add(E),_.get(n).then(l,g);return}let afterLoad=()=>{u&&u(),y.add(E)},S=document.createElement("script"),R=new Promise((r,n)=>{S.addEventListener("load",function(n){r(),l&&l.call(this,n),afterLoad()}),S.addEventListener("error",function(r){n(r)})}).catch(function(r){g&&g(r)});for(let[o,l]of(s?(S.innerHTML=s.__html||"",afterLoad()):d?(S.textContent="string"==typeof d?d:Array.isArray(d)?d.join(""):"",afterLoad()):n&&(S.src=n,_.set(n,R)),Object.entries(r))){if(void 0===l||P.includes(o))continue;let r=h.DOMAttributeNames[o]||o.toLowerCase();S.setAttribute(r,l)}"worker"===f&&S.setAttribute("type","text/partytown"),S.setAttribute("data-nscript",f),b&&insertStylesheets(b),document.body.appendChild(S)};function handleClientScriptLoad(r){let{strategy:n="afterInteractive"}=r;"lazyOnload"===n?window.addEventListener("load",()=>{(0,g.requestIdleCallback)(()=>loadScript(r))}):loadScript(r)}function initScriptLoader(r){r.forEach(handleClientScriptLoad),function(){let r=[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')];r.forEach(r=>{let n=r.id||r.getAttribute("src");y.add(n)})}()}function Script(r){let{id:n,src:o="",onLoad:l=()=>{},onReady:u=null,strategy:h="afterInteractive",onError:_,stylesheets:P,...b}=r,{updateScripts:E,scripts:S,getIsSsr:R,appDir:w,nonce:O}=(0,d.useContext)(f.HeadManagerContext),j=(0,d.useRef)(!1);(0,d.useEffect)(()=>{let r=n||o;j.current||(u&&r&&y.has(r)&&u(),j.current=!0)},[u,n,o]);let A=(0,d.useRef)(!1);if((0,d.useEffect)(()=>{!A.current&&("afterInteractive"===h?loadScript(r):"lazyOnload"===h&&("complete"===document.readyState?(0,g.requestIdleCallback)(()=>loadScript(r)):window.addEventListener("load",()=>{(0,g.requestIdleCallback)(()=>loadScript(r))})),A.current=!0)},[r,h]),("beforeInteractive"===h||"worker"===h)&&(E?(S[h]=(S[h]||[]).concat([{id:n,src:o,onLoad:l,onReady:u,onError:_,...b}]),E(S)):R&&R()?y.add(n||o):R&&!R()&&loadScript(r)),w){if(P&&P.forEach(r=>{s.default.preinit(r,{as:"style"})}),"beforeInteractive"===h)return o?(s.default.preload(o,b.integrity?{as:"script",integrity:b.integrity}:{as:"script"}),d.default.createElement("script",{nonce:O,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([o])+")"}})):(b.dangerouslySetInnerHTML&&(b.children=b.dangerouslySetInnerHTML.__html,delete b.dangerouslySetInnerHTML),d.default.createElement("script",{nonce:O,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...b}])+")"}}));"afterInteractive"===h&&o&&s.default.preload(o,b.integrity?{as:"script",integrity:b.integrity}:{as:"script"})}return null}Object.defineProperty(Script,"__nextScript",{value:!0});let b=Script;("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},6077:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return reportToSocket}});let l=o(6948);function reportToSocket(r){if("ended"!==r.state.state)throw Error("Expected span to be ended");(0,l.sendMessage)(JSON.stringify({event:"span-end",startTime:r.startTime,endTime:r.state.endTime,spanName:r.name,attributes:r.attributes}))}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},2295:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return s}});let l=o(8754),u=l._(o(3745));let Span=class Span{end(r){if("ended"===this.state.state)throw Error("Span has already ended");this.state={state:"ended",endTime:null!=r?r:Date.now()},this.onSpanEnd(this)}constructor(r,n,o){var l,u;this.name=r,this.attributes=null!=(l=n.attributes)?l:{},this.startTime=null!=(u=n.startTime)?u:Date.now(),this.onSpanEnd=o,this.state={state:"inprogress"}}};let s=new class{startSpan(r,n){return new Span(r,n,this.handleSpanEnd)}onSpanEnd(r){return this._emitter.on("spanend",r),()=>{this._emitter.off("spanend",r)}}constructor(){this._emitter=(0,u.default)(),this.handleSpanEnd=r=>{this._emitter.emit("spanend",r)}}};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},4581:function(r,n){"use strict";let o;function __unsafeCreateTrustedScriptURL(r){var n;return(null==(n=function(){if(void 0===o){var r;o=(null==(r=window.trustedTypes)?void 0:r.createPolicy("nextjs",{createHTML:r=>r,createScript:r=>r,createScriptURL:r=>r}))||null}return o}())?void 0:n.createScriptURL(r))||r}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"__unsafeCreateTrustedScriptURL",{enumerable:!0,get:function(){return __unsafeCreateTrustedScriptURL}}),("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},1867:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),o(4878),self.__next_set_public_path__=r=>{o.p=r},("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},7727:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return withRouter}});let l=o(8754),u=l._(o(7294)),s=o(4751);function withRouter(r){function WithRouterWrapper(n){return u.default.createElement(r,{router:(0,s.useRouter)(),...n})}return WithRouterWrapper.getInitialProps=r.getInitialProps,WithRouterWrapper.origGetInitialProps=r.origGetInitialProps,WithRouterWrapper}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},8955:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return App}});let l=o(8754),u=l._(o(7294)),s=o(1306);async function appGetInitialProps(r){let{Component:n,ctx:o}=r,l=await (0,s.loadGetInitialProps)(n,o);return{pageProps:l}}let App=class App extends u.default.Component{render(){let{Component:r,pageProps:n}=this.props;return u.default.createElement(r,n)}};App.origGetInitialProps=appGetInitialProps,App.getInitialProps=appGetInitialProps,("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},4570:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return Error}});let l=o(8754),u=l._(o(7294)),s=l._(o(46)),d={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function _getInitialProps(r){let{res:n,err:o}=r,l=n&&n.statusCode?n.statusCode:o?o.statusCode:404;return{statusCode:l}}let f={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};let Error=class Error extends u.default.Component{render(){let{statusCode:r,withDarkMode:n=!0}=this.props,o=this.props.title||d[r]||"An unexpected error has occurred";return u.default.createElement("div",{style:f.error},u.default.createElement(s.default,null,u.default.createElement("title",null,r?r+": "+o:"Application error: a client-side exception has occurred")),u.default.createElement("div",{style:f.desc},u.default.createElement("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(n?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),r?u.default.createElement("h1",{className:"next-error-h1",style:f.h1},r):null,u.default.createElement("div",{style:f.wrap},u.default.createElement("h2",{style:f.h2},this.props.title||r?o:u.default.createElement(u.default.Fragment,null,"Application error: a client-side exception has occurred (see the browser console for more information)"),"."))))}};Error.displayName="ErrorPage",Error.getInitialProps=_getInitialProps,Error.origGetInitialProps=_getInitialProps,("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},5938:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"AmpStateContext",{enumerable:!0,get:function(){return s}});let l=o(8754),u=l._(o(7294)),s=u.default.createContext({})},8926:function(r,n){"use strict";function isInAmpMode(r){let{ampFirst:n=!1,hybrid:o=!1,hasQuery:l=!1}=void 0===r?{}:r;return n||o&&l}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isInAmpMode",{enumerable:!0,get:function(){return isInAmpMode}})},1158:function(r,n,o){"use strict";var l,u;Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{CacheStates:function(){return l},AppRouterContext:function(){return f},LayoutRouterContext:function(){return h},GlobalLayoutRouterContext:function(){return g},TemplateContext:function(){return _}});let s=o(8754),d=s._(o(7294));(u=l||(l={})).LAZY_INITIALIZED="LAZYINITIALIZED",u.DATA_FETCH="DATAFETCH",u.READY="READY";let f=d.default.createContext(null),h=d.default.createContext(null),g=d.default.createContext(null),_=d.default.createContext(null)},1403:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"BloomFilter",{enumerable:!0,get:function(){return BloomFilter}});let BloomFilter=class BloomFilter{static from(r,n){void 0===n&&(n=.01);let o=new BloomFilter(r.length,n);for(let n of r)o.add(n);return o}export(){let r={numItems:this.numItems,errorRate:this.errorRate,numBits:this.numBits,numHashes:this.numHashes,bitArray:this.bitArray};return r}import(r){this.numItems=r.numItems,this.errorRate=r.errorRate,this.numBits=r.numBits,this.numHashes=r.numHashes,this.bitArray=r.bitArray}add(r){let n=this.getHashValues(r);n.forEach(r=>{this.bitArray[r]=1})}contains(r){let n=this.getHashValues(r);return n.every(r=>this.bitArray[r])}getHashValues(r){let n=[];for(let o=1;o<=this.numHashes;o++){let l=function(r){let n=0;for(let o=0;o<r.length;o++){let l=r.charCodeAt(o);n=Math.imul(n^l,1540483477),n^=n>>>13,n=Math.imul(n,1540483477)}return n>>>0}(""+r+o)%this.numBits;n.push(l)}return n}constructor(r,n){this.numItems=r,this.errorRate=n,this.numBits=Math.ceil(-(r*Math.log(n))/(Math.log(2)*Math.log(2))),this.numHashes=Math.ceil(this.numBits/r*Math.log(2)),this.bitArray=Array(this.numBits).fill(0)}}},729:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{MODERN_BROWSERSLIST_TARGET:function(){return u.default},COMPILER_NAMES:function(){return s},INTERNAL_HEADERS:function(){return d},COMPILER_INDEXES:function(){return f},PHASE_EXPORT:function(){return h},PHASE_PRODUCTION_BUILD:function(){return g},PHASE_PRODUCTION_SERVER:function(){return _},PHASE_DEVELOPMENT_SERVER:function(){return y},PHASE_TEST:function(){return P},PHASE_INFO:function(){return b},PAGES_MANIFEST:function(){return E},APP_PATHS_MANIFEST:function(){return S},APP_PATH_ROUTES_MANIFEST:function(){return R},BUILD_MANIFEST:function(){return w},APP_BUILD_MANIFEST:function(){return O},FUNCTIONS_CONFIG_MANIFEST:function(){return j},SUBRESOURCE_INTEGRITY_MANIFEST:function(){return A},NEXT_FONT_MANIFEST:function(){return M},EXPORT_MARKER:function(){return C},EXPORT_DETAIL:function(){return I},PRERENDER_MANIFEST:function(){return x},ROUTES_MANIFEST:function(){return N},IMAGES_MANIFEST:function(){return L},SERVER_FILES_MANIFEST:function(){return D},DEV_CLIENT_PAGES_MANIFEST:function(){return k},MIDDLEWARE_MANIFEST:function(){return F},DEV_MIDDLEWARE_MANIFEST:function(){return U},REACT_LOADABLE_MANIFEST:function(){return H},FONT_MANIFEST:function(){return B},SERVER_DIRECTORY:function(){return W},CONFIG_FILES:function(){return q},BUILD_ID_FILE:function(){return z},BLOCKED_PAGES:function(){return G},CLIENT_PUBLIC_FILES_PATH:function(){return V},CLIENT_STATIC_FILES_PATH:function(){return X},STRING_LITERAL_DROP_BUNDLE:function(){return Y},NEXT_BUILTIN_DOCUMENT:function(){return K},BARREL_OPTIMIZATION_PREFIX:function(){return Q},CLIENT_REFERENCE_MANIFEST:function(){return $},SERVER_REFERENCE_MANIFEST:function(){return J},MIDDLEWARE_BUILD_MANIFEST:function(){return Z},MIDDLEWARE_REACT_LOADABLE_MANIFEST:function(){return ee},CLIENT_STATIC_FILES_RUNTIME_MAIN:function(){return et},CLIENT_STATIC_FILES_RUNTIME_MAIN_APP:function(){return er},APP_CLIENT_INTERNALS:function(){return en},CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH:function(){return ea},CLIENT_STATIC_FILES_RUNTIME_AMP:function(){return eo},CLIENT_STATIC_FILES_RUNTIME_WEBPACK:function(){return ei},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS:function(){return el},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL:function(){return eu},EDGE_RUNTIME_WEBPACK:function(){return es},TEMPORARY_REDIRECT_STATUS:function(){return ec},PERMANENT_REDIRECT_STATUS:function(){return ed},STATIC_PROPS_ID:function(){return ef},SERVER_PROPS_ID:function(){return ep},PAGE_SEGMENT_KEY:function(){return eh},GOOGLE_FONT_PROVIDER:function(){return em},OPTIMIZED_FONT_PROVIDERS:function(){return eg},DEFAULT_SERIF_FONT:function(){return e_},DEFAULT_SANS_SERIF_FONT:function(){return ey},STATIC_STATUS_PAGES:function(){return eP},TRACE_OUTPUT_VERSION:function(){return ev},TURBO_TRACE_DEFAULT_MEMORY_LIMIT:function(){return eb},RSC_MODULE_TYPES:function(){return eE},EDGE_UNSUPPORTED_NODE_APIS:function(){return eS},SYSTEM_ENTRYPOINTS:function(){return eR}});let l=o(8754),u=l._(o(1329)),s={client:"client",server:"server",edgeServer:"edge-server"},d=["x-invoke-path","x-invoke-status","x-invoke-error","x-invoke-query","x-middleware-invoke"],f={[s.client]:0,[s.server]:1,[s.edgeServer]:2},h="phase-export",g="phase-production-build",_="phase-production-server",y="phase-development-server",P="phase-test",b="phase-info",E="pages-manifest.json",S="app-paths-manifest.json",R="app-path-routes-manifest.json",w="build-manifest.json",O="app-build-manifest.json",j="functions-config-manifest.json",A="subresource-integrity-manifest",M="next-font-manifest",C="export-marker.json",I="export-detail.json",x="prerender-manifest.json",N="routes-manifest.json",L="images-manifest.json",D="required-server-files.json",k="_devPagesManifest.json",F="middleware-manifest.json",U="_devMiddlewareManifest.json",H="react-loadable-manifest.json",B="font-manifest.json",W="server",q=["next.config.js","next.config.mjs"],z="BUILD_ID",G=["/_document","/_app","/_error"],V="public",X="static",Y="__NEXT_DROP_CLIENT_FILE__",K="__NEXT_BUILTIN_DOCUMENT__",Q="__barrel_optimize__",$="client-reference-manifest",J="server-reference-manifest",Z="middleware-build-manifest",ee="middleware-react-loadable-manifest",et="main",er=""+et+"-app",en="app-pages-internals",ea="react-refresh",eo="amp",ei="webpack",el="polyfills",eu=Symbol(el),es="edge-runtime-webpack",ec=307,ed=308,ef="__N_SSG",ep="__N_SSP",eh="__PAGE__",em="https://fonts.googleapis.com/",eg=[{url:em,preconnect:"https://fonts.gstatic.com"},{url:"https://use.typekit.net",preconnect:"https://use.typekit.net"}],e_={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},ey={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},eP=["/500"],ev=1,eb=6e3,eE={client:"client",server:"server"},eS=["clearImmediate","setImmediate","BroadcastChannel","ByteLengthQueuingStrategy","CompressionStream","CountQueuingStrategy","DecompressionStream","DomException","MessageChannel","MessageEvent","MessagePort","ReadableByteStreamController","ReadableStreamBYOBRequest","ReadableStreamDefaultController","TransformStreamDefaultController","WritableStreamDefaultController"],eR=new Set([et,ea,eo,er]);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},4125:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"escapeStringRegexp",{enumerable:!0,get:function(){return escapeStringRegexp}});let o=/[|\\{}()[\]^$+*?.-]/,l=/[|\\{}()[\]^$+*?.-]/g;function escapeStringRegexp(r){return o.test(r)?r.replace(l,"\\$&"):r}},7768:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"HeadManagerContext",{enumerable:!0,get:function(){return s}});let l=o(8754),u=l._(o(7294)),s=u.default.createContext({})},46:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{defaultHead:function(){return defaultHead},default:function(){return _default}});let l=o(8754),u=o(1757),s=u._(o(7294)),d=l._(o(3632)),f=o(5938),h=o(7768),g=o(8926);function defaultHead(r){void 0===r&&(r=!1);let n=[s.default.createElement("meta",{charSet:"utf-8"})];return r||n.push(s.default.createElement("meta",{name:"viewport",content:"width=device-width"})),n}function onlyReactElement(r,n){return"string"==typeof n||"number"==typeof n?r:n.type===s.default.Fragment?r.concat(s.default.Children.toArray(n.props.children).reduce((r,n)=>"string"==typeof n||"number"==typeof n?r:r.concat(n),[])):r.concat(n)}o(9941);let _=["name","httpEquiv","charSet","itemProp"];function reduceComponents(r,n){let{inAmpMode:o}=n;return r.reduce(onlyReactElement,[]).reverse().concat(defaultHead(o).reverse()).filter(function(){let r=new Set,n=new Set,o=new Set,l={};return u=>{let s=!0,d=!1;if(u.key&&"number"!=typeof u.key&&u.key.indexOf("$")>0){d=!0;let n=u.key.slice(u.key.indexOf("$")+1);r.has(n)?s=!1:r.add(n)}switch(u.type){case"title":case"base":n.has(u.type)?s=!1:n.add(u.type);break;case"meta":for(let r=0,n=_.length;r<n;r++){let n=_[r];if(u.props.hasOwnProperty(n)){if("charSet"===n)o.has(n)?s=!1:o.add(n);else{let r=u.props[n],o=l[n]||new Set;("name"!==n||!d)&&o.has(r)?s=!1:(o.add(r),l[n]=o)}}}}return s}}()).reverse().map((r,n)=>{let l=r.key||n;if(!o&&"link"===r.type&&r.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(n=>r.props.href.startsWith(n))){let n={...r.props||{}};return n["data-href"]=n.href,n.href=void 0,n["data-optimized-fonts"]=!0,s.default.cloneElement(r,n)}return s.default.cloneElement(r,{key:l})})}let _default=function(r){let{children:n}=r,o=(0,s.useContext)(f.AmpStateContext),l=(0,s.useContext)(h.HeadManagerContext);return s.default.createElement(d.default,{reduceComponentsToState:reduceComponents,headManager:l,inAmpMode:(0,g.isInAmpMode)(o)},n)};("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),r.exports=n.default)},8197:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{SearchParamsContext:function(){return u},PathnameContext:function(){return s},PathParamsContext:function(){return d}});let l=o(7294),u=(0,l.createContext)(null),s=(0,l.createContext)(null),d=(0,l.createContext)(null)},4656:function(r,n){"use strict";function normalizeLocalePath(r,n){let o;let l=r.split("/");return(n||[]).some(n=>!!l[1]&&l[1].toLowerCase()===n.toLowerCase()&&(o=n,l.splice(1,1),r=l.join("/")||"/",!0)),{pathname:r,detectedLocale:o}}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"normalizeLocalePath",{enumerable:!0,get:function(){return normalizeLocalePath}})},4955:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"ImageConfigContext",{enumerable:!0,get:function(){return d}});let l=o(8754),u=l._(o(7294)),s=o(7599),d=u.default.createContext(s.imageConfigDefault)},7599:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{VALID_LOADERS:function(){return o},imageConfigDefault:function(){return l}});let o=["default","imgix","cloudinary","akamai","custom"],l={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},9942:function(r,n){"use strict";function getObjectClassLabel(r){return Object.prototype.toString.call(r)}function isPlainObject(r){if("[object Object]"!==getObjectClassLabel(r))return!1;let n=Object.getPrototypeOf(r);return null===n||n.hasOwnProperty("isPrototypeOf")}Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{getObjectClassLabel:function(){return getObjectClassLabel},isPlainObject:function(){return isPlainObject}})},4600:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"NEXT_DYNAMIC_NO_SSR_CODE",{enumerable:!0,get:function(){return o}});let o="NEXT_DYNAMIC_NO_SSR_CODE"},3745:function(r,n){"use strict";function mitt(){let r=Object.create(null);return{on(n,o){(r[n]||(r[n]=[])).push(o)},off(n,o){r[n]&&r[n].splice(r[n].indexOf(o)>>>0,1)},emit(n){for(var o=arguments.length,l=Array(o>1?o-1:0),u=1;u<o;u++)l[u-1]=arguments[u];(r[n]||[]).slice().map(r=>{r(...l)})}}}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return mitt}})},1329:function(r){"use strict";r.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},8902:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"denormalizePagePath",{enumerable:!0,get:function(){return denormalizePagePath}});let l=o(1232),u=o(7932);function denormalizePagePath(r){let n=(0,u.normalizePathSep)(r);return n.startsWith("/index/")&&!(0,l.isDynamicRoute)(n)?n.slice(6):"/index"!==n?n:"/"}},3029:function(r,n){"use strict";function ensureLeadingSlash(r){return r.startsWith("/")?r:"/"+r}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"ensureLeadingSlash",{enumerable:!0,get:function(){return ensureLeadingSlash}})},7932:function(r,n){"use strict";function normalizePathSep(r){return r.replace(/\\/g,"/")}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"normalizePathSep",{enumerable:!0,get:function(){return normalizePathSep}})},637:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"RouterContext",{enumerable:!0,get:function(){return s}});let l=o(8754),u=l._(o(7294)),s=u.default.createContext(null)},7178:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{adaptForAppRouterInstance:function(){return adaptForAppRouterInstance},adaptForSearchParams:function(){return adaptForSearchParams},adaptForPathParams:function(){return adaptForPathParams},PathnameContextProviderAdapter:function(){return PathnameContextProviderAdapter}});let l=o(1757),u=l._(o(7294)),s=o(8197),d=o(1232),f=o(2391),h=o(8300);function adaptForAppRouterInstance(r){return{back(){r.back()},forward(){r.forward()},refresh(){r.reload()},push(n,o){let{scroll:l}=void 0===o?{}:o;r.push(n,void 0,{scroll:l})},replace(n,o){let{scroll:l}=void 0===o?{}:o;r.replace(n,void 0,{scroll:l})},prefetch(n){r.prefetch(n)}}}function adaptForSearchParams(r){return r.isReady&&r.query?(0,f.asPathToSearchParams)(r.asPath):new URLSearchParams}function adaptForPathParams(r){if(!r.isReady||!r.query)return null;let n={},o=(0,h.getRouteRegex)(r.pathname),l=Object.keys(o.groups);for(let o of l)n[o]=r.query[o];return n}function PathnameContextProviderAdapter(r){let{children:n,router:o,...l}=r,f=(0,u.useRef)(l.isAutoExport),h=(0,u.useMemo)(()=>{let r;let n=f.current;if(n&&(f.current=!1),(0,d.isDynamicRoute)(o.pathname)&&(o.isFallback||n&&!o.isReady))return null;try{r=new URL(o.asPath,"http://f")}catch(r){return"/"}return r.pathname},[o.asPath,o.isFallback,o.isReady,o.pathname]);return u.default.createElement(s.PathnameContext.Provider,{value:h},n)}},7797:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{default:function(){return Router},matchesMiddleware:function(){return matchesMiddleware},createKey:function(){return createKey}});let l=o(8754),u=o(1757),s=o(6082),d=o(7159),f=o(2288),h=u._(o(676)),g=o(8902),_=o(4656),y=l._(o(3745)),P=o(1306),b=o(8011),E=o(9823);o(2431);let S=o(9001),R=o(8300),w=o(3833);o(4287);let O=o(891),j=o(2048),A=o(3099),M=o(7786),C=o(5864),I=o(5213),x=o(4785),N=o(9423),L=o(673),D=o(8979),k=o(1530),F=o(4131),U=o(6116),H=o(9302),B=o(9381),W=o(9987);function buildCancellationError(){return Object.assign(Error("Route Cancelled"),{cancelled:!0})}async function matchesMiddleware(r){let n=await Promise.resolve(r.router.pageLoader.getMiddleware());if(!n)return!1;let{pathname:o}=(0,O.parsePath)(r.asPath),l=(0,I.hasBasePath)(o)?(0,M.removeBasePath)(o):o,u=(0,C.addBasePath)((0,j.addLocale)(l,r.locale));return n.some(r=>new RegExp(r.regexp).test(u))}function stripOrigin(r){let n=(0,P.getLocationOrigin)();return r.startsWith(n)?r.substring(n.length):r}function prepareUrlAs(r,n,o){let[l,u]=(0,x.resolveHref)(r,n,!0),s=(0,P.getLocationOrigin)(),d=l.startsWith(s),f=u&&u.startsWith(s);l=stripOrigin(l),u=u?stripOrigin(u):u;let h=d?l:(0,C.addBasePath)(l),g=o?stripOrigin((0,x.resolveHref)(r,o)):u||l;return{url:h,as:f?g:(0,C.addBasePath)(g)}}function resolveDynamicRoute(r,n){let o=(0,s.removeTrailingSlash)((0,g.denormalizePagePath)(r));return"/404"===o||"/_error"===o?r:(n.includes(o)||n.some(n=>{if((0,b.isDynamicRoute)(n)&&(0,R.getRouteRegex)(n).re.test(o))return r=n,!0}),(0,s.removeTrailingSlash)(r))}async function withMiddlewareEffects(r){let n=await matchesMiddleware(r);if(!n||!r.fetchData)return null;try{let n=await r.fetchData(),o=await function(r,n,o){let l={basePath:o.router.basePath,i18n:{locales:o.router.locales},trailingSlash:!1},u=n.headers.get("x-nextjs-rewrite"),f=u||n.headers.get("x-nextjs-matched-path"),h=n.headers.get("x-matched-path");if(!h||f||h.includes("__next_data_catchall")||h.includes("/_error")||h.includes("/404")||(f=h),f){if(f.startsWith("/")){let n=(0,E.parseRelativeUrl)(f),h=(0,L.getNextPathnameInfo)(n.pathname,{nextConfig:l,parseData:!0}),g=(0,s.removeTrailingSlash)(h.pathname);return Promise.all([o.router.pageLoader.getPageList(),(0,d.getClientBuildManifest)()]).then(s=>{let[d,{__rewrites:f}]=s,y=(0,j.addLocale)(h.pathname,h.locale);if((0,b.isDynamicRoute)(y)||!u&&d.includes((0,_.normalizeLocalePath)((0,M.removeBasePath)(y),o.router.locales).pathname)){let o=(0,L.getNextPathnameInfo)((0,E.parseRelativeUrl)(r).pathname,{nextConfig:l,parseData:!0});y=(0,C.addBasePath)(o.pathname),n.pathname=y}if(!d.includes(g)){let r=resolveDynamicRoute(g,d);r!==g&&(g=r)}let P=d.includes(g)?g:resolveDynamicRoute((0,_.normalizeLocalePath)((0,M.removeBasePath)(n.pathname),o.router.locales).pathname,d);if((0,b.isDynamicRoute)(P)){let r=(0,S.getRouteMatcher)((0,R.getRouteRegex)(P))(y);Object.assign(n.query,r||{})}return{type:"rewrite",parsedAs:n,resolvedHref:P}})}let n=(0,O.parsePath)(r),h=(0,D.formatNextPathnameInfo)({...(0,L.getNextPathnameInfo)(n.pathname,{nextConfig:l,parseData:!0}),defaultLocale:o.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-external",destination:""+h+n.query+n.hash})}let g=n.headers.get("x-nextjs-redirect");if(g){if(g.startsWith("/")){let r=(0,O.parsePath)(g),n=(0,D.formatNextPathnameInfo)({...(0,L.getNextPathnameInfo)(r.pathname,{nextConfig:l,parseData:!0}),defaultLocale:o.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-internal",newAs:""+n+r.query+r.hash,newUrl:""+n+r.query+r.hash})}return Promise.resolve({type:"redirect-external",destination:g})}return Promise.resolve({type:"next"})}(n.dataHref,n.response,r);return{dataHref:n.dataHref,json:n.json,response:n.response,text:n.text,cacheKey:n.cacheKey,effect:o}}catch(r){return null}}let q=Symbol("SSG_DATA_NOT_FOUND");function tryToParseAsJSON(r){try{return JSON.parse(r)}catch(r){return null}}function fetchNextData(r){var n;let{dataHref:o,inflightCache:l,isPrefetch:u,hasMiddleware:s,isServerRender:f,parseJSON:h,persistCache:g,isBackground:_,unstable_skipClientCache:y}=r,{href:P}=new URL(o,window.location.href),getData=r=>(function fetchRetry(r,n,o){return fetch(r,{credentials:"same-origin",method:o.method||"GET",headers:Object.assign({},o.headers,{"x-nextjs-data":"1"})}).then(l=>!l.ok&&n>1&&l.status>=500?fetchRetry(r,n-1,o):l)})(o,f?3:1,{headers:Object.assign({},u?{purpose:"prefetch"}:{},u&&s?{"x-middleware-prefetch":"1"}:{}),method:null!=(n=null==r?void 0:r.method)?n:"GET"}).then(n=>n.ok&&(null==r?void 0:r.method)==="HEAD"?{dataHref:o,response:n,text:"",json:{},cacheKey:P}:n.text().then(r=>{if(!n.ok){if(s&&[301,302,307,308].includes(n.status))return{dataHref:o,response:n,text:r,json:{},cacheKey:P};if(404===n.status){var l;if(null==(l=tryToParseAsJSON(r))?void 0:l.notFound)return{dataHref:o,json:{notFound:q},response:n,text:r,cacheKey:P}}let u=Error("Failed to load static props");throw f||(0,d.markAssetError)(u),u}return{dataHref:o,json:h?tryToParseAsJSON(r):null,response:n,text:r,cacheKey:P}})).then(r=>(g&&"no-cache"!==r.response.headers.get("x-middleware-cache")||delete l[P],r)).catch(r=>{throw y||delete l[P],("Failed to fetch"===r.message||"NetworkError when attempting to fetch resource."===r.message||"Load failed"===r.message)&&(0,d.markAssetError)(r),r});return y&&g?getData({}).then(r=>(l[P]=Promise.resolve(r),r)):void 0!==l[P]?l[P]:l[P]=getData(_?{method:"HEAD"}:{})}function createKey(){return Math.random().toString(36).slice(2,10)}function handleHardNavigation(r){let{url:n,router:o}=r;if(n===(0,C.addBasePath)((0,j.addLocale)(o.asPath,o.locale)))throw Error("Invariant: attempted to hard navigate to the same URL "+n+" "+location.href);window.location.href=n}let getCancelledHandler=r=>{let{route:n,router:o}=r,l=!1,u=o.clc=()=>{l=!0};return()=>{if(l){let r=Error('Abort fetching component for route: "'+n+'"');throw r.cancelled=!0,r}u===o.clc&&(o.clc=null)}};let Router=class Router{reload(){window.location.reload()}back(){window.history.back()}forward(){window.history.forward()}push(r,n,o){return void 0===o&&(o={}),{url:r,as:n}=prepareUrlAs(this,r,n),this.change("pushState",r,n,o)}replace(r,n,o){return void 0===o&&(o={}),{url:r,as:n}=prepareUrlAs(this,r,n),this.change("replaceState",r,n,o)}async _bfl(r,n,o,l){{let h=!1,g=!1;for(let _ of[r,n])if(_){let n=(0,s.removeTrailingSlash)(new URL(_,"http://n").pathname),y=(0,C.addBasePath)((0,j.addLocale)(n,o||this.locale));if(n!==(0,s.removeTrailingSlash)(new URL(this.asPath,"http://n").pathname)){var u,d,f;for(let r of(h=h||!!(null==(u=this._bfl_s)?void 0:u.contains(n))||!!(null==(d=this._bfl_s)?void 0:d.contains(y)),[n,y])){let n=r.split("/");for(let r=0;!g&&r<n.length+1;r++){let o=n.slice(0,r).join("/");if(o&&(null==(f=this._bfl_d)?void 0:f.contains(o))){g=!0;break}}}if(h||g){if(l)return!0;return handleHardNavigation({url:(0,C.addBasePath)((0,j.addLocale)(r,o||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}}}}return!1}async change(r,n,o,l,u){var g,_,y,x,N,L,D,U,W;let z,G;if(!(0,F.isLocalURL)(n))return handleHardNavigation({url:n,router:this}),!1;let V=1===l._h;V||l.shallow||await this._bfl(o,void 0,l.locale);let X=V||l._shouldResolveHref||(0,O.parsePath)(n).pathname===(0,O.parsePath)(o).pathname,Y={...this.state},K=!0!==this.isReady;this.isReady=!0;let Q=this.isSsr;if(V||(this.isSsr=!1),V&&this.clc)return!1;let $=Y.locale;P.ST&&performance.mark("routeChange");let{shallow:J=!1,scroll:Z=!0}=l,ee={shallow:J};this._inFlightRoute&&this.clc&&(Q||Router.events.emit("routeChangeError",buildCancellationError(),this._inFlightRoute,ee),this.clc(),this.clc=null),o=(0,C.addBasePath)((0,j.addLocale)((0,I.hasBasePath)(o)?(0,M.removeBasePath)(o):o,l.locale,this.defaultLocale));let et=(0,A.removeLocale)((0,I.hasBasePath)(o)?(0,M.removeBasePath)(o):o,Y.locale);this._inFlightRoute=o;let er=$!==Y.locale;if(!V&&this.onlyAHashChange(et)&&!er){Y.asPath=et,Router.events.emit("hashChangeStart",o,ee),this.changeState(r,n,o,{...l,scroll:!1}),Z&&this.scrollToHash(et);try{await this.set(Y,this.components[Y.route],null)}catch(r){throw(0,h.default)(r)&&r.cancelled&&Router.events.emit("routeChangeError",r,et,ee),r}return Router.events.emit("hashChangeComplete",o,ee),!0}let en=(0,E.parseRelativeUrl)(n),{pathname:ea,query:eo}=en;if(null==(g=this.components[ea])?void 0:g.__appRouter)return handleHardNavigation({url:o,router:this}),new Promise(()=>{});try{[z,{__rewrites:G}]=await Promise.all([this.pageLoader.getPageList(),(0,d.getClientBuildManifest)(),this.pageLoader.getMiddleware()])}catch(r){return handleHardNavigation({url:o,router:this}),!1}this.urlIsNew(et)||er||(r="replaceState");let ei=o;ea=ea?(0,s.removeTrailingSlash)((0,M.removeBasePath)(ea)):ea;let el=(0,s.removeTrailingSlash)(ea),eu=o.startsWith("/")&&(0,E.parseRelativeUrl)(o).pathname,es=!!(eu&&el!==eu&&(!(0,b.isDynamicRoute)(el)||!(0,S.getRouteMatcher)((0,R.getRouteRegex)(el))(eu))),ec=!l.shallow&&await matchesMiddleware({asPath:o,locale:Y.locale,router:this});if(V&&ec&&(X=!1),X&&"/_error"!==ea&&(l._shouldResolveHref=!0,en.pathname=resolveDynamicRoute(ea,z),en.pathname===ea||(ea=en.pathname,en.pathname=(0,C.addBasePath)(ea),ec||(n=(0,w.formatWithValidation)(en)))),!(0,F.isLocalURL)(o))return handleHardNavigation({url:o,router:this}),!1;ei=(0,A.removeLocale)((0,M.removeBasePath)(ei),Y.locale),el=(0,s.removeTrailingSlash)(ea);let ed=!1;if((0,b.isDynamicRoute)(el)){let r=(0,E.parseRelativeUrl)(ei),l=r.pathname,u=(0,R.getRouteRegex)(el);ed=(0,S.getRouteMatcher)(u)(l);let s=el===l,d=s?(0,B.interpolateAs)(el,l,eo):{};if(ed&&(!s||d.result))s?o=(0,w.formatWithValidation)(Object.assign({},r,{pathname:d.result,query:(0,H.omit)(eo,d.params)})):Object.assign(eo,ed);else{let r=Object.keys(u.groups).filter(r=>!eo[r]&&!u.groups[r].optional);if(r.length>0&&!ec)throw Error((s?"The provided `href` ("+n+") value is missing query values ("+r.join(", ")+") to be interpolated properly. ":"The provided `as` value ("+l+") is incompatible with the `href` value ("+el+"). ")+"Read more: https://nextjs.org/docs/messages/"+(s?"href-interpolation-failed":"incompatible-href-as"))}}V||Router.events.emit("routeChangeStart",o,ee);let ef="/404"===this.pathname||"/_error"===this.pathname;try{let s=await this.getRouteInfo({route:el,pathname:ea,query:eo,as:o,resolvedAs:ei,routeProps:ee,locale:Y.locale,isPreview:Y.isPreview,hasMiddleware:ec,unstable_skipClientCache:l.unstable_skipClientCache,isQueryUpdating:V&&!this.isFallback,isMiddlewareRewrite:es});if(V||l.shallow||await this._bfl(o,"resolvedAs"in s?s.resolvedAs:void 0,Y.locale),"route"in s&&ec){el=ea=s.route||el,ee.shallow||(eo=Object.assign({},s.query||{},eo));let r=(0,I.hasBasePath)(en.pathname)?(0,M.removeBasePath)(en.pathname):en.pathname;if(ed&&ea!==r&&Object.keys(ed).forEach(r=>{ed&&eo[r]===ed[r]&&delete eo[r]}),(0,b.isDynamicRoute)(ea)){let r=!ee.shallow&&s.resolvedAs?s.resolvedAs:(0,C.addBasePath)((0,j.addLocale)(new URL(o,location.href).pathname,Y.locale),!0),n=r;(0,I.hasBasePath)(n)&&(n=(0,M.removeBasePath)(n));let l=(0,R.getRouteRegex)(ea),u=(0,S.getRouteMatcher)(l)(new URL(n,location.href).pathname);u&&Object.assign(eo,u)}}if("type"in s){if("redirect-internal"===s.type)return this.change(r,s.newUrl,s.newAs,l);return handleHardNavigation({url:s.destination,router:this}),new Promise(()=>{})}let d=s.Component;if(d&&d.unstable_scriptLoader){let r=[].concat(d.unstable_scriptLoader());r.forEach(r=>{(0,f.handleClientScriptLoad)(r.props)})}if((s.__N_SSG||s.__N_SSP)&&s.props){if(s.props.pageProps&&s.props.pageProps.__N_REDIRECT){l.locale=!1;let n=s.props.pageProps.__N_REDIRECT;if(n.startsWith("/")&&!1!==s.props.pageProps.__N_REDIRECT_BASE_PATH){let o=(0,E.parseRelativeUrl)(n);o.pathname=resolveDynamicRoute(o.pathname,z);let{url:u,as:s}=prepareUrlAs(this,n,n);return this.change(r,u,s,l)}return handleHardNavigation({url:n,router:this}),new Promise(()=>{})}if(Y.isPreview=!!s.props.__N_PREVIEW,s.props.notFound===q){let r;try{await this.fetchComponent("/404"),r="/404"}catch(n){r="/_error"}if(s=await this.getRouteInfo({route:r,pathname:r,query:eo,as:o,resolvedAs:ei,routeProps:{shallow:!1},locale:Y.locale,isPreview:Y.isPreview,isNotFound:!0}),"type"in s)throw Error("Unexpected middleware effect on /404")}}V&&"/_error"===this.pathname&&(null==(y=self.__NEXT_DATA__.props)?void 0:null==(_=y.pageProps)?void 0:_.statusCode)===500&&(null==(x=s.props)?void 0:x.pageProps)&&(s.props.pageProps.statusCode=500);let g=l.shallow&&Y.route===(null!=(N=s.route)?N:el),P=null!=(L=l.scroll)?L:!V&&!g,w=null!=u?u:P?{x:0,y:0}:null,O={...Y,route:el,pathname:ea,query:eo,asPath:et,isFallback:!1};if(V&&ef){if(s=await this.getRouteInfo({route:this.pathname,pathname:this.pathname,query:eo,as:o,resolvedAs:ei,routeProps:{shallow:!1},locale:Y.locale,isPreview:Y.isPreview,isQueryUpdating:V&&!this.isFallback}),"type"in s)throw Error("Unexpected middleware effect on "+this.pathname);"/_error"===this.pathname&&(null==(U=self.__NEXT_DATA__.props)?void 0:null==(D=U.pageProps)?void 0:D.statusCode)===500&&(null==(W=s.props)?void 0:W.pageProps)&&(s.props.pageProps.statusCode=500);try{await this.set(O,s,w)}catch(r){throw(0,h.default)(r)&&r.cancelled&&Router.events.emit("routeChangeError",r,et,ee),r}return!0}Router.events.emit("beforeHistoryChange",o,ee),this.changeState(r,n,o,l);let A=V&&!w&&!K&&!er&&(0,k.compareRouterStates)(O,this.state);if(!A){try{await this.set(O,s,w)}catch(r){if(r.cancelled)s.error=s.error||r;else throw r}if(s.error)throw V||Router.events.emit("routeChangeError",s.error,et,ee),s.error;V||Router.events.emit("routeChangeComplete",o,ee),P&&/#.+$/.test(o)&&this.scrollToHash(o)}return!0}catch(r){if((0,h.default)(r)&&r.cancelled)return!1;throw r}}changeState(r,n,o,l){void 0===l&&(l={}),("pushState"!==r||(0,P.getURL)()!==o)&&(this._shallow=l.shallow,window.history[r]({url:n,as:o,options:l,__N:!0,key:this._key="pushState"!==r?this._key:createKey()},"",o))}async handleRouteInfoError(r,n,o,l,u,s){if(console.error(r),r.cancelled)throw r;if((0,d.isAssetError)(r)||s)throw Router.events.emit("routeChangeError",r,l,u),handleHardNavigation({url:l,router:this}),buildCancellationError();try{let l;let{page:u,styleSheets:s}=await this.fetchComponent("/_error"),d={props:l,Component:u,styleSheets:s,err:r,error:r};if(!d.props)try{d.props=await this.getInitialProps(u,{err:r,pathname:n,query:o})}catch(r){console.error("Error in error page `getInitialProps`: ",r),d.props={}}return d}catch(r){return this.handleRouteInfoError((0,h.default)(r)?r:Error(r+""),n,o,l,u,!0)}}async getRouteInfo(r){let{route:n,pathname:o,query:l,as:u,resolvedAs:d,routeProps:f,locale:g,hasMiddleware:y,isPreview:P,unstable_skipClientCache:b,isQueryUpdating:E,isMiddlewareRewrite:S,isNotFound:R}=r,O=n;try{var j,A,C,I;let r=getCancelledHandler({route:O,router:this}),n=this.components[O];if(f.shallow&&n&&this.route===O)return n;y&&(n=void 0);let h=!n||"initial"in n?void 0:n,x={dataHref:this.pageLoader.getDataHref({href:(0,w.formatWithValidation)({pathname:o,query:l}),skipInterpolation:!0,asPath:R?"/404":d,locale:g}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:E?this.sbc:this.sdc,persistCache:!P,isPrefetch:!1,unstable_skipClientCache:b,isBackground:E},L=E&&!S?null:await withMiddlewareEffects({fetchData:()=>fetchNextData(x),asPath:R?"/404":d,locale:g,router:this}).catch(r=>{if(E)return null;throw r});if(L&&("/_error"===o||"/404"===o)&&(L.effect=void 0),E&&(L?L.json=self.__NEXT_DATA__.props:L={json:self.__NEXT_DATA__.props}),r(),(null==L?void 0:null==(j=L.effect)?void 0:j.type)==="redirect-internal"||(null==L?void 0:null==(A=L.effect)?void 0:A.type)==="redirect-external")return L.effect;if((null==L?void 0:null==(C=L.effect)?void 0:C.type)==="rewrite"){let r=(0,s.removeTrailingSlash)(L.effect.resolvedHref),u=await this.pageLoader.getPageList();if((!E||u.includes(r))&&(O=r,o=L.effect.resolvedHref,l={...l,...L.effect.parsedAs.query},d=(0,M.removeBasePath)((0,_.normalizeLocalePath)(L.effect.parsedAs.pathname,this.locales).pathname),n=this.components[O],f.shallow&&n&&this.route===O&&!y))return{...n,route:O}}if((0,N.isAPIRoute)(O))return handleHardNavigation({url:u,router:this}),new Promise(()=>{});let D=h||await this.fetchComponent(O).then(r=>({Component:r.page,styleSheets:r.styleSheets,__N_SSG:r.mod.__N_SSG,__N_SSP:r.mod.__N_SSP})),k=null==L?void 0:null==(I=L.response)?void 0:I.headers.get("x-middleware-skip"),F=D.__N_SSG||D.__N_SSP;k&&(null==L?void 0:L.dataHref)&&delete this.sdc[L.dataHref];let{props:U,cacheKey:H}=await this._getData(async()=>{if(F){if((null==L?void 0:L.json)&&!k)return{cacheKey:L.cacheKey,props:L.json};let r=(null==L?void 0:L.dataHref)?L.dataHref:this.pageLoader.getDataHref({href:(0,w.formatWithValidation)({pathname:o,query:l}),asPath:d,locale:g}),n=await fetchNextData({dataHref:r,isServerRender:this.isSsr,parseJSON:!0,inflightCache:k?{}:this.sdc,persistCache:!P,isPrefetch:!1,unstable_skipClientCache:b});return{cacheKey:n.cacheKey,props:n.json||{}}}return{headers:{},props:await this.getInitialProps(D.Component,{pathname:o,query:l,asPath:u,locale:g,locales:this.locales,defaultLocale:this.defaultLocale})}});return D.__N_SSP&&x.dataHref&&H&&delete this.sdc[H],this.isPreview||!D.__N_SSG||E||fetchNextData(Object.assign({},x,{isBackground:!0,persistCache:!1,inflightCache:this.sbc})).catch(()=>{}),U.pageProps=Object.assign({},U.pageProps),D.props=U,D.route=O,D.query=l,D.resolvedAs=d,this.components[O]=D,D}catch(r){return this.handleRouteInfoError((0,h.getProperError)(r),o,l,u,f)}}set(r,n,o){return this.state=r,this.sub(n,this.components["/_app"].Component,o)}beforePopState(r){this._bps=r}onlyAHashChange(r){if(!this.asPath)return!1;let[n,o]=this.asPath.split("#"),[l,u]=r.split("#");return!!u&&n===l&&o===u||n===l&&o!==u}scrollToHash(r){let[,n=""]=r.split("#");(0,W.handleSmoothScroll)(()=>{if(""===n||"top"===n){window.scrollTo(0,0);return}let r=decodeURIComponent(n),o=document.getElementById(r);if(o){o.scrollIntoView();return}let l=document.getElementsByName(r)[0];l&&l.scrollIntoView()},{onlyHashChange:this.onlyAHashChange(r)})}urlIsNew(r){return this.asPath!==r}async prefetch(r,n,o){if(void 0===n&&(n=r),void 0===o&&(o={}),(0,U.isBot)(window.navigator.userAgent))return;let l=(0,E.parseRelativeUrl)(r),u=l.pathname,{pathname:d,query:f}=l,h=d,g=await this.pageLoader.getPageList(),_=n,y=void 0!==o.locale?o.locale||void 0:this.locale,P=await matchesMiddleware({asPath:n,locale:y,router:this});l.pathname=resolveDynamicRoute(l.pathname,g),(0,b.isDynamicRoute)(l.pathname)&&(d=l.pathname,l.pathname=d,Object.assign(f,(0,S.getRouteMatcher)((0,R.getRouteRegex)(l.pathname))((0,O.parsePath)(n).pathname)||{}),P||(r=(0,w.formatWithValidation)(l)));let j=await withMiddlewareEffects({fetchData:()=>fetchNextData({dataHref:this.pageLoader.getDataHref({href:(0,w.formatWithValidation)({pathname:h,query:f}),skipInterpolation:!0,asPath:_,locale:y}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0}),asPath:n,locale:y,router:this});if((null==j?void 0:j.effect.type)==="rewrite"&&(l.pathname=j.effect.resolvedHref,d=j.effect.resolvedHref,f={...f,...j.effect.parsedAs.query},_=j.effect.parsedAs.pathname,r=(0,w.formatWithValidation)(l)),(null==j?void 0:j.effect.type)==="redirect-external")return;let A=(0,s.removeTrailingSlash)(d);await this._bfl(n,_,o.locale,!0)&&(this.components[u]={__appRouter:!0}),await Promise.all([this.pageLoader._isSsg(A).then(n=>!!n&&fetchNextData({dataHref:(null==j?void 0:j.json)?null==j?void 0:j.dataHref:this.pageLoader.getDataHref({href:r,asPath:_,locale:y}),isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0,unstable_skipClientCache:o.unstable_skipClientCache||o.priority&&!0}).then(()=>!1).catch(()=>!1)),this.pageLoader[o.priority?"loadPage":"prefetch"](A)])}async fetchComponent(r){let n=getCancelledHandler({route:r,router:this});try{let o=await this.pageLoader.loadPage(r);return n(),o}catch(r){throw n(),r}}_getData(r){let n=!1,cancel=()=>{n=!0};return this.clc=cancel,r().then(r=>{if(cancel===this.clc&&(this.clc=null),n){let r=Error("Loading initial props cancelled");throw r.cancelled=!0,r}return r})}_getFlightData(r){return fetchNextData({dataHref:r,isServerRender:!0,parseJSON:!1,inflightCache:this.sdc,persistCache:!1,isPrefetch:!1}).then(r=>{let{text:n}=r;return{data:n}})}getInitialProps(r,n){let{Component:o}=this.components["/_app"],l=this._wrapApp(o);return n.AppTree=l,(0,P.loadGetInitialProps)(o,{AppTree:l,Component:r,router:this,ctx:n})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(r,n,l,{initialProps:u,pageLoader:d,App:f,wrapApp:h,Component:g,err:_,subscription:y,isFallback:S,locale:R,locales:O,defaultLocale:j,domainLocales:A,isPreview:M}){this.sdc={},this.sbc={},this.isFirstPopStateEvent=!0,this._key=createKey(),this.onPopState=r=>{let n;let{isFirstPopStateEvent:o}=this;this.isFirstPopStateEvent=!1;let l=r.state;if(!l){let{pathname:r,query:n}=this;this.changeState("replaceState",(0,w.formatWithValidation)({pathname:(0,C.addBasePath)(r),query:n}),(0,P.getURL)());return}if(l.__NA){window.location.reload();return}if(!l.__N||o&&this.locale===l.options.locale&&l.as===this.asPath)return;let{url:u,as:s,options:d,key:f}=l;this._key=f;let{pathname:h}=(0,E.parseRelativeUrl)(u);(!this.isSsr||s!==(0,C.addBasePath)(this.asPath)||h!==(0,C.addBasePath)(this.pathname))&&(!this._bps||this._bps(l))&&this.change("replaceState",u,s,Object.assign({},d,{shallow:d.shallow&&this._shallow,locale:d.locale||this.defaultLocale,_h:0}),n)};let I=(0,s.removeTrailingSlash)(r);this.components={},"/_error"!==r&&(this.components[I]={Component:g,initial:!0,props:u,err:_,__N_SSG:u&&u.__N_SSG,__N_SSP:u&&u.__N_SSP}),this.components["/_app"]={Component:f,styleSheets:[]};{let{BloomFilter:r}=o(1403),n={numItems:10,errorRate:.01,numBits:96,numHashes:7,bitArray:[0,0,1,1,1,1,1,0,1,1,0,1,1,1,0,0,0,0,1,1,1,0,1,0,1,1,0,1,1,0,1,0,1,1,1,1,0,1,0,0,0,0,1,1,0,1,1,1,0,0,0,0,1,0,0,0,1,0,1,1,0,1,0,1,0,1,1,0,0,1,0,0,0,0,1,0,0,1,1,1,0,1,1,0,1,1,0,1,0,0,0,0,1,1,1,1]},l={numItems:1,errorRate:.01,numBits:10,numHashes:7,bitArray:[1,1,0,1,1,0,0,0,0,1]};(null==n?void 0:n.numHashes)&&(this._bfl_s=new r(n.numItems,n.errorRate),this._bfl_s.import(n)),(null==l?void 0:l.numHashes)&&(this._bfl_d=new r(l.numItems,l.errorRate),this._bfl_d.import(l))}this.events=Router.events,this.pageLoader=d;let x=(0,b.isDynamicRoute)(r)&&self.__NEXT_DATA__.autoExport;if(this.basePath="",this.sub=y,this.clc=null,this._wrapApp=h,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.isExperimentalCompile||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!x&&!self.location.search),this.state={route:I,pathname:r,query:n,asPath:x?r:l,isPreview:!!M,locale:void 0,isFallback:S},this._initialMatchesMiddlewarePromise=Promise.resolve(!1),!l.startsWith("//")){let o={locale:R},u=(0,P.getURL)();this._initialMatchesMiddlewarePromise=matchesMiddleware({router:this,locale:R,asPath:u}).then(s=>(o._shouldResolveHref=l!==r,this.changeState("replaceState",s?u:(0,w.formatWithValidation)({pathname:(0,C.addBasePath)(r),query:n}),u,o),s))}window.addEventListener("popstate",this.onPopState)}};Router.events=(0,y.default)()},142:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"addLocale",{enumerable:!0,get:function(){return addLocale}});let l=o(3776),u=o(6315);function addLocale(r,n,o,s){if(!n||n===o)return r;let d=r.toLowerCase();return!s&&((0,u.pathHasPrefix)(d,"/api")||(0,u.pathHasPrefix)(d,"/"+n.toLowerCase()))?r:(0,l.addPathPrefix)(r,"/"+n)}},3776:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"addPathPrefix",{enumerable:!0,get:function(){return addPathPrefix}});let l=o(891);function addPathPrefix(r,n){if(!r.startsWith("/")||!n)return r;let{pathname:o,query:u,hash:s}=(0,l.parsePath)(r);return""+n+o+u+s}},93:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"addPathSuffix",{enumerable:!0,get:function(){return addPathSuffix}});let l=o(891);function addPathSuffix(r,n){if(!r.startsWith("/")||!n)return r;let{pathname:o,query:u,hash:s}=(0,l.parsePath)(r);return""+o+n+u+s}},9537:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{normalizeAppPath:function(){return normalizeAppPath},normalizeRscPath:function(){return normalizeRscPath}});let l=o(3029),u=o(7494);function normalizeAppPath(r){return(0,l.ensureLeadingSlash)(r.split("/").reduce((r,n,o,l)=>!n||(0,u.isGroupSegment)(n)||"@"===n[0]||("page"===n||"route"===n)&&o===l.length-1?r:r+"/"+n,""))}function normalizeRscPath(r,n){return n?r.replace(/\.rsc($|\?)/,"$1"):r}},2391:function(r,n){"use strict";function asPathToSearchParams(r){return new URL(r,"http://n").searchParams}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"asPathToSearchParams",{enumerable:!0,get:function(){return asPathToSearchParams}})},1530:function(r,n){"use strict";function compareRouterStates(r,n){let o=Object.keys(r);if(o.length!==Object.keys(n).length)return!1;for(let l=o.length;l--;){let u=o[l];if("query"===u){let o=Object.keys(r.query);if(o.length!==Object.keys(n.query).length)return!1;for(let l=o.length;l--;){let u=o[l];if(!n.query.hasOwnProperty(u)||r.query[u]!==n.query[u])return!1}}else if(!n.hasOwnProperty(u)||r[u]!==n[u])return!1}return!0}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"compareRouterStates",{enumerable:!0,get:function(){return compareRouterStates}})},8979:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"formatNextPathnameInfo",{enumerable:!0,get:function(){return formatNextPathnameInfo}});let l=o(6082),u=o(3776),s=o(93),d=o(142);function formatNextPathnameInfo(r){let n=(0,d.addLocale)(r.pathname,r.locale,r.buildId?void 0:r.defaultLocale,r.ignorePrefix);return(r.buildId||!r.trailingSlash)&&(n=(0,l.removeTrailingSlash)(n)),r.buildId&&(n=(0,s.addPathSuffix)((0,u.addPathPrefix)(n,"/_next/data/"+r.buildId),"/"===r.pathname?"index.json":".json")),n=(0,u.addPathPrefix)(n,r.basePath),!r.buildId&&r.trailingSlash?n.endsWith("/")?n:(0,s.addPathSuffix)(n,"/"):(0,l.removeTrailingSlash)(n)}},3833:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{formatUrl:function(){return formatUrl},urlObjectKeys:function(){return d},formatWithValidation:function(){return formatWithValidation}});let l=o(1757),u=l._(o(4828)),s=/https?|ftp|gopher|file/;function formatUrl(r){let{auth:n,hostname:o}=r,l=r.protocol||"",d=r.pathname||"",f=r.hash||"",h=r.query||"",g=!1;n=n?encodeURIComponent(n).replace(/%3A/i,":")+"@":"",r.host?g=n+r.host:o&&(g=n+(~o.indexOf(":")?"["+o+"]":o),r.port&&(g+=":"+r.port)),h&&"object"==typeof h&&(h=String(u.urlQueryToSearchParams(h)));let _=r.search||h&&"?"+h||"";return l&&!l.endsWith(":")&&(l+=":"),r.slashes||(!l||s.test(l))&&!1!==g?(g="//"+(g||""),d&&"/"!==d[0]&&(d="/"+d)):g||(g=""),f&&"#"!==f[0]&&(f="#"+f),_&&"?"!==_[0]&&(_="?"+_),""+l+g+(d=d.replace(/[?#]/g,encodeURIComponent))+(_=_.replace("#","%23"))+f}let d=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function formatWithValidation(r){return formatUrl(r)}},8052:function(r,n){"use strict";function getAssetPathFromRoute(r,n){void 0===n&&(n="");let o="/"===r?"/index":/^\/index(\/|$)/.test(r)?"/index"+r:""+r;return o+n}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return getAssetPathFromRoute}})},673:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"getNextPathnameInfo",{enumerable:!0,get:function(){return getNextPathnameInfo}});let l=o(4656),u=o(4980),s=o(6315);function getNextPathnameInfo(r,n){var o,d;let{basePath:f,i18n:h,trailingSlash:g}=null!=(o=n.nextConfig)?o:{},_={pathname:r,trailingSlash:"/"!==r?r.endsWith("/"):g};f&&(0,s.pathHasPrefix)(_.pathname,f)&&(_.pathname=(0,u.removePathPrefix)(_.pathname,f),_.basePath=f);let y=_.pathname;if(_.pathname.startsWith("/_next/data/")&&_.pathname.endsWith(".json")){let r=_.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),o=r[0];_.buildId=o,y="index"!==r[1]?"/"+r.slice(1).join("/"):"/",!0===n.parseData&&(_.pathname=y)}if(h){let r=n.i18nProvider?n.i18nProvider.analyze(_.pathname):(0,l.normalizeLocalePath)(_.pathname,h.locales);_.locale=r.detectedLocale,_.pathname=null!=(d=r.pathname)?d:_.pathname,!r.detectedLocale&&_.buildId&&(r=n.i18nProvider?n.i18nProvider.analyze(y):(0,l.normalizeLocalePath)(y,h.locales)).detectedLocale&&(_.locale=r.detectedLocale)}return _}},9987:function(r,n){"use strict";function handleSmoothScroll(r,n){if(void 0===n&&(n={}),n.onlyHashChange){r();return}let o=document.documentElement,l=o.style.scrollBehavior;o.style.scrollBehavior="auto",n.dontForceLayout||o.getClientRects(),r(),o.style.scrollBehavior=l}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"handleSmoothScroll",{enumerable:!0,get:function(){return handleSmoothScroll}})},1232:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{getSortedRoutes:function(){return l.getSortedRoutes},isDynamicRoute:function(){return u.isDynamicRoute}});let l=o(2839),u=o(8011)},9381:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"interpolateAs",{enumerable:!0,get:function(){return interpolateAs}});let l=o(9001),u=o(8300);function interpolateAs(r,n,o){let s="",d=(0,u.getRouteRegex)(r),f=d.groups,h=(n!==r?(0,l.getRouteMatcher)(d)(n):"")||o;s=r;let g=Object.keys(f);return g.every(r=>{let n=h[r]||"",{repeat:o,optional:l}=f[r],u="["+(o?"...":"")+r+"]";return l&&(u=(n?"":"/")+"["+u+"]"),o&&!Array.isArray(n)&&(n=[n]),(l||r in h)&&(s=s.replace(u,o?n.map(r=>encodeURIComponent(r)).join("/"):encodeURIComponent(n))||"/")})||(s=""),{params:g,result:s}}},6116:function(r,n){"use strict";function isBot(r){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(r)}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isBot",{enumerable:!0,get:function(){return isBot}})},8011:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isDynamicRoute",{enumerable:!0,get:function(){return isDynamicRoute}});let o=/\/\[[^/]+?\](?=\/|$)/;function isDynamicRoute(r){return o.test(r)}},4131:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isLocalURL",{enumerable:!0,get:function(){return isLocalURL}});let l=o(1306),u=o(5213);function isLocalURL(r){if(!(0,l.isAbsoluteUrl)(r))return!0;try{let n=(0,l.getLocationOrigin)(),o=new URL(r,n);return o.origin===n&&(0,u.hasBasePath)(o.pathname)}catch(r){return!1}}},9302:function(r,n){"use strict";function omit(r,n){let o={};return Object.keys(r).forEach(l=>{n.includes(l)||(o[l]=r[l])}),o}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"omit",{enumerable:!0,get:function(){return omit}})},891:function(r,n){"use strict";function parsePath(r){let n=r.indexOf("#"),o=r.indexOf("?"),l=o>-1&&(n<0||o<n);return l||n>-1?{pathname:r.substring(0,l?o:n),query:l?r.substring(o,n>-1?n:void 0):"",hash:n>-1?r.slice(n):""}:{pathname:r,query:"",hash:""}}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"parsePath",{enumerable:!0,get:function(){return parsePath}})},9823:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"parseRelativeUrl",{enumerable:!0,get:function(){return parseRelativeUrl}});let l=o(1306),u=o(4828);function parseRelativeUrl(r,n){let o=new URL((0,l.getLocationOrigin)()),s=n?new URL(n,o):r.startsWith(".")?new URL(window.location.href):o,{pathname:d,searchParams:f,search:h,hash:g,href:_,origin:y}=new URL(r,s);if(y!==o.origin)throw Error("invariant: invalid relative URL, router received "+r);return{pathname:d,query:(0,u.searchParamsToUrlQuery)(f),search:h,hash:g,href:_.slice(o.origin.length)}}},6315:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"pathHasPrefix",{enumerable:!0,get:function(){return pathHasPrefix}});let l=o(891);function pathHasPrefix(r,n){if("string"!=typeof r)return!1;let{pathname:o}=(0,l.parsePath)(r);return o===n||o.startsWith(n+"/")}},4828:function(r,n){"use strict";function searchParamsToUrlQuery(r){let n={};return r.forEach((r,o)=>{void 0===n[o]?n[o]=r:Array.isArray(n[o])?n[o].push(r):n[o]=[n[o],r]}),n}function stringifyUrlQueryParam(r){return"string"!=typeof r&&("number"!=typeof r||isNaN(r))&&"boolean"!=typeof r?"":String(r)}function urlQueryToSearchParams(r){let n=new URLSearchParams;return Object.entries(r).forEach(r=>{let[o,l]=r;Array.isArray(l)?l.forEach(r=>n.append(o,stringifyUrlQueryParam(r))):n.set(o,stringifyUrlQueryParam(l))}),n}function assign(r){for(var n=arguments.length,o=Array(n>1?n-1:0),l=1;l<n;l++)o[l-1]=arguments[l];return o.forEach(n=>{Array.from(n.keys()).forEach(n=>r.delete(n)),n.forEach((n,o)=>r.append(o,n))}),r}Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{searchParamsToUrlQuery:function(){return searchParamsToUrlQuery},urlQueryToSearchParams:function(){return urlQueryToSearchParams},assign:function(){return assign}})},4980:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"removePathPrefix",{enumerable:!0,get:function(){return removePathPrefix}});let l=o(6315);function removePathPrefix(r,n){if(!(0,l.pathHasPrefix)(r,n))return r;let o=r.slice(n.length);return o.startsWith("/")?o:"/"+o}},6082:function(r,n){"use strict";function removeTrailingSlash(r){return r.replace(/\/$/,"")||"/"}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"removeTrailingSlash",{enumerable:!0,get:function(){return removeTrailingSlash}})},9001:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"getRouteMatcher",{enumerable:!0,get:function(){return getRouteMatcher}});let l=o(1306);function getRouteMatcher(r){let{re:n,groups:o}=r;return r=>{let u=n.exec(r);if(!u)return!1;let decode=r=>{try{return decodeURIComponent(r)}catch(r){throw new l.DecodeError("failed to decode param")}},s={};return Object.keys(o).forEach(r=>{let n=o[r],l=u[n.pos];void 0!==l&&(s[r]=~l.indexOf("/")?l.split("/").map(r=>decode(r)):n.repeat?[decode(l)]:decode(l))}),s}}},8300:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{getRouteRegex:function(){return getRouteRegex},getNamedRouteRegex:function(){return getNamedRouteRegex},getNamedMiddlewareRegex:function(){return getNamedMiddlewareRegex}});let l=o(2407),u=o(4125),s=o(6082);function parseParameter(r){let n=r.startsWith("[")&&r.endsWith("]");n&&(r=r.slice(1,-1));let o=r.startsWith("...");return o&&(r=r.slice(3)),{key:r,repeat:o,optional:n}}function getParametrizedRoute(r){let n=(0,s.removeTrailingSlash)(r).slice(1).split("/"),o={},d=1;return{parameterizedRoute:n.map(r=>{let n=l.INTERCEPTION_ROUTE_MARKERS.find(n=>r.startsWith(n)),s=r.match(/\[((?:\[.*\])|.+)\]/);if(n&&s){let{key:r,optional:l,repeat:f}=parseParameter(s[1]);return o[r]={pos:d++,repeat:f,optional:l},"/"+(0,u.escapeStringRegexp)(n)+"([^/]+?)"}if(!s)return"/"+(0,u.escapeStringRegexp)(r);{let{key:r,repeat:n,optional:l}=parseParameter(s[1]);return o[r]={pos:d++,repeat:n,optional:l},n?l?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:o}}function getRouteRegex(r){let{parameterizedRoute:n,groups:o}=getParametrizedRoute(r);return{re:RegExp("^"+n+"(?:/)?$"),groups:o}}function getSafeKeyFromSegment(r){let{getSafeRouteKey:n,segment:o,routeKeys:l,keyPrefix:u}=r,{key:s,optional:d,repeat:f}=parseParameter(o),h=s.replace(/\W/g,"");u&&(h=""+u+h);let g=!1;return(0===h.length||h.length>30)&&(g=!0),isNaN(parseInt(h.slice(0,1)))||(g=!0),g&&(h=n()),u?l[h]=""+u+s:l[h]=""+s,f?d?"(?:/(?<"+h+">.+?))?":"/(?<"+h+">.+?)":"/(?<"+h+">[^/]+?)"}function getNamedParametrizedRoute(r,n){let o;let d=(0,s.removeTrailingSlash)(r).slice(1).split("/"),f=(o=0,()=>{let r="",n=++o;for(;n>0;)r+=String.fromCharCode(97+(n-1)%26),n=Math.floor((n-1)/26);return r}),h={};return{namedParameterizedRoute:d.map(r=>{let o=l.INTERCEPTION_ROUTE_MARKERS.some(n=>r.startsWith(n)),s=r.match(/\[((?:\[.*\])|.+)\]/);return o&&s?getSafeKeyFromSegment({getSafeRouteKey:f,segment:s[1],routeKeys:h,keyPrefix:n?"nxtI":void 0}):s?getSafeKeyFromSegment({getSafeRouteKey:f,segment:s[1],routeKeys:h,keyPrefix:n?"nxtP":void 0}):"/"+(0,u.escapeStringRegexp)(r)}).join(""),routeKeys:h}}function getNamedRouteRegex(r,n){let o=getNamedParametrizedRoute(r,n);return{...getRouteRegex(r),namedRegex:"^"+o.namedParameterizedRoute+"(?:/)?$",routeKeys:o.routeKeys}}function getNamedMiddlewareRegex(r,n){let{parameterizedRoute:o}=getParametrizedRoute(r),{catchAll:l=!0}=n;if("/"===o)return{namedRegex:"^/"+(l?".*":"")+"$"};let{namedParameterizedRoute:u}=getNamedParametrizedRoute(r,!1);return{namedRegex:"^"+u+(l?"(?:(/.*)?)":"")+"$"}}},2839:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"getSortedRoutes",{enumerable:!0,get:function(){return getSortedRoutes}});let UrlNode=class UrlNode{insert(r){this._insert(r.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(r){void 0===r&&(r="/");let n=[...this.children.keys()].sort();null!==this.slugName&&n.splice(n.indexOf("[]"),1),null!==this.restSlugName&&n.splice(n.indexOf("[...]"),1),null!==this.optionalRestSlugName&&n.splice(n.indexOf("[[...]]"),1);let o=n.map(n=>this.children.get(n)._smoosh(""+r+n+"/")).reduce((r,n)=>[...r,...n],[]);if(null!==this.slugName&&o.push(...this.children.get("[]")._smoosh(r+"["+this.slugName+"]/")),!this.placeholder){let n="/"===r?"/":r.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+n+'" and "'+n+"[[..."+this.optionalRestSlugName+']]").');o.unshift(n)}return null!==this.restSlugName&&o.push(...this.children.get("[...]")._smoosh(r+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&o.push(...this.children.get("[[...]]")._smoosh(r+"[[..."+this.optionalRestSlugName+"]]/")),o}_insert(r,n,o){if(0===r.length){this.placeholder=!1;return}if(o)throw Error("Catch-all must be the last part of the URL.");let l=r[0];if(l.startsWith("[")&&l.endsWith("]")){let u=l.slice(1,-1),s=!1;if(u.startsWith("[")&&u.endsWith("]")&&(u=u.slice(1,-1),s=!0),u.startsWith("...")&&(u=u.substring(3),o=!0),u.startsWith("[")||u.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+u+"').");if(u.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+u+"').");function handleSlug(r,o){if(null!==r&&r!==o)throw Error("You cannot use different slug names for the same dynamic path ('"+r+"' !== '"+o+"').");n.forEach(r=>{if(r===o)throw Error('You cannot have the same slug name "'+o+'" repeat within a single dynamic path');if(r.replace(/\W/g,"")===l.replace(/\W/g,""))throw Error('You cannot have the slug names "'+r+'" and "'+o+'" differ only by non-word symbols within a single dynamic path')}),n.push(o)}if(o){if(s){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+r[0]+'" ).');handleSlug(this.optionalRestSlugName,u),this.optionalRestSlugName=u,l="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+r[0]+'").');handleSlug(this.restSlugName,u),this.restSlugName=u,l="[...]"}}else{if(s)throw Error('Optional route parameters are not yet supported ("'+r[0]+'").');handleSlug(this.slugName,u),this.slugName=u,l="[]"}}this.children.has(l)||this.children.set(l,new UrlNode),this.children.get(l)._insert(r.slice(1),n,o)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}};function getSortedRoutes(r){let n=new UrlNode;return r.forEach(r=>n.insert(r)),n.smoosh()}},510:function(r,n){"use strict";let o;Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{default:function(){return _default},setConfig:function(){return setConfig}});let _default=()=>o;function setConfig(r){o=r}},7494:function(r,n){"use strict";function isGroupSegment(r){return"("===r[0]&&r.endsWith(")")}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isGroupSegment",{enumerable:!0,get:function(){return isGroupSegment}})},3632:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return SideEffect}});let l=o(7294),u=l.useLayoutEffect,s=l.useEffect;function SideEffect(r){let{headManager:n,reduceComponentsToState:o}=r;function emitChange(){if(n&&n.mountedInstances){let u=l.Children.toArray(Array.from(n.mountedInstances).filter(Boolean));n.updateHead(o(u,r))}}return u(()=>{var o;return null==n||null==(o=n.mountedInstances)||o.add(r.children),()=>{var o;null==n||null==(o=n.mountedInstances)||o.delete(r.children)}}),u(()=>(n&&(n._pendingUpdate=emitChange),()=>{n&&(n._pendingUpdate=emitChange)})),s(()=>(n&&n._pendingUpdate&&(n._pendingUpdate(),n._pendingUpdate=null),()=>{n&&n._pendingUpdate&&(n._pendingUpdate(),n._pendingUpdate=null)})),null}},1306:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{WEB_VITALS:function(){return o},execOnce:function(){return execOnce},isAbsoluteUrl:function(){return isAbsoluteUrl},getLocationOrigin:function(){return getLocationOrigin},getURL:function(){return getURL},getDisplayName:function(){return getDisplayName},isResSent:function(){return isResSent},normalizeRepeatedSlashes:function(){return normalizeRepeatedSlashes},loadGetInitialProps:function(){return loadGetInitialProps},SP:function(){return u},ST:function(){return s},DecodeError:function(){return DecodeError},NormalizeError:function(){return NormalizeError},PageNotFoundError:function(){return PageNotFoundError},MissingStaticPage:function(){return MissingStaticPage},MiddlewareNotFoundError:function(){return MiddlewareNotFoundError},stringifyError:function(){return stringifyError}});let o=["CLS","FCP","FID","INP","LCP","TTFB"];function execOnce(r){let n,o=!1;return function(){for(var l=arguments.length,u=Array(l),s=0;s<l;s++)u[s]=arguments[s];return o||(o=!0,n=r(...u)),n}}let l=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,isAbsoluteUrl=r=>l.test(r);function getLocationOrigin(){let{protocol:r,hostname:n,port:o}=window.location;return r+"//"+n+(o?":"+o:"")}function getURL(){let{href:r}=window.location,n=getLocationOrigin();return r.substring(n.length)}function getDisplayName(r){return"string"==typeof r?r:r.displayName||r.name||"Unknown"}function isResSent(r){return r.finished||r.headersSent}function normalizeRepeatedSlashes(r){let n=r.split("?"),o=n[0];return o.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(n[1]?"?"+n.slice(1).join("?"):"")}async function loadGetInitialProps(r,n){let o=n.res||n.ctx&&n.ctx.res;if(!r.getInitialProps)return n.ctx&&n.Component?{pageProps:await loadGetInitialProps(n.Component,n.ctx)}:{};let l=await r.getInitialProps(n);if(o&&isResSent(o))return l;if(!l){let n='"'+getDisplayName(r)+'.getInitialProps()" should resolve to an object. But found "'+l+'" instead.';throw Error(n)}return l}let u="undefined"!=typeof performance,s=u&&["mark","measure","getEntriesByName"].every(r=>"function"==typeof performance[r]);let DecodeError=class DecodeError extends Error{};let NormalizeError=class NormalizeError extends Error{};let PageNotFoundError=class PageNotFoundError extends Error{constructor(r){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+r}};let MissingStaticPage=class MissingStaticPage extends Error{constructor(r,n){super(),this.message="Failed to load static file for page: "+r+" "+n}};let MiddlewareNotFoundError=class MiddlewareNotFoundError extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}};function stringifyError(r){return JSON.stringify({message:r.message,stack:r.stack})}},9941:function(r,n){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"warnOnce",{enumerable:!0,get:function(){return warnOnce}});let warnOnce=r=>{}},8018:function(r){var n,o,l,u,s,d,f,h,g,_,y,P,b,E,S,R,w,O,j,A,M,C,I,x,N,L,D,k,F,U,H,B,W,q,z,G,V,X,Y,K,Q,$,J,Z,ee,et;(n={}).d=function(r,o){for(var l in o)n.o(o,l)&&!n.o(r,l)&&Object.defineProperty(r,l,{enumerable:!0,get:o[l]})},n.o=function(r,n){return Object.prototype.hasOwnProperty.call(r,n)},n.r=function(r){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(r,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(r,"__esModule",{value:!0})},void 0!==n&&(n.ab="//"),o={},n.r(o),n.d(o,{getCLS:function(){return I},getFCP:function(){return A},getFID:function(){return U},getINP:function(){return $},getLCP:function(){return Z},getTTFB:function(){return et},onCLS:function(){return I},onFCP:function(){return A},onFID:function(){return U},onINP:function(){return $},onLCP:function(){return Z},onTTFB:function(){return et}}),h=-1,g=function(r){addEventListener("pageshow",function(n){n.persisted&&(h=n.timeStamp,r(n))},!0)},_=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},y=function(){var r=_();return r&&r.activationStart||0},P=function(r,n){var o=_(),l="navigate";return h>=0?l="back-forward-cache":o&&(l=document.prerendering||y()>0?"prerender":o.type.replace(/_/g,"-")),{name:r,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:l}},b=function(r,n,o){try{if(PerformanceObserver.supportedEntryTypes.includes(r)){var l=new PerformanceObserver(function(r){n(r.getEntries())});return l.observe(Object.assign({type:r,buffered:!0},o||{})),l}}catch(r){}},E=function(r,n){var T=function t(o){"pagehide"!==o.type&&"hidden"!==document.visibilityState||(r(o),n&&(removeEventListener("visibilitychange",t,!0),removeEventListener("pagehide",t,!0)))};addEventListener("visibilitychange",T,!0),addEventListener("pagehide",T,!0)},S=function(r,n,o,l){var u,s;return function(d){var f;n.value>=0&&(d||l)&&((s=n.value-(u||0))||void 0===u)&&(u=n.value,n.delta=s,n.rating=(f=n.value)>o[1]?"poor":f>o[0]?"needs-improvement":"good",r(n))}},R=-1,w=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},O=function(){E(function(r){R=r.timeStamp},!0)},j=function(){return R<0&&(R=w(),O(),g(function(){setTimeout(function(){R=w(),O()},0)})),{get firstHiddenTime(){return R}}},A=function(r,n){n=n||{};var o,l=[1800,3e3],u=j(),s=P("FCP"),c=function(r){r.forEach(function(r){"first-contentful-paint"===r.name&&(f&&f.disconnect(),r.startTime<u.firstHiddenTime&&(s.value=r.startTime-y(),s.entries.push(r),o(!0)))})},d=window.performance&&window.performance.getEntriesByName&&window.performance.getEntriesByName("first-contentful-paint")[0],f=d?null:b("paint",c);(d||f)&&(o=S(r,s,l,n.reportAllChanges),d&&c([d]),g(function(u){o=S(r,s=P("FCP"),l,n.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){s.value=performance.now()-u.timeStamp,o(!0)})})}))},M=!1,C=-1,I=function(r,n){n=n||{};var o=[.1,.25];M||(A(function(r){C=r.value}),M=!0);var l,i=function(n){C>-1&&r(n)},u=P("CLS",0),s=0,d=[],p=function(r){r.forEach(function(r){if(!r.hadRecentInput){var n=d[0],o=d[d.length-1];s&&r.startTime-o.startTime<1e3&&r.startTime-n.startTime<5e3?(s+=r.value,d.push(r)):(s=r.value,d=[r]),s>u.value&&(u.value=s,u.entries=d,l())}})},f=b("layout-shift",p);f&&(l=S(i,u,o,n.reportAllChanges),E(function(){p(f.takeRecords()),l(!0)}),g(function(){s=0,C=-1,l=S(i,u=P("CLS",0),o,n.reportAllChanges)}))},x={passive:!0,capture:!0},N=new Date,L=function(r,n){l||(l=n,u=r,s=new Date,F(removeEventListener),D())},D=function(){if(u>=0&&u<s-N){var r={entryType:"first-input",name:l.type,target:l.target,cancelable:l.cancelable,startTime:l.timeStamp,processingStart:l.timeStamp+u};d.forEach(function(n){n(r)}),d=[]}},k=function(r){if(r.cancelable){var n,o,l,u=(r.timeStamp>1e12?new Date:performance.now())-r.timeStamp;"pointerdown"==r.type?(n=function(){L(u,r),l()},o=function(){l()},l=function(){removeEventListener("pointerup",n,x),removeEventListener("pointercancel",o,x)},addEventListener("pointerup",n,x),addEventListener("pointercancel",o,x)):L(u,r)}},F=function(r){["mousedown","keydown","touchstart","pointerdown"].forEach(function(n){return r(n,k,x)})},U=function(r,n){n=n||{};var o,s=[100,300],f=j(),h=P("FID"),v=function(r){r.startTime<f.firstHiddenTime&&(h.value=r.processingStart-r.startTime,h.entries.push(r),o(!0))},m=function(r){r.forEach(v)},_=b("first-input",m);o=S(r,h,s,n.reportAllChanges),_&&E(function(){m(_.takeRecords()),_.disconnect()},!0),_&&g(function(){o=S(r,h=P("FID"),s,n.reportAllChanges),d=[],u=-1,l=null,F(addEventListener),d.push(v),D()})},H=0,B=1/0,W=0,q=function(r){r.forEach(function(r){r.interactionId&&(B=Math.min(B,r.interactionId),H=(W=Math.max(W,r.interactionId))?(W-B)/7+1:0)})},z=function(){return f?H:performance.interactionCount||0},G=function(){"interactionCount"in performance||f||(f=b("event",q,{type:"event",buffered:!0,durationThreshold:0}))},V=0,X=function(){return z()-V},Y=[],K={},Q=function(r){var n=Y[Y.length-1],o=K[r.interactionId];if(o||Y.length<10||r.duration>n.latency){if(o)o.entries.push(r),o.latency=Math.max(o.latency,r.duration);else{var l={id:r.interactionId,latency:r.duration,entries:[r]};K[l.id]=l,Y.push(l)}Y.sort(function(r,n){return n.latency-r.latency}),Y.splice(10).forEach(function(r){delete K[r.id]})}},$=function(r,n){n=n||{};var o=[200,500];G();var l,u=P("INP"),a=function(r){r.forEach(function(r){r.interactionId&&Q(r),"first-input"!==r.entryType||Y.some(function(n){return n.entries.some(function(n){return r.duration===n.duration&&r.startTime===n.startTime})})||Q(r)});var n,o=(n=Math.min(Y.length-1,Math.floor(X()/50)),Y[n]);o&&o.latency!==u.value&&(u.value=o.latency,u.entries=o.entries,l())},s=b("event",a,{durationThreshold:n.durationThreshold||40});l=S(r,u,o,n.reportAllChanges),s&&(s.observe({type:"first-input",buffered:!0}),E(function(){a(s.takeRecords()),u.value<0&&X()>0&&(u.value=0,u.entries=[]),l(!0)}),g(function(){Y=[],V=z(),l=S(r,u=P("INP"),o,n.reportAllChanges)}))},J={},Z=function(r,n){n=n||{};var o,l=[2500,4e3],u=j(),s=P("LCP"),c=function(r){var n=r[r.length-1];if(n){var l=n.startTime-y();l<u.firstHiddenTime&&(s.value=l,s.entries=[n],o())}},d=b("largest-contentful-paint",c);if(d){o=S(r,s,l,n.reportAllChanges);var v=function(){J[s.id]||(c(d.takeRecords()),d.disconnect(),J[s.id]=!0,o(!0))};["keydown","click"].forEach(function(r){addEventListener(r,v,{once:!0,capture:!0})}),E(v,!0),g(function(u){o=S(r,s=P("LCP"),l,n.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){s.value=performance.now()-u.timeStamp,J[s.id]=!0,o(!0)})})})}},ee=function e(r){document.prerendering?addEventListener("prerenderingchange",function(){return e(r)},!0):"complete"!==document.readyState?addEventListener("load",function(){return e(r)},!0):setTimeout(r,0)},et=function(r,n){n=n||{};var o=[800,1800],l=P("TTFB"),u=S(r,l,o,n.reportAllChanges);ee(function(){var s=_();if(s){if(l.value=Math.max(s.responseStart-y(),0),l.value<0||l.value>performance.now())return;l.entries=[s],u(!0),g(function(){(u=S(r,l=P("TTFB",0),o,n.reportAllChanges))(!0)})}})},r.exports=o},9423:function(r,n){"use strict";function isAPIRoute(r){return"/api"===r||!!(null==r?void 0:r.startsWith("/api/"))}Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isAPIRoute",{enumerable:!0,get:function(){return isAPIRoute}})},676:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{default:function(){return isError},getProperError:function(){return getProperError}});let l=o(9942);function isError(r){return"object"==typeof r&&null!==r&&"name"in r&&"message"in r}function getProperError(r){return isError(r)?r:Error((0,l.isPlainObject)(r)?JSON.stringify(r):r+"")}},2407:function(r,n,o){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(r,n){for(var o in n)Object.defineProperty(r,o,{enumerable:!0,get:n[o]})}(n,{INTERCEPTION_ROUTE_MARKERS:function(){return u},isInterceptionRouteAppPath:function(){return isInterceptionRouteAppPath},extractInterceptionRouteInformation:function(){return extractInterceptionRouteInformation}});let l=o(9537),u=["(..)(..)","(.)","(..)","(...)"];function isInterceptionRouteAppPath(r){return void 0!==r.split("/").find(r=>u.find(n=>r.startsWith(n)))}function extractInterceptionRouteInformation(r){let n,o,s;for(let l of r.split("/"))if(o=u.find(r=>l.startsWith(r))){[n,s]=r.split(o,2);break}if(!n||!o||!s)throw Error(`Invalid interception route: ${r}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(n=(0,l.normalizeAppPath)(n),o){case"(.)":s="/"===n?`/${s}`:n+"/"+s;break;case"(..)":if("/"===n)throw Error(`Invalid interception route: ${r}. Cannot use (..) marker at the root level, use (.) instead.`);s=n.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let d=n.split("/");if(d.length<=2)throw Error(`Invalid interception route: ${r}. Cannot use (..)(..) marker at the root level or one level up.`);s=d.slice(0,-2).concat(s).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:n,interceptedRoute:s}}},2431:function(){},8754:function(r,n,o){"use strict";function _interop_require_default(r){return r&&r.__esModule?r:{default:r}}o.r(n),o.d(n,{_:function(){return _interop_require_default},_interop_require_default:function(){return _interop_require_default}})},1757:function(r,n,o){"use strict";function _getRequireWildcardCache(r){if("function"!=typeof WeakMap)return null;var n=new WeakMap,o=new WeakMap;return(_getRequireWildcardCache=function(r){return r?o:n})(r)}function _interop_require_wildcard(r,n){if(!n&&r&&r.__esModule)return r;if(null===r||"object"!=typeof r&&"function"!=typeof r)return{default:r};var o=_getRequireWildcardCache(n);if(o&&o.has(r))return o.get(r);var l={},u=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in r)if("default"!==s&&Object.prototype.hasOwnProperty.call(r,s)){var d=u?Object.getOwnPropertyDescriptor(r,s):null;d&&(d.get||d.set)?Object.defineProperty(l,s,d):l[s]=r[s]}return l.default=r,o&&o.set(r,l),l}o.r(n),o.d(n,{_:function(){return _interop_require_wildcard},_interop_require_wildcard:function(){return _interop_require_wildcard}})}},function(r){r.O(0,[774],function(){return r(r.s=6653)}),_N_E=r.O()}]);