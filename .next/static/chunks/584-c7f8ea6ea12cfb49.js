(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[584],{3925:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},5606:function(e,n,r){"use strict";r.d(n,{z:function(){return Presence}});var a=r(2265),s=r(2210),i=r(1030),Presence=e=>{let n,r;let{present:o,children:l}=e,u=function(e){var n;let[r,s]=a.useState(),o=a.useRef({}),l=a.useRef(e),u=a.useRef("none"),h=e?"mounted":"unmounted",[f,p]=(n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},a.useReducer((e,r)=>{let a=n[e][r];return a??e},h));return a.useEffect(()=>{let e=getAnimationName(o.current);u.current="mounted"===f?e:"none"},[f]),(0,i.b)(()=>{let n=o.current,r=l.current,a=r!==e;if(a){let a=u.current,s=getAnimationName(n);e?p("MOUNT"):"none"===s||n?.display==="none"?p("UNMOUNT"):r&&a!==s?p("ANIMATION_OUT"):p("UNMOUNT"),l.current=e}},[e,p]),(0,i.b)(()=>{if(r){let e;let n=r.ownerDocument.defaultView??window,handleAnimationEnd=a=>{let s=getAnimationName(o.current),i=s.includes(a.animationName);if(a.target===r&&i&&(p("ANIMATION_END"),!l.current)){let a=r.style.animationFillMode;r.style.animationFillMode="forwards",e=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=a)})}},handleAnimationStart=e=>{e.target===r&&(u.current=getAnimationName(o.current))};return r.addEventListener("animationstart",handleAnimationStart),r.addEventListener("animationcancel",handleAnimationEnd),r.addEventListener("animationend",handleAnimationEnd),()=>{n.clearTimeout(e),r.removeEventListener("animationstart",handleAnimationStart),r.removeEventListener("animationcancel",handleAnimationEnd),r.removeEventListener("animationend",handleAnimationEnd)}}p("ANIMATION_END")},[r,p]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:a.useCallback(e=>{e&&(o.current=getComputedStyle(e)),s(e)},[])}}(o),h="function"==typeof l?l({present:u.isPresent}):a.Children.only(l),f=(0,s.e)(u.ref,(n=Object.getOwnPropertyDescriptor(h.props,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning?h.ref:(n=Object.getOwnPropertyDescriptor(h,"ref")?.get)&&"isReactWarning"in n&&n.isReactWarning?h.props.ref:h.props.ref||h.ref),p="function"==typeof l;return p||u.isPresent?a.cloneElement(h,{ref:f}):null};function getAnimationName(e){return e?.animationName||"none"}Presence.displayName="Presence"},8834:function(e,n,r){"use strict";r.d(n,{aU:function(){return es},x8:function(){return ei},dk:function(){return ea},zt:function(){return ee},fC:function(){return en},Dx:function(){return er},l_:function(){return et}});var a,s=r(2265),i=r(4887),o=r(5744),l=r(2210),u=r(3651),h=r(6989),f=r(9790),p=r(6459),m=r(7437),y="dismissableLayer.update",v=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),g=s.forwardRef((e,n)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:h,onInteractOutside:g,onDismiss:b,...w}=e,x=s.useContext(v),[E,C]=s.useState(null),P=E?.ownerDocument??globalThis?.document,[,T]=s.useState({}),D=(0,l.e)(n,e=>C(e)),S=Array.from(x.layers),[O]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),R=S.indexOf(O),M=E?S.indexOf(E):-1,F=x.layersWithOutsidePointerEventsDisabled.size>0,k=M>=R,L=function(e,n=globalThis?.document){let r=(0,p.W)(e),a=s.useRef(!1),i=s.useRef(()=>{});return s.useEffect(()=>{let handlePointerDown=e=>{if(e.target&&!a.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",r,a,{discrete:!0})},a={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=handleAndDispatchPointerDownOutsideEvent2,n.addEventListener("click",i.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else n.removeEventListener("click",i.current);a.current=!1},e=window.setTimeout(()=>{n.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),n.removeEventListener("pointerdown",handlePointerDown),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let n=e.target,r=[...x.branches].some(e=>e.contains(n));!k||r||(u?.(e),g?.(e),e.defaultPrevented||b?.())},P),N=function(e,n=globalThis?.document){let r=(0,p.W)(e),a=s.useRef(!1);return s.useEffect(()=>{let handleFocus=e=>{e.target&&!a.current&&handleAndDispatchCustomEvent("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",handleFocus),()=>n.removeEventListener("focusin",handleFocus)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let n=e.target,r=[...x.branches].some(e=>e.contains(n));r||(h?.(e),g?.(e),e.defaultPrevented||b?.())},P);return!function(e,n=globalThis?.document){let r=(0,p.W)(e);s.useEffect(()=>{let handleKeyDown=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>n.removeEventListener("keydown",handleKeyDown,{capture:!0})},[r,n])}(e=>{let n=M===x.layers.size-1;n&&(i?.(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},P),s.useEffect(()=>{if(E)return r&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(a=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(E)),x.layers.add(E),dispatchUpdate(),()=>{r&&1===x.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=a)}},[E,P,r,x]),s.useEffect(()=>()=>{E&&(x.layers.delete(E),x.layersWithOutsidePointerEventsDisabled.delete(E),dispatchUpdate())},[E,x]),s.useEffect(()=>{let handleUpdate=()=>T({});return document.addEventListener(y,handleUpdate),()=>document.removeEventListener(y,handleUpdate)},[]),(0,m.jsx)(f.WV.div,{...w,ref:D,style:{pointerEvents:F?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,L.onPointerDownCapture)})});g.displayName="DismissableLayer";var b=s.forwardRef((e,n)=>{let r=s.useContext(v),a=s.useRef(null),i=(0,l.e)(n,a);return s.useEffect(()=>{let e=a.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,m.jsx)(f.WV.div,{...e,ref:i})});function dispatchUpdate(){let e=new CustomEvent(y);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,n,r,{discrete:a}){let s=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});n&&s.addEventListener(e,n,{once:!0}),a?(0,f.jH)(s,i):s.dispatchEvent(i)}b.displayName="DismissableLayerBranch";var w=r(1030),x=s.forwardRef((e,n)=>{let{container:r,...a}=e,[o,l]=s.useState(!1);(0,w.b)(()=>l(!0),[]);let u=r||o&&globalThis?.document?.body;return u?i.createPortal((0,m.jsx)(f.WV.div,{...a,ref:n}),u):null});x.displayName="Portal";var E=r(5606),C=r(3763),P=r(8281),T="ToastProvider",[D,S,O]=(0,u.B)("Toast"),[R,M]=(0,h.b)("Toast",[O]),[F,k]=R(T),ToastProvider=e=>{let{__scopeToast:n,label:r="Notification",duration:a=5e3,swipeDirection:i="right",swipeThreshold:o=50,children:l}=e,[u,h]=s.useState(null),[f,p]=s.useState(0),y=s.useRef(!1),v=s.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${T}\`. Expected non-empty \`string\`.`),(0,m.jsx)(D.Provider,{scope:n,children:(0,m.jsx)(F,{scope:n,label:r,duration:a,swipeDirection:i,swipeThreshold:o,toastCount:f,viewport:u,onViewportChange:h,onToastAdd:s.useCallback(()=>p(e=>e+1),[]),onToastRemove:s.useCallback(()=>p(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:y,isClosePausedRef:v,children:l})})};ToastProvider.displayName=T;var L="ToastViewport",N=["F8"],A="toast.viewportPause",I="toast.viewportResume",q=s.forwardRef((e,n)=>{let{__scopeToast:r,hotkey:a=N,label:i="Notifications ({hotkey})",...o}=e,u=k(L,r),h=S(r),p=s.useRef(null),y=s.useRef(null),v=s.useRef(null),g=s.useRef(null),w=(0,l.e)(n,g,u.onViewportChange),x=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=u.toastCount>0;s.useEffect(()=>{let handleKeyDown=e=>{let n=0!==a.length&&a.every(n=>e[n]||e.code===n);n&&g.current?.focus()};return document.addEventListener("keydown",handleKeyDown),()=>document.removeEventListener("keydown",handleKeyDown)},[a]),s.useEffect(()=>{let e=p.current,n=g.current;if(E&&e&&n){let handlePause=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(A);n.dispatchEvent(e),u.isClosePausedRef.current=!0}},handleResume=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(I);n.dispatchEvent(e),u.isClosePausedRef.current=!1}},handleFocusOutResume=n=>{let r=!e.contains(n.relatedTarget);r&&handleResume()},handlePointerLeaveResume=()=>{let n=e.contains(document.activeElement);n||handleResume()};return e.addEventListener("focusin",handlePause),e.addEventListener("focusout",handleFocusOutResume),e.addEventListener("pointermove",handlePause),e.addEventListener("pointerleave",handlePointerLeaveResume),window.addEventListener("blur",handlePause),window.addEventListener("focus",handleResume),()=>{e.removeEventListener("focusin",handlePause),e.removeEventListener("focusout",handleFocusOutResume),e.removeEventListener("pointermove",handlePause),e.removeEventListener("pointerleave",handlePointerLeaveResume),window.removeEventListener("blur",handlePause),window.removeEventListener("focus",handleResume)}}},[E,u.isClosePausedRef]);let C=s.useCallback(({tabbingDirection:e})=>{let n=h(),r=n.map(n=>{let r=n.ref.current,a=[r,...function(e){let n=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let n="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||n?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)n.push(r.currentNode);return n}(r)];return"forwards"===e?a:a.reverse()});return("forwards"===e?r.reverse():r).flat()},[h]);return s.useEffect(()=>{let e=g.current;if(e){let handleKeyDown=n=>{let r=n.altKey||n.ctrlKey||n.metaKey,a="Tab"===n.key&&!r;if(a){let r=document.activeElement,a=n.shiftKey,s=n.target===e;if(s&&a){y.current?.focus();return}let i=C({tabbingDirection:a?"backwards":"forwards"}),o=i.findIndex(e=>e===r);focusFirst(i.slice(o+1))?n.preventDefault():a?y.current?.focus():v.current?.focus()}};return e.addEventListener("keydown",handleKeyDown),()=>e.removeEventListener("keydown",handleKeyDown)}},[h,C]),(0,m.jsxs)(b,{ref:p,role:"region","aria-label":i.replace("{hotkey}",x),tabIndex:-1,style:{pointerEvents:E?void 0:"none"},children:[E&&(0,m.jsx)(B,{ref:y,onFocusFromOutsideViewport:()=>{let e=C({tabbingDirection:"forwards"});focusFirst(e)}}),(0,m.jsx)(D.Slot,{scope:r,children:(0,m.jsx)(f.WV.ol,{tabIndex:-1,...o,ref:w})}),E&&(0,m.jsx)(B,{ref:v,onFocusFromOutsideViewport:()=>{let e=C({tabbingDirection:"backwards"});focusFirst(e)}})]})});q.displayName=L;var K="ToastFocusProxy",B=s.forwardRef((e,n)=>{let{__scopeToast:r,onFocusFromOutsideViewport:a,...s}=e,i=k(K,r);return(0,m.jsx)(P.T,{"aria-hidden":!0,tabIndex:0,...s,ref:n,style:{position:"fixed"},onFocus:e=>{let n=e.relatedTarget,r=!i.viewport?.contains(n);r&&a()}})});B.displayName=K;var Q="Toast",W=s.forwardRef((e,n)=>{let{forceMount:r,open:a,defaultOpen:s,onOpenChange:i,...l}=e,[u=!0,h]=(0,C.T)({prop:a,defaultProp:s,onChange:i});return(0,m.jsx)(E.z,{present:r||u,children:(0,m.jsx)(z,{open:u,...l,ref:n,onClose:()=>h(!1),onPause:(0,p.W)(e.onPause),onResume:(0,p.W)(e.onResume),onSwipeStart:(0,o.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,o.M)(e.onSwipeMove,e=>{let{x:n,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${n}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${r}px`)}),onSwipeCancel:(0,o.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,o.M)(e.onSwipeEnd,e=>{let{x:n,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${n}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${r}px`),h(!1)})})})});W.displayName=Q;var[_,$]=R(Q,{onClose(){}}),z=s.forwardRef((e,n)=>{let{__scopeToast:r,type:a="foreground",duration:u,open:h,onClose:y,onEscapeKeyDown:v,onPause:b,onResume:w,onSwipeStart:x,onSwipeMove:E,onSwipeCancel:C,onSwipeEnd:P,...T}=e,S=k(Q,r),[O,R]=s.useState(null),M=(0,l.e)(n,e=>R(e)),F=s.useRef(null),L=s.useRef(null),N=u||S.duration,q=s.useRef(0),K=s.useRef(N),B=s.useRef(0),{onToastAdd:W,onToastRemove:$}=S,z=(0,p.W)(()=>{let e=O?.contains(document.activeElement);e&&S.viewport?.focus(),y()}),H=s.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(B.current),q.current=new Date().getTime(),B.current=window.setTimeout(z,e))},[z]);s.useEffect(()=>{let e=S.viewport;if(e){let handleResume=()=>{H(K.current),w?.()},handlePause=()=>{let e=new Date().getTime()-q.current;K.current=K.current-e,window.clearTimeout(B.current),b?.()};return e.addEventListener(A,handlePause),e.addEventListener(I,handleResume),()=>{e.removeEventListener(A,handlePause),e.removeEventListener(I,handleResume)}}},[S.viewport,N,b,w,H]),s.useEffect(()=>{h&&!S.isClosePausedRef.current&&H(N)},[h,N,S.isClosePausedRef,H]),s.useEffect(()=>(W(),()=>$()),[W,$]);let V=s.useMemo(()=>O?function getAnnounceTextContent(e){let n=[],r=Array.from(e.childNodes);return r.forEach(e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&n.push(e.textContent),e.nodeType===e.ELEMENT_NODE){let r=e.ariaHidden||e.hidden||"none"===e.style.display,a=""===e.dataset.radixToastAnnounceExclude;if(!r){if(a){let r=e.dataset.radixToastAnnounceAlt;r&&n.push(r)}else n.push(...getAnnounceTextContent(e))}}}),n}(O):null,[O]);return S.viewport?(0,m.jsxs)(m.Fragment,{children:[V&&(0,m.jsx)(ToastAnnounce,{__scopeToast:r,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:V}),(0,m.jsx)(_,{scope:r,onClose:z,children:i.createPortal((0,m.jsx)(D.ItemSlot,{scope:r,children:(0,m.jsx)(g,{asChild:!0,onEscapeKeyDown:(0,o.M)(v,()=>{S.isFocusedToastEscapeKeyDownRef.current||z(),S.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,m.jsx)(f.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":h?"open":"closed","data-swipe-direction":S.swipeDirection,...T,ref:M,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,o.M)(e.onKeyDown,e=>{"Escape"!==e.key||(v?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(S.isFocusedToastEscapeKeyDownRef.current=!0,z()))}),onPointerDown:(0,o.M)(e.onPointerDown,e=>{0===e.button&&(F.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,o.M)(e.onPointerMove,e=>{if(!F.current)return;let n=e.clientX-F.current.x,r=e.clientY-F.current.y,a=!!L.current,s=["left","right"].includes(S.swipeDirection),i=["left","up"].includes(S.swipeDirection)?Math.min:Math.max,o=s?i(0,n):0,l=s?0:i(0,r),u="touch"===e.pointerType?10:2,h={x:o,y:l},f={originalEvent:e,delta:h};a?(L.current=h,dist_handleAndDispatchCustomEvent("toast.swipeMove",E,f,{discrete:!1})):isDeltaInDirection(h,S.swipeDirection,u)?(L.current=h,dist_handleAndDispatchCustomEvent("toast.swipeStart",x,f,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(n)>u||Math.abs(r)>u)&&(F.current=null)}),onPointerUp:(0,o.M)(e.onPointerUp,e=>{let n=L.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),L.current=null,F.current=null,n){let r=e.currentTarget,a={originalEvent:e,delta:n};isDeltaInDirection(n,S.swipeDirection,S.swipeThreshold)?dist_handleAndDispatchCustomEvent("toast.swipeEnd",P,a,{discrete:!0}):dist_handleAndDispatchCustomEvent("toast.swipeCancel",C,a,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),S.viewport)})]}):null}),ToastAnnounce=e=>{let{__scopeToast:n,children:r,...a}=e,i=k(Q,n),[o,l]=s.useState(!1),[u,h]=s.useState(!1);return function(e=()=>{}){let n=(0,p.W)(e);(0,w.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(n)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[n])}(()=>l(!0)),s.useEffect(()=>{let e=window.setTimeout(()=>h(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,m.jsx)(x,{asChild:!0,children:(0,m.jsx)(P.T,{...a,children:o&&(0,m.jsxs)(m.Fragment,{children:[i.label," ",r]})})})},H=s.forwardRef((e,n)=>{let{__scopeToast:r,...a}=e;return(0,m.jsx)(f.WV.div,{...a,ref:n})});H.displayName="ToastTitle";var V=s.forwardRef((e,n)=>{let{__scopeToast:r,...a}=e;return(0,m.jsx)(f.WV.div,{...a,ref:n})});V.displayName="ToastDescription";var Y="ToastAction",G=s.forwardRef((e,n)=>{let{altText:r,...a}=e;return r.trim()?(0,m.jsx)(Z,{altText:r,asChild:!0,children:(0,m.jsx)(J,{...a,ref:n})}):(console.error(`Invalid prop \`altText\` supplied to \`${Y}\`. Expected non-empty \`string\`.`),null)});G.displayName=Y;var X="ToastClose",J=s.forwardRef((e,n)=>{let{__scopeToast:r,...a}=e,s=$(X,r);return(0,m.jsx)(Z,{asChild:!0,children:(0,m.jsx)(f.WV.button,{type:"button",...a,ref:n,onClick:(0,o.M)(e.onClick,s.onClose)})})});J.displayName=X;var Z=s.forwardRef((e,n)=>{let{__scopeToast:r,altText:a,...s}=e;return(0,m.jsx)(f.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":a||void 0,...s,ref:n})});function dist_handleAndDispatchCustomEvent(e,n,r,{discrete:a}){let s=r.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});n&&s.addEventListener(e,n,{once:!0}),a?(0,f.jH)(s,i):s.dispatchEvent(i)}var isDeltaInDirection=(e,n,r=0)=>{let a=Math.abs(e.x),s=Math.abs(e.y),i=a>s;return"left"===n||"right"===n?i&&a>r:!i&&s>r};function focusFirst(e){let n=document.activeElement;return e.some(e=>e===n||(e.focus(),document.activeElement!==n))}var ee=ToastProvider,et=q,en=W,er=H,ea=V,es=G,ei=J},3789:function(e,n,r){"use strict";r.d(n,{VY:function(){return X},zt:function(){return V},fC:function(){return Y},xz:function(){return G}});var a,s=r(2265),i=r(5744),o=r(2210),l=r(6989),u=r(9790),h=r(6459),f=r(7437),p="dismissableLayer.update",m=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=s.forwardRef((e,n)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:l,onPointerDownOutside:y,onFocusOutside:v,onInteractOutside:g,onDismiss:b,...w}=e,x=s.useContext(m),[E,C]=s.useState(null),P=E?.ownerDocument??globalThis?.document,[,T]=s.useState({}),D=(0,o.e)(n,e=>C(e)),S=Array.from(x.layers),[O]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),R=S.indexOf(O),M=E?S.indexOf(E):-1,F=x.layersWithOutsidePointerEventsDisabled.size>0,k=M>=R,L=function(e,n=globalThis?.document){let r=(0,h.W)(e),a=s.useRef(!1),i=s.useRef(()=>{});return s.useEffect(()=>{let handlePointerDown=e=>{if(e.target&&!a.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",r,a,{discrete:!0})},a={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=handleAndDispatchPointerDownOutsideEvent2,n.addEventListener("click",i.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else n.removeEventListener("click",i.current);a.current=!1},e=window.setTimeout(()=>{n.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),n.removeEventListener("pointerdown",handlePointerDown),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let n=e.target,r=[...x.branches].some(e=>e.contains(n));!k||r||(y?.(e),g?.(e),e.defaultPrevented||b?.())},P),N=function(e,n=globalThis?.document){let r=(0,h.W)(e),a=s.useRef(!1);return s.useEffect(()=>{let handleFocus=e=>{e.target&&!a.current&&handleAndDispatchCustomEvent("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",handleFocus),()=>n.removeEventListener("focusin",handleFocus)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let n=e.target,r=[...x.branches].some(e=>e.contains(n));r||(v?.(e),g?.(e),e.defaultPrevented||b?.())},P);return!function(e,n=globalThis?.document){let r=(0,h.W)(e);s.useEffect(()=>{let handleKeyDown=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>n.removeEventListener("keydown",handleKeyDown,{capture:!0})},[r,n])}(e=>{let n=M===x.layers.size-1;n&&(l?.(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},P),s.useEffect(()=>{if(E)return r&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(a=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(E)),x.layers.add(E),dispatchUpdate(),()=>{r&&1===x.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=a)}},[E,P,r,x]),s.useEffect(()=>()=>{E&&(x.layers.delete(E),x.layersWithOutsidePointerEventsDisabled.delete(E),dispatchUpdate())},[E,x]),s.useEffect(()=>{let handleUpdate=()=>T({});return document.addEventListener(p,handleUpdate),()=>document.removeEventListener(p,handleUpdate)},[]),(0,f.jsx)(u.WV.div,{...w,ref:D,style:{pointerEvents:F?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.M)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,i.M)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,i.M)(e.onPointerDownCapture,L.onPointerDownCapture)})});function dispatchUpdate(){let e=new CustomEvent(p);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,n,r,{discrete:a}){let s=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});n&&s.addEventListener(e,n,{once:!0}),a?(0,u.jH)(s,i):s.dispatchEvent(i)}y.displayName="DismissableLayer",s.forwardRef((e,n)=>{let r=s.useContext(m),a=s.useRef(null),i=(0,o.e)(n,a);return s.useEffect(()=>{let e=a.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,f.jsx)(u.WV.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var v=r(966),g=r(8452),b=r(4887),w=r(1030);s.forwardRef((e,n)=>{let{container:r,...a}=e,[i,o]=s.useState(!1);(0,w.b)(()=>o(!0),[]);let l=r||i&&globalThis?.document?.body;return l?b.createPortal((0,f.jsx)(u.WV.div,{...a,ref:n}),l):null}).displayName="Portal";var x=r(5606),E=r(7256),C=r(3763),P=r(8281),[T,D]=(0,l.b)("Tooltip",[g.D7]),S=(0,g.D7)(),O="TooltipProvider",R="tooltip.open",[M,F]=T(O),TooltipProvider=e=>{let{__scopeTooltip:n,delayDuration:r=700,skipDelayDuration:a=300,disableHoverableContent:i=!1,children:o}=e,[l,u]=s.useState(!0),h=s.useRef(!1),p=s.useRef(0);return s.useEffect(()=>{let e=p.current;return()=>window.clearTimeout(e)},[]),(0,f.jsx)(M,{scope:n,isOpenDelayed:l,delayDuration:r,onOpen:s.useCallback(()=>{window.clearTimeout(p.current),u(!1)},[]),onClose:s.useCallback(()=>{window.clearTimeout(p.current),p.current=window.setTimeout(()=>u(!0),a)},[a]),isPointerInTransitRef:h,onPointerInTransitChange:s.useCallback(e=>{h.current=e},[]),disableHoverableContent:i,children:o})};TooltipProvider.displayName=O;var k="Tooltip",[L,N]=T(k),Tooltip=e=>{let{__scopeTooltip:n,children:r,open:a,defaultOpen:i=!1,onOpenChange:o,disableHoverableContent:l,delayDuration:u}=e,h=F(k,e.__scopeTooltip),p=S(n),[m,y]=s.useState(null),b=(0,v.M)(),w=s.useRef(0),x=l??h.disableHoverableContent,E=u??h.delayDuration,P=s.useRef(!1),[T=!1,D]=(0,C.T)({prop:a,defaultProp:i,onChange:e=>{e?(h.onOpen(),document.dispatchEvent(new CustomEvent(R))):h.onClose(),o?.(e)}}),O=s.useMemo(()=>T?P.current?"delayed-open":"instant-open":"closed",[T]),M=s.useCallback(()=>{window.clearTimeout(w.current),w.current=0,P.current=!1,D(!0)},[D]),N=s.useCallback(()=>{window.clearTimeout(w.current),w.current=0,D(!1)},[D]),A=s.useCallback(()=>{window.clearTimeout(w.current),w.current=window.setTimeout(()=>{P.current=!0,D(!0),w.current=0},E)},[E,D]);return s.useEffect(()=>()=>{w.current&&(window.clearTimeout(w.current),w.current=0)},[]),(0,f.jsx)(g.fC,{...p,children:(0,f.jsx)(L,{scope:n,contentId:b,open:T,stateAttribute:O,trigger:m,onTriggerChange:y,onTriggerEnter:s.useCallback(()=>{h.isOpenDelayed?A():M()},[h.isOpenDelayed,A,M]),onTriggerLeave:s.useCallback(()=>{x?N():(window.clearTimeout(w.current),w.current=0)},[N,x]),onOpen:M,onClose:N,disableHoverableContent:x,children:r})})};Tooltip.displayName=k;var A="TooltipTrigger",I=s.forwardRef((e,n)=>{let{__scopeTooltip:r,...a}=e,l=N(A,r),h=F(A,r),p=S(r),m=s.useRef(null),y=(0,o.e)(n,m,l.onTriggerChange),v=s.useRef(!1),b=s.useRef(!1),w=s.useCallback(()=>v.current=!1,[]);return s.useEffect(()=>()=>document.removeEventListener("pointerup",w),[w]),(0,f.jsx)(g.ee,{asChild:!0,...p,children:(0,f.jsx)(u.WV.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...a,ref:y,onPointerMove:(0,i.M)(e.onPointerMove,e=>{"touch"===e.pointerType||b.current||h.isPointerInTransitRef.current||(l.onTriggerEnter(),b.current=!0)}),onPointerLeave:(0,i.M)(e.onPointerLeave,()=>{l.onTriggerLeave(),b.current=!1}),onPointerDown:(0,i.M)(e.onPointerDown,()=>{v.current=!0,document.addEventListener("pointerup",w,{once:!0})}),onFocus:(0,i.M)(e.onFocus,()=>{v.current||l.onOpen()}),onBlur:(0,i.M)(e.onBlur,l.onClose),onClick:(0,i.M)(e.onClick,l.onClose)})})});I.displayName=A;var[q,K]=T("TooltipPortal",{forceMount:void 0}),B="TooltipContent",Q=s.forwardRef((e,n)=>{let r=K(B,e.__scopeTooltip),{forceMount:a=r.forceMount,side:s="top",...i}=e,o=N(B,e.__scopeTooltip);return(0,f.jsx)(x.z,{present:a||o.open,children:o.disableHoverableContent?(0,f.jsx)(z,{side:s,...i,ref:n}):(0,f.jsx)(W,{side:s,...i,ref:n})})}),W=s.forwardRef((e,n)=>{let r=N(B,e.__scopeTooltip),a=F(B,e.__scopeTooltip),i=s.useRef(null),l=(0,o.e)(n,i),[u,h]=s.useState(null),{trigger:p,onClose:m}=r,y=i.current,{onPointerInTransitChange:v}=a,g=s.useCallback(()=>{h(null),v(!1)},[v]),b=s.useCallback((e,n)=>{let r=e.currentTarget,a={x:e.clientX,y:e.clientY},s=function(e,n){let r=Math.abs(n.top-e.y),a=Math.abs(n.bottom-e.y),s=Math.abs(n.right-e.x),i=Math.abs(n.left-e.x);switch(Math.min(r,a,s,i)){case i:return"left";case s:return"right";case r:return"top";case a:return"bottom";default:throw Error("unreachable")}}(a,r.getBoundingClientRect()),i=function(e,n,r=5){let a=[];switch(n){case"top":a.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":a.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":a.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":a.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return a}(a,s),o=function(e){let{top:n,right:r,bottom:a,left:s}=e;return[{x:s,y:n},{x:r,y:n},{x:r,y:a},{x:s,y:a}]}(n.getBoundingClientRect()),l=function(e){let n=e.slice();return n.sort((e,n)=>e.x<n.x?-1:e.x>n.x?1:e.y<n.y?-1:e.y>n.y?1:0),function(e){if(e.length<=1)return e.slice();let n=[];for(let r=0;r<e.length;r++){let a=e[r];for(;n.length>=2;){let e=n[n.length-1],r=n[n.length-2];if((e.x-r.x)*(a.y-r.y)>=(e.y-r.y)*(a.x-r.x))n.pop();else break}n.push(a)}n.pop();let r=[];for(let n=e.length-1;n>=0;n--){let a=e[n];for(;r.length>=2;){let e=r[r.length-1],n=r[r.length-2];if((e.x-n.x)*(a.y-n.y)>=(e.y-n.y)*(a.x-n.x))r.pop();else break}r.push(a)}return(r.pop(),1===n.length&&1===r.length&&n[0].x===r[0].x&&n[0].y===r[0].y)?n:n.concat(r)}(n)}([...i,...o]);h(l),v(!0)},[v]);return s.useEffect(()=>()=>g(),[g]),s.useEffect(()=>{if(p&&y){let handleTriggerLeave=e=>b(e,y),handleContentLeave=e=>b(e,p);return p.addEventListener("pointerleave",handleTriggerLeave),y.addEventListener("pointerleave",handleContentLeave),()=>{p.removeEventListener("pointerleave",handleTriggerLeave),y.removeEventListener("pointerleave",handleContentLeave)}}},[p,y,b,g]),s.useEffect(()=>{if(u){let handleTrackPointerGrace=e=>{let n=e.target,r={x:e.clientX,y:e.clientY},a=p?.contains(n)||y?.contains(n),s=!function(e,n){let{x:r,y:a}=e,s=!1;for(let e=0,i=n.length-1;e<n.length;i=e++){let o=n[e].x,l=n[e].y,u=n[i].x,h=n[i].y,f=l>a!=h>a&&r<(u-o)*(a-l)/(h-l)+o;f&&(s=!s)}return s}(r,u);a?g():s&&(g(),m())};return document.addEventListener("pointermove",handleTrackPointerGrace),()=>document.removeEventListener("pointermove",handleTrackPointerGrace)}},[p,y,u,m,g]),(0,f.jsx)(z,{...e,ref:l})}),[_,$]=T(k,{isInside:!1}),z=s.forwardRef((e,n)=>{let{__scopeTooltip:r,children:a,"aria-label":i,onEscapeKeyDown:o,onPointerDownOutside:l,...u}=e,h=N(B,r),p=S(r),{onClose:m}=h;return s.useEffect(()=>(document.addEventListener(R,m),()=>document.removeEventListener(R,m)),[m]),s.useEffect(()=>{if(h.trigger){let handleScroll=e=>{let n=e.target;n?.contains(h.trigger)&&m()};return window.addEventListener("scroll",handleScroll,{capture:!0}),()=>window.removeEventListener("scroll",handleScroll,{capture:!0})}},[h.trigger,m]),(0,f.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:m,children:(0,f.jsxs)(g.VY,{"data-state":h.stateAttribute,...p,...u,ref:n,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,f.jsx)(E.A4,{children:a}),(0,f.jsx)(_,{scope:r,isInside:!0,children:(0,f.jsx)(P.f,{id:h.contentId,role:"tooltip",children:i||a})})]})})});Q.displayName=B;var H="TooltipArrow";s.forwardRef((e,n)=>{let{__scopeTooltip:r,...a}=e,s=S(r),i=$(H,r);return i.isInside?null:(0,f.jsx)(g.Eh,{...s,...a,ref:n})}).displayName=H;var V=TooltipProvider,Y=Tooltip,G=I,X=Q},9715:function(e,n,r){"use strict";r.d(n,{S:function(){return g}});var a="undefined"==typeof window||"Deno"in globalThis;function noop(){}function resolveStaleTime(e,n){return"function"==typeof e?e(n):e}function matchQuery(e,n){let{type:r="all",exact:a,fetchStatus:s,predicate:i,queryKey:o,stale:l}=e;if(o){if(a){if(n.queryHash!==hashQueryKeyByOptions(o,n.options))return!1}else if(!partialMatchKey(n.queryKey,o))return!1}if("all"!==r){let e=n.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof l||n.isStale()===l)&&(!s||s===n.state.fetchStatus)&&(!i||!!i(n))}function matchMutation(e,n){let{exact:r,status:a,predicate:s,mutationKey:i}=e;if(i){if(!n.options.mutationKey)return!1;if(r){if(hashKey(n.options.mutationKey)!==hashKey(i))return!1}else if(!partialMatchKey(n.options.mutationKey,i))return!1}return(!a||n.state.status===a)&&(!s||!!s(n))}function hashQueryKeyByOptions(e,n){let r=n?.queryKeyHashFn||hashKey;return r(e)}function hashKey(e){return JSON.stringify(e,(e,n)=>isPlainObject(n)?Object.keys(n).sort().reduce((e,r)=>(e[r]=n[r],e),{}):n)}function partialMatchKey(e,n){return e===n||typeof e==typeof n&&!!e&&!!n&&"object"==typeof e&&"object"==typeof n&&!Object.keys(n).some(r=>!partialMatchKey(e[r],n[r]))}function isPlainArray(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function isPlainObject(e){if(!hasObjectPrototype(e))return!1;let n=e.constructor;if(void 0===n)return!0;let r=n.prototype;return!!(hasObjectPrototype(r)&&r.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function hasObjectPrototype(e){return"[object Object]"===Object.prototype.toString.call(e)}function addToEnd(e,n,r=0){let a=[...e,n];return r&&a.length>r?a.slice(1):a}function addToStart(e,n,r=0){let a=[n,...e];return r&&a.length>r?a.slice(0,-1):a}var s=Symbol();function ensureQueryFn(e,n){return!e.queryFn&&n?.initialPromise?()=>n.initialPromise:e.queryFn&&e.queryFn!==s?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var i=function(){let e=[],n=0,notifyFn=e=>{e()},batchNotifyFn=e=>{e()},scheduleFn=e=>setTimeout(e,0),schedule=r=>{n?e.push(r):scheduleFn(()=>{notifyFn(r)})},flush=()=>{let n=e;e=[],n.length&&scheduleFn(()=>{batchNotifyFn(()=>{n.forEach(e=>{notifyFn(e)})})})};return{batch:e=>{let r;n++;try{r=e()}finally{--n||flush()}return r},batchCalls:e=>(...n)=>{schedule(()=>{e(...n)})},schedule,setNotifyFunction:e=>{notifyFn=e},setBatchNotifyFunction:e=>{batchNotifyFn=e},setScheduler:e=>{scheduleFn=e}}}(),o=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},l=new class extends o{#e;#t;#n;constructor(){super(),this.#n=e=>{if(!a&&window.addEventListener){let listener=()=>e();return window.addEventListener("visibilitychange",listener,!1),()=>{window.removeEventListener("visibilitychange",listener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){let n=this.#e!==e;n&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(n=>{n(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},u=new class extends o{#r=!0;#t;#n;constructor(){super(),this.#n=e=>{if(!a&&window.addEventListener){let onlineListener=()=>e(!0),offlineListener=()=>e(!1);return window.addEventListener("online",onlineListener,!1),window.addEventListener("offline",offlineListener,!1),()=>{window.removeEventListener("online",onlineListener),window.removeEventListener("offline",offlineListener)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){let n=this.#r!==e;n&&(this.#r=e,this.listeners.forEach(n=>{n(e)}))}isOnline(){return this.#r}};function defaultRetryDelay(e){return Math.min(1e3*2**e,3e4)}function canFetch(e){return(e??"online")!=="online"||u.isOnline()}var h=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function isCancelledError(e){return e instanceof h}function createRetryer(e){let n,r=!1,s=0,i=!1,o=function(){let e,n;let r=new Promise((r,a)=>{e=r,n=a});function finalize(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=n=>{finalize({status:"fulfilled",value:n}),e(n)},r.reject=e=>{finalize({status:"rejected",reason:e}),n(e)},r}(),canContinue=()=>l.isFocused()&&("always"===e.networkMode||u.isOnline())&&e.canRun(),canStart=()=>canFetch(e.networkMode)&&e.canRun(),resolve=r=>{i||(i=!0,e.onSuccess?.(r),n?.(),o.resolve(r))},reject=r=>{i||(i=!0,e.onError?.(r),n?.(),o.reject(r))},pause=()=>new Promise(r=>{n=e=>{(i||canContinue())&&r(e)},e.onPause?.()}).then(()=>{n=void 0,i||e.onContinue?.()}),run=()=>{let n;if(i)return;let o=0===s?e.initialPromise:void 0;try{n=o??e.fn()}catch(e){n=Promise.reject(e)}Promise.resolve(n).then(resolve).catch(n=>{if(i)return;let o=e.retry??(a?0:3),l=e.retryDelay??defaultRetryDelay,u="function"==typeof l?l(s,n):l,h=!0===o||"number"==typeof o&&s<o||"function"==typeof o&&o(s,n);if(r||!h){reject(n);return}s++,e.onFail?.(s,n),new Promise(e=>{setTimeout(e,u)}).then(()=>canContinue()?void 0:pause()).then(()=>{r?reject(n):run()})})};return{promise:o,cancel:n=>{i||(reject(new h(n)),e.abort?.())},continue:()=>(n?.(),o),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart,start:()=>(canStart()?run():pause().then(run),o)}}var f=class{#a;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#a=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(a?1/0:3e5))}clearGcTimeout(){this.#a&&(clearTimeout(this.#a),this.#a=void 0)}},p=class extends f{#s;#i;#o;#l;#u;#d;constructor(e){super(),this.#d=!1,this.#u=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#o=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#s=function(e){let n="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==n,a=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:n,dataUpdateCount:0,dataUpdatedAt:r?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#s,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#l?.promise}setOptions(e){this.options={...this.#u,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#o.remove(this)}setData(e,n){var r,a;let s=(r=this.state.data,"function"==typeof(a=this.options).structuralSharing?a.structuralSharing(r,e):!1!==a.structuralSharing?function replaceEqualDeep(e,n){if(e===n)return e;let r=isPlainArray(e)&&isPlainArray(n);if(r||isPlainObject(e)&&isPlainObject(n)){let a=r?e:Object.keys(e),s=a.length,i=r?n:Object.keys(n),o=i.length,l=r?[]:{},u=0;for(let s=0;s<o;s++){let o=r?s:i[s];(!r&&a.includes(o)||r)&&void 0===e[o]&&void 0===n[o]?(l[o]=void 0,u++):(l[o]=replaceEqualDeep(e[o],n[o]),l[o]===e[o]&&void 0!==e[o]&&u++)}return s===o&&u===s?e:l}return n}(r,e):e);return this.#c({data:s,type:"success",dataUpdatedAt:n?.updatedAt,manual:n?.manual}),s}setState(e,n){this.#c({type:"setState",state:e,setStateOptions:n})}cancel(e){let n=this.#l?.promise;return this.#l?.cancel(e),n?n.then(noop).catch(noop):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#s)}isActive(){return this.observers.some(e=>{var n;return!1!==("function"==typeof(n=e.options.enabled)?n(this):n)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===s||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#l?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#o.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(n=>n!==e),this.observers.length||(this.#l&&(this.#d?this.#l.cancel({revert:!0}):this.#l.cancelRetry()),this.scheduleGc()),this.#o.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#c({type:"invalidate"})}fetch(e,n){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&n?.cancelRefetch)this.cancel({silent:!0});else if(this.#l)return this.#l.continueRetry(),this.#l.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,r.signal)})},a={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{let e=ensureQueryFn(this.options,n),r={queryKey:this.queryKey,meta:this.meta};return(addSignalProperty(r),this.#d=!1,this.options.persister)?this.options.persister(e,r,this):e(r)}};addSignalProperty(a),this.options.behavior?.onFetch(a,this),this.#i=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==a.fetchOptions?.meta)&&this.#c({type:"fetch",meta:a.fetchOptions?.meta});let onError=e=>{isCancelledError(e)&&e.silent||this.#c({type:"error",error:e}),isCancelledError(e)||(this.#o.config.onError?.(e,this),this.#o.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#l=createRetryer({initialPromise:n?.initialPromise,fn:a.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e){onError(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){onError(e);return}this.#o.config.onSuccess?.(e,this),this.#o.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError,onFail:(e,n)=>{this.#c({type:"failed",failureCount:e,error:n})},onPause:()=>{this.#c({type:"pause"})},onContinue:()=>{this.#c({type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0}),this.#l.start()}#c(e){this.state=(n=>{switch(e.type){case"failed":return{...n,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...n,fetchStatus:"paused"};case"continue":return{...n,fetchStatus:"fetching"};case"fetch":var r;return{...n,...(r=n.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:canFetch(this.options.networkMode)?"fetching":"paused",...void 0===r&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...n,data:e.data,dataUpdateCount:n.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let a=e.error;if(isCancelledError(a)&&a.revert&&this.#i)return{...this.#i,fetchStatus:"idle"};return{...n,error:a,errorUpdateCount:n.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:n.fetchFailureCount+1,fetchFailureReason:a,fetchStatus:"idle",status:"error"};case"invalidate":return{...n,isInvalidated:!0};case"setState":return{...n,...e.state}}})(this.state),i.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#o.notify({query:this,type:"updated",action:e})})}},m=class extends o{constructor(e={}){super(),this.config=e,this.#h=new Map}#h;build(e,n,r){let a=n.queryKey,s=n.queryHash??hashQueryKeyByOptions(a,n),i=this.get(s);return i||(i=new p({cache:this,queryKey:a,queryHash:s,options:e.defaultQueryOptions(n),state:r,defaultOptions:e.getQueryDefaults(a)}),this.add(i)),i}add(e){this.#h.has(e.queryHash)||(this.#h.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let n=this.#h.get(e.queryHash);n&&(e.destroy(),n===e&&this.#h.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){i.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#h.get(e)}getAll(){return[...this.#h.values()]}find(e){let n={exact:!0,...e};return this.getAll().find(e=>matchQuery(n,e))}findAll(e={}){let n=this.getAll();return Object.keys(e).length>0?n.filter(n=>matchQuery(e,n)):n}notify(e){i.batch(()=>{this.listeners.forEach(n=>{n(e)})})}onFocus(){i.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){i.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},y=class extends f{#f;#p;#l;constructor(e){super(),this.mutationId=e.mutationId,this.#p=e.mutationCache,this.#f=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#f.includes(e)||(this.#f.push(e),this.clearGcTimeout(),this.#p.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#f=this.#f.filter(n=>n!==e),this.scheduleGc(),this.#p.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#f.length||("pending"===this.state.status?this.scheduleGc():this.#p.remove(this))}continue(){return this.#l?.continue()??this.execute(this.state.variables)}async execute(e){this.#l=createRetryer({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,n)=>{this.#c({type:"failed",failureCount:e,error:n})},onPause:()=>{this.#c({type:"pause"})},onContinue:()=>{this.#c({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#p.canRun(this)});let n="pending"===this.state.status,r=!this.#l.canStart();try{if(!n){this.#c({type:"pending",variables:e,isPaused:r}),await this.#p.config.onMutate?.(e,this);let n=await this.options.onMutate?.(e);n!==this.state.context&&this.#c({type:"pending",context:n,variables:e,isPaused:r})}let a=await this.#l.start();return await this.#p.config.onSuccess?.(a,e,this.state.context,this),await this.options.onSuccess?.(a,e,this.state.context),await this.#p.config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,e,this.state.context),this.#c({type:"success",data:a}),a}catch(n){try{throw await this.#p.config.onError?.(n,e,this.state.context,this),await this.options.onError?.(n,e,this.state.context),await this.#p.config.onSettled?.(void 0,n,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,n,e,this.state.context),n}finally{this.#c({type:"error",error:n})}}finally{this.#p.runNext(this)}}#c(e){this.state=(n=>{switch(e.type){case"failed":return{...n,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...n,isPaused:!0};case"continue":return{...n,isPaused:!1};case"pending":return{...n,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...n,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...n,data:void 0,error:e.error,failureCount:n.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),i.batch(()=>{this.#f.forEach(n=>{n.onMutationUpdate(e)}),this.#p.notify({mutation:this,type:"updated",action:e})})}},v=class extends o{constructor(e={}){super(),this.config=e,this.#m=new Map,this.#y=Date.now()}#m;#y;build(e,n,r){let a=new y({mutationCache:this,mutationId:++this.#y,options:e.defaultMutationOptions(n),state:r});return this.add(a),a}add(e){let n=scopeFor(e),r=this.#m.get(n)??[];r.push(e),this.#m.set(n,r),this.notify({type:"added",mutation:e})}remove(e){let n=scopeFor(e);if(this.#m.has(n)){let r=this.#m.get(n)?.filter(n=>n!==e);r&&(0===r.length?this.#m.delete(n):this.#m.set(n,r))}this.notify({type:"removed",mutation:e})}canRun(e){let n=this.#m.get(scopeFor(e))?.find(e=>"pending"===e.state.status);return!n||n===e}runNext(e){let n=this.#m.get(scopeFor(e))?.find(n=>n!==e&&n.state.isPaused);return n?.continue()??Promise.resolve()}clear(){i.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}getAll(){return[...this.#m.values()].flat()}find(e){let n={exact:!0,...e};return this.getAll().find(e=>matchMutation(n,e))}findAll(e={}){return this.getAll().filter(n=>matchMutation(e,n))}notify(e){i.batch(()=>{this.listeners.forEach(n=>{n(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return i.batch(()=>Promise.all(e.map(e=>e.continue().catch(noop))))}};function scopeFor(e){return e.options.scope?.id??String(e.mutationId)}function infiniteQueryBehavior(e){return{onFetch:(n,r)=>{let a=n.options,s=n.fetchOptions?.meta?.fetchMore?.direction,i=n.state.data?.pages||[],o=n.state.data?.pageParams||[],l={pages:[],pageParams:[]},u=0,fetchFn=async()=>{let r=!1,addSignalProperty=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(n.signal.aborted?r=!0:n.signal.addEventListener("abort",()=>{r=!0}),n.signal)})},h=ensureQueryFn(n.options,n.fetchOptions),fetchPage=async(e,a,s)=>{if(r)return Promise.reject();if(null==a&&e.pages.length)return Promise.resolve(e);let i={queryKey:n.queryKey,pageParam:a,direction:s?"backward":"forward",meta:n.options.meta};addSignalProperty(i);let o=await h(i),{maxPages:l}=n.options,u=s?addToStart:addToEnd;return{pages:u(e.pages,o,l),pageParams:u(e.pageParams,a,l)}};if(s&&i.length){let e="backward"===s,n=e?getPreviousPageParam:getNextPageParam,r={pages:i,pageParams:o},u=n(a,r);l=await fetchPage(r,u,e)}else{let n=e??i.length;do{let e=0===u?o[0]??a.initialPageParam:getNextPageParam(a,l);if(u>0&&null==e)break;l=await fetchPage(l,e),u++}while(u<n)}return l};n.options.persister?n.fetchFn=()=>n.options.persister?.(fetchFn,{queryKey:n.queryKey,meta:n.options.meta,signal:n.signal},r):n.fetchFn=fetchFn}}}function getNextPageParam(e,{pages:n,pageParams:r}){let a=n.length-1;return n.length>0?e.getNextPageParam(n[a],n,r[a],r):void 0}function getPreviousPageParam(e,{pages:n,pageParams:r}){return n.length>0?e.getPreviousPageParam?.(n[0],n,r[0],r):void 0}var g=class{#v;#p;#u;#g;#b;#w;#x;#E;constructor(e={}){this.#v=e.queryCache||new m,this.#p=e.mutationCache||new v,this.#u=e.defaultOptions||{},this.#g=new Map,this.#b=new Map,this.#w=0}mount(){this.#w++,1===this.#w&&(this.#x=l.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#v.onFocus())}),this.#E=u.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#v.onOnline())}))}unmount(){this.#w--,0===this.#w&&(this.#x?.(),this.#x=void 0,this.#E?.(),this.#E=void 0)}isFetching(e){return this.#v.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#p.findAll({...e,status:"pending"}).length}getQueryData(e){let n=this.defaultQueryOptions({queryKey:e});return this.#v.get(n.queryHash)?.state.data}ensureQueryData(e){let n=this.getQueryData(e.queryKey);if(void 0===n)return this.fetchQuery(e);{let r=this.defaultQueryOptions(e),a=this.#v.build(this,r);return e.revalidateIfStale&&a.isStaleByTime(resolveStaleTime(r.staleTime,a))&&this.prefetchQuery(r),Promise.resolve(n)}}getQueriesData(e){return this.#v.findAll(e).map(({queryKey:e,state:n})=>{let r=n.data;return[e,r]})}setQueryData(e,n,r){let a=this.defaultQueryOptions({queryKey:e}),s=this.#v.get(a.queryHash),i=s?.state.data,o="function"==typeof n?n(i):n;if(void 0!==o)return this.#v.build(this,a).setData(o,{...r,manual:!0})}setQueriesData(e,n,r){return i.batch(()=>this.#v.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,n,r)]))}getQueryState(e){let n=this.defaultQueryOptions({queryKey:e});return this.#v.get(n.queryHash)?.state}removeQueries(e){let n=this.#v;i.batch(()=>{n.findAll(e).forEach(e=>{n.remove(e)})})}resetQueries(e,n){let r=this.#v,a={type:"active",...e};return i.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries(a,n)))}cancelQueries(e={},n={}){let r={revert:!0,...n},a=i.batch(()=>this.#v.findAll(e).map(e=>e.cancel(r)));return Promise.all(a).then(noop).catch(noop)}invalidateQueries(e={},n={}){return i.batch(()=>{if(this.#v.findAll(e).forEach(e=>{e.invalidate()}),"none"===e.refetchType)return Promise.resolve();let r={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(r,n)})}refetchQueries(e={},n){let r={...n,cancelRefetch:n?.cancelRefetch??!0},a=i.batch(()=>this.#v.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let n=e.fetch(void 0,r);return r.throwOnError||(n=n.catch(noop)),"paused"===e.state.fetchStatus?Promise.resolve():n}));return Promise.all(a).then(noop)}fetchQuery(e){let n=this.defaultQueryOptions(e);void 0===n.retry&&(n.retry=!1);let r=this.#v.build(this,n);return r.isStaleByTime(resolveStaleTime(n.staleTime,r))?r.fetch(n):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(noop).catch(noop)}fetchInfiniteQuery(e){return e.behavior=infiniteQueryBehavior(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(noop).catch(noop)}ensureInfiniteQueryData(e){return e.behavior=infiniteQueryBehavior(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return u.isOnline()?this.#p.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#v}getMutationCache(){return this.#p}getDefaultOptions(){return this.#u}setDefaultOptions(e){this.#u=e}setQueryDefaults(e,n){this.#g.set(hashKey(e),{queryKey:e,defaultOptions:n})}getQueryDefaults(e){let n=[...this.#g.values()],r={};return n.forEach(n=>{partialMatchKey(e,n.queryKey)&&(r={...r,...n.defaultOptions})}),r}setMutationDefaults(e,n){this.#b.set(hashKey(e),{mutationKey:e,defaultOptions:n})}getMutationDefaults(e){let n=[...this.#b.values()],r={};return n.forEach(n=>{partialMatchKey(e,n.mutationKey)&&(r={...r,...n.defaultOptions})}),r}defaultQueryOptions(e){if(e._defaulted)return e;let n={...this.#u.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return n.queryHash||(n.queryHash=hashQueryKeyByOptions(n.queryKey,n)),void 0===n.refetchOnReconnect&&(n.refetchOnReconnect="always"!==n.networkMode),void 0===n.throwOnError&&(n.throwOnError=!!n.suspense),!n.networkMode&&n.persister&&(n.networkMode="offlineFirst"),!0!==n.enabled&&n.queryFn===s&&(n.enabled=!1),n}defaultMutationOptions(e){return e?._defaulted?e:{...this.#u.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#v.clear(),this.#p.clear()}}},8038:function(e,n,r){"use strict";r.d(n,{aH:function(){return QueryClientProvider}});var a=r(2265),s=r(7437),i=a.createContext(void 0),QueryClientProvider=({client:e,children:n})=>(a.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,s.jsx)(i.Provider,{value:e,children:n}))},1350:function(e,n,r){"use strict";r.d(n,{F:function(){return j}});var a=r(2265),s=["light","dark"],i=a.createContext(void 0),o={setTheme:e=>{},themes:[]},j=()=>{var e;return null!=(e=a.useContext(i))?e:o};a.memo(({forcedTheme:e,storageKey:n,attribute:r,enableSystem:i,enableColorScheme:o,defaultTheme:l,value:u,attrs:h,nonce:f})=>{let p="system"===l,m="class"===r?`var d=document.documentElement,c=d.classList;c.remove(${h.map(e=>`'${e}'`).join(",")});`:`var d=document.documentElement,n='${r}',s='setAttribute';`,y=o?(s.includes(l)?l:null)?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${l}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",d=(e,n=!1,a=!0)=>{let i=u?u[e]:e,l=n?e+"|| ''":`'${i}'`,h="";return o&&a&&!n&&s.includes(e)&&(h+=`d.style.colorScheme = '${e}';`),"class"===r?n||i?h+=`c.add(${l})`:h+="null":i&&(h+=`d[s](n,${l})`),h},v=e?`!function(){${m}${d(e)}}()`:i?`!function(){try{${m}var e=localStorage.getItem('${n}');if('system'===e||(!e&&${p})){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){${d("dark")}}else{${d("light")}}}else if(e){${u?`var x=${JSON.stringify(u)};`:""}${d(u?"x[e]":"e",!0)}}${p?"":"else{"+d(l,!1,!1)+"}"}${y}}catch(e){}}()`:`!function(){try{${m}var e=localStorage.getItem('${n}');if(e){${u?`var x=${JSON.stringify(u)};`:""}${d(u?"x[e]":"e",!0)}}else{${d(l,!1,!1)};}${y}}catch(t){}}();`;return a.createElement("script",{nonce:f,dangerouslySetInnerHTML:{__html:v}})})},1424:function(e,n,r){"use strict";r.d(n,{x7:function(){return Te}});var a=r(2265),s=r(4887),Ct=e=>{switch(e){case"success":return o;case"info":return u;case"warning":return l;case"error":return h;default:return null}},i=Array(12).fill(0),It=({visible:e})=>a.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},a.createElement("div",{className:"sonner-spinner"},i.map((e,n)=>a.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),o=a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},a.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),l=a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},a.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),u=a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},a.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),h=a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},a.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),Dt=()=>{let[e,n]=a.useState(document.hidden);return a.useEffect(()=>{let t=()=>{n(document.hidden)};return document.addEventListener("visibilitychange",t),()=>window.removeEventListener("visibilitychange",t)},[]),e},f=1,p=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let n=this.subscribers.indexOf(e);this.subscribers.splice(n,1)}),this.publish=e=>{this.subscribers.forEach(n=>n(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var n;let{message:r,...a}=e,s="number"==typeof(null==e?void 0:e.id)||(null==(n=e.id)?void 0:n.length)>0?e.id:f++,i=this.toasts.find(e=>e.id===s),o=void 0===e.dismissible||e.dismissible;return i?this.toasts=this.toasts.map(n=>n.id===s?(this.publish({...n,...e,id:s,title:r}),{...n,...e,id:s,dismissible:o,title:r}):n):this.addToast({title:r,...a,dismissible:o,id:s}),s},this.dismiss=e=>(e||this.toasts.forEach(e=>{this.subscribers.forEach(n=>n({id:e.id,dismiss:!0}))}),this.subscribers.forEach(n=>n({id:e,dismiss:!0})),e),this.message=(e,n)=>this.create({...n,message:e}),this.error=(e,n)=>this.create({...n,message:e,type:"error"}),this.success=(e,n)=>this.create({...n,type:"success",message:e}),this.info=(e,n)=>this.create({...n,type:"info",message:e}),this.warning=(e,n)=>this.create({...n,type:"warning",message:e}),this.loading=(e,n)=>this.create({...n,type:"loading",message:e}),this.promise=(e,n)=>{let r;if(!n)return;void 0!==n.loading&&(r=this.create({...n,promise:e,type:"loading",message:n.loading,description:"function"!=typeof n.description?n.description:void 0}));let a=e instanceof Promise?e:e(),s=void 0!==r;return a.then(async e=>{if(Ot(e)&&!e.ok){s=!1;let a="function"==typeof n.error?await n.error(`HTTP error! status: ${e.status}`):n.error,i="function"==typeof n.description?await n.description(`HTTP error! status: ${e.status}`):n.description;this.create({id:r,type:"error",message:a,description:i})}else if(void 0!==n.success){s=!1;let a="function"==typeof n.success?await n.success(e):n.success,i="function"==typeof n.description?await n.description(e):n.description;this.create({id:r,type:"success",message:a,description:i})}}).catch(async e=>{if(void 0!==n.error){s=!1;let a="function"==typeof n.error?await n.error(e):n.error,i="function"==typeof n.description?await n.description(e):n.description;this.create({id:r,type:"error",message:a,description:i})}}).finally(()=>{var e;s&&(this.dismiss(r),r=void 0),null==(e=n.finally)||e.call(n)}),r},this.custom=(e,n)=>{let r=(null==n?void 0:n.id)||f++;return this.create({jsx:e(r),id:r,...n}),r},this.subscribers=[],this.toasts=[]}},Ot=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status;function U(e){return void 0!==e.label}function ne(...e){return e.filter(Boolean).join(" ")}Object.assign((e,n)=>{let r=(null==n?void 0:n.id)||f++;return p.addToast({title:e,...n,id:r}),r},{success:p.success,info:p.info,warning:p.warning,error:p.error,custom:p.custom,message:p.message,promise:p.promise,dismiss:p.dismiss,loading:p.loading},{getHistory:()=>p.toasts}),function(e,{insertAt:n}={}){if(!e||"undefined"==typeof document)return;let r=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css","top"===n&&r.firstChild?r.insertBefore(a,r.firstChild):r.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);var se=e=>{var n,r,s,i,o,l,u,h,f,p;let{invert:m,toast:y,unstyled:v,interacting:g,setHeights:b,visibleToasts:w,heights:x,index:E,toasts:C,expanded:P,removeToast:T,defaultRichColors:D,closeButton:S,style:O,cancelButtonStyle:R,actionButtonStyle:M,className:F="",descriptionClassName:k="",duration:L,position:N,gap:A,loadingIcon:I,expandByDefault:q,classNames:K,icons:B,closeButtonAriaLabel:Q="Close toast",pauseWhenPageIsHidden:W,cn:_}=e,[$,z]=a.useState(!1),[H,V]=a.useState(!1),[Y,G]=a.useState(!1),[X,J]=a.useState(!1),[Z,ee]=a.useState(0),[et,en]=a.useState(0),er=a.useRef(null),ea=a.useRef(null),es=y.type,ei=!1!==y.dismissible,eo=y.className||"",el=y.descriptionClassName||"",eu=a.useMemo(()=>x.findIndex(e=>e.toastId===y.id)||0,[x,y.id]),ed=a.useMemo(()=>{var e;return null!=(e=y.closeButton)?e:S},[y.closeButton,S]),ec=a.useMemo(()=>y.duration||L||4e3,[y.duration,L]),eh=a.useRef(0),ef=a.useRef(0),ep=a.useRef(0),em=a.useRef(null),[ey,ev]=N.split("-"),eg=a.useMemo(()=>x.reduce((e,n,r)=>r>=eu?e:e+n.height,0),[x,eu]),eb=Dt(),ew=y.invert||m,ex="loading"===es;ef.current=a.useMemo(()=>eu*A+eg,[eu,eg]),a.useEffect(()=>{z(!0)},[]),a.useLayoutEffect(()=>{if(!$)return;let e=ea.current,n=e.style.height;e.style.height="auto";let r=e.getBoundingClientRect().height;e.style.height=n,en(r),b(e=>e.find(e=>e.toastId===y.id)?e.map(e=>e.toastId===y.id?{...e,height:r}:e):[{toastId:y.id,height:r,position:y.position},...e])},[$,y.title,y.description,b,y.id]);let eE=a.useCallback(()=>{V(!0),ee(ef.current),b(e=>e.filter(e=>e.toastId!==y.id)),setTimeout(()=>{T(y)},200)},[y,T,b,ef]);return a.useEffect(()=>{if(y.promise&&"loading"===es||y.duration===1/0||"loading"===y.type)return;let e,n=ec;return P||g||W&&eb?(()=>{if(ep.current<eh.current){let e=new Date().getTime()-eh.current;n-=e}ep.current=new Date().getTime()})():n!==1/0&&(eh.current=new Date().getTime(),e=setTimeout(()=>{var e;null==(e=y.onAutoClose)||e.call(y,y),eE()},n)),()=>clearTimeout(e)},[P,g,q,y,ec,eE,y.promise,es,W,eb]),a.useEffect(()=>{let e=ea.current;if(e){let n=e.getBoundingClientRect().height;return en(n),b(e=>[{toastId:y.id,height:n,position:y.position},...e]),()=>b(e=>e.filter(e=>e.toastId!==y.id))}},[b,y.id]),a.useEffect(()=>{y.delete&&eE()},[eE,y.delete]),a.createElement("li",{"aria-live":y.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:ea,className:_(F,eo,null==K?void 0:K.toast,null==(n=null==y?void 0:y.classNames)?void 0:n.toast,null==K?void 0:K.default,null==K?void 0:K[es],null==(r=null==y?void 0:y.classNames)?void 0:r[es]),"data-sonner-toast":"","data-rich-colors":null!=(s=y.richColors)?s:D,"data-styled":!(y.jsx||y.unstyled||v),"data-mounted":$,"data-promise":!!y.promise,"data-removed":H,"data-visible":E+1<=w,"data-y-position":ey,"data-x-position":ev,"data-index":E,"data-front":0===E,"data-swiping":Y,"data-dismissible":ei,"data-type":es,"data-invert":ew,"data-swipe-out":X,"data-expanded":!!(P||q&&$),style:{"--index":E,"--toasts-before":E,"--z-index":C.length-E,"--offset":`${H?Z:ef.current}px`,"--initial-height":q?"auto":`${et}px`,...O,...y.style},onPointerDown:e=>{ex||!ei||(er.current=new Date,ee(ef.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(G(!0),em.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,n,r,a;if(X||!ei)return;em.current=null;let s=Number((null==(e=ea.current)?void 0:e.style.getPropertyValue("--swipe-amount").replace("px",""))||0),i=new Date().getTime()-(null==(n=er.current)?void 0:n.getTime());if(Math.abs(s)>=20||Math.abs(s)/i>.11){ee(ef.current),null==(r=y.onDismiss)||r.call(y,y),eE(),J(!0);return}null==(a=ea.current)||a.style.setProperty("--swipe-amount","0px"),G(!1)},onPointerMove:e=>{var n;if(!em.current||!ei)return;let r=e.clientY-em.current.y,a=e.clientX-em.current.x,s=("top"===ey?Math.min:Math.max)(0,r),i="touch"===e.pointerType?10:2;Math.abs(s)>i?null==(n=ea.current)||n.style.setProperty("--swipe-amount",`${r}px`):Math.abs(a)>i&&(em.current=null)}},ed&&!y.jsx?a.createElement("button",{"aria-label":Q,"data-disabled":ex,"data-close-button":!0,onClick:ex||!ei?()=>{}:()=>{var e;eE(),null==(e=y.onDismiss)||e.call(y,y)},className:_(null==K?void 0:K.closeButton,null==(i=null==y?void 0:y.classNames)?void 0:i.closeButton)},a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},a.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),a.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,y.jsx||a.isValidElement(y.title)?y.jsx||y.title:a.createElement(a.Fragment,null,es||y.icon||y.promise?a.createElement("div",{"data-icon":"",className:_(null==K?void 0:K.icon,null==(o=null==y?void 0:y.classNames)?void 0:o.icon)},y.promise||"loading"===y.type&&!y.icon?y.icon||(null!=B&&B.loading?a.createElement("div",{className:"sonner-loader","data-visible":"loading"===es},B.loading):I?a.createElement("div",{className:"sonner-loader","data-visible":"loading"===es},I):a.createElement(It,{visible:"loading"===es})):null,"loading"!==y.type?y.icon||(null==B?void 0:B[es])||Ct(es):null):null,a.createElement("div",{"data-content":"",className:_(null==K?void 0:K.content,null==(l=null==y?void 0:y.classNames)?void 0:l.content)},a.createElement("div",{"data-title":"",className:_(null==K?void 0:K.title,null==(u=null==y?void 0:y.classNames)?void 0:u.title)},y.title),y.description?a.createElement("div",{"data-description":"",className:_(k,el,null==K?void 0:K.description,null==(h=null==y?void 0:y.classNames)?void 0:h.description)},y.description):null),a.isValidElement(y.cancel)?y.cancel:y.cancel&&U(y.cancel)?a.createElement("button",{"data-button":!0,"data-cancel":!0,style:y.cancelButtonStyle||R,onClick:e=>{var n,r;U(y.cancel)&&ei&&(null==(r=(n=y.cancel).onClick)||r.call(n,e),eE())},className:_(null==K?void 0:K.cancelButton,null==(f=null==y?void 0:y.classNames)?void 0:f.cancelButton)},y.cancel.label):null,a.isValidElement(y.action)?y.action:y.action&&U(y.action)?a.createElement("button",{"data-button":!0,"data-action":!0,style:y.actionButtonStyle||M,onClick:e=>{var n,r;U(y.action)&&(e.defaultPrevented||(null==(r=(n=y.action).onClick)||r.call(n,e),eE()))},className:_(null==K?void 0:K.actionButton,null==(p=null==y?void 0:y.classNames)?void 0:p.actionButton)},y.action.label):null))};function Ht(){if("undefined"==typeof window||"undefined"==typeof document)return"ltr";let e=document.documentElement.getAttribute("dir");return"auto"!==e&&e?e:window.getComputedStyle(document.documentElement).direction}var Te=e=>{let{invert:n,position:r="bottom-right",hotkey:i=["altKey","KeyT"],expand:o,closeButton:l,className:u,offset:h,theme:f="light",richColors:m,duration:y,style:v,visibleToasts:g=3,toastOptions:b,dir:w=Ht(),gap:x=14,loadingIcon:E,icons:C,containerAriaLabel:P="Notifications",pauseWhenPageIsHidden:T,cn:D=ne}=e,[S,O]=a.useState([]),R=a.useMemo(()=>Array.from(new Set([r].concat(S.filter(e=>e.position).map(e=>e.position)))),[S,r]),[M,F]=a.useState([]),[k,L]=a.useState(!1),[N,A]=a.useState(!1),[I,q]=a.useState("system"!==f?f:"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),K=a.useRef(null),B=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),Q=a.useRef(null),W=a.useRef(!1),_=a.useCallback(e=>{var n;null!=(n=S.find(n=>n.id===e.id))&&n.delete||p.dismiss(e.id),O(n=>n.filter(({id:n})=>n!==e.id))},[S]);return a.useEffect(()=>p.subscribe(e=>{if(e.dismiss){O(n=>n.map(n=>n.id===e.id?{...n,delete:!0}:n));return}setTimeout(()=>{s.flushSync(()=>{O(n=>{let r=n.findIndex(n=>n.id===e.id);return -1!==r?[...n.slice(0,r),{...n[r],...e},...n.slice(r+1)]:[e,...n]})})})}),[]),a.useEffect(()=>{if("system"!==f){q(f);return}"system"===f&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?q("dark"):q("light")),"undefined"!=typeof window&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:e})=>{q(e?"dark":"light")})},[f]),a.useEffect(()=>{S.length<=1&&L(!1)},[S]),a.useEffect(()=>{let c=e=>{var n,r;i.every(n=>e[n]||e.code===n)&&(L(!0),null==(n=K.current)||n.focus()),"Escape"===e.code&&(document.activeElement===K.current||null!=(r=K.current)&&r.contains(document.activeElement))&&L(!1)};return document.addEventListener("keydown",c),()=>document.removeEventListener("keydown",c)},[i]),a.useEffect(()=>{if(K.current)return()=>{Q.current&&(Q.current.focus({preventScroll:!0}),Q.current=null,W.current=!1)}},[K.current]),S.length?a.createElement("section",{"aria-label":`${P} ${B}`,tabIndex:-1},R.map((e,r)=>{var s;let[i,f]=e.split("-");return a.createElement("ol",{key:e,dir:"auto"===w?Ht():w,tabIndex:-1,ref:K,className:u,"data-sonner-toaster":!0,"data-theme":I,"data-y-position":i,"data-x-position":f,style:{"--front-toast-height":`${(null==(s=M[0])?void 0:s.height)||0}px`,"--offset":"number"==typeof h?`${h}px`:h||"32px","--width":"356px","--gap":`${x}px`,...v},onBlur:e=>{W.current&&!e.currentTarget.contains(e.relatedTarget)&&(W.current=!1,Q.current&&(Q.current.focus({preventScroll:!0}),Q.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||W.current||(W.current=!0,Q.current=e.relatedTarget)},onMouseEnter:()=>L(!0),onMouseMove:()=>L(!0),onMouseLeave:()=>{N||L(!1)},onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||A(!0)},onPointerUp:()=>A(!1)},S.filter(n=>!n.position&&0===r||n.position===e).map((r,s)=>{var i,u;return a.createElement(se,{key:r.id,icons:C,index:s,toast:r,defaultRichColors:m,duration:null!=(i=null==b?void 0:b.duration)?i:y,className:null==b?void 0:b.className,descriptionClassName:null==b?void 0:b.descriptionClassName,invert:n,visibleToasts:g,closeButton:null!=(u=null==b?void 0:b.closeButton)?u:l,interacting:N,position:e,style:null==b?void 0:b.style,unstyled:null==b?void 0:b.unstyled,classNames:null==b?void 0:b.classNames,cancelButtonStyle:null==b?void 0:b.cancelButtonStyle,actionButtonStyle:null==b?void 0:b.actionButtonStyle,removeToast:_,toasts:S.filter(e=>e.position==r.position),heights:M.filter(e=>e.position==r.position),setHeights:F,expandByDefault:o,gap:x,loadingIcon:E,expanded:k,pauseWhenPageIsHidden:T,cn:D})}))})):null}}}]);