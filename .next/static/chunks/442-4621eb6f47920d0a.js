"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[442],{5859:function(e,t,n){n.d(t,{Ry:function(){return hideOthers}});var o=new WeakMap,i=new WeakMap,l={},a=0,unwrapHost=function(e){return e&&(e.host||unwrapHost(e.parentNode))},applyAttributeToOthers=function(e,t,n,s){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=unwrapHost(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});l[n]||(l[n]=new WeakMap);var c=l[n],d=[],f=new Set,p=new Set(u),keep=function(e){!e||f.has(e)||(f.add(e),keep(e.parentNode))};u.forEach(keep);var deep=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))deep(e);else try{var t=e.getAttribute(s),l=null!==t&&"false"!==t,a=(o.get(e)||0)+1,u=(c.get(e)||0)+1;o.set(e,a),c.set(e,u),d.push(e),1===a&&l&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),l||e.setAttribute(s,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return deep(t),f.clear(),a++,function(){d.forEach(function(e){var t=o.get(e)-1,l=c.get(e)-1;o.set(e,t),c.set(e,l),t||(i.has(e)||e.removeAttribute(s),i.delete(e)),l||e.removeAttribute(n)}),--a||(o=new WeakMap,o=new WeakMap,i=new WeakMap,l={})}},hideOthers=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live]"))),applyAttributeToOthers(o,i,n,"aria-hidden")):function(){return null}}},5531:function(e,t,n){n.d(t,{Z:function(){return createLucideIcon}});var o=n(2265);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let toKebabCase=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),mergeClasses=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,o.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:l,className:a="",children:s,iconNode:u,...c},d)=>(0,o.createElement)("svg",{ref:d,...i,width:t,height:t,stroke:e,strokeWidth:l?24*Number(n)/Number(t):n,className:mergeClasses("lucide",a),...c},[...u.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(s)?s:[s]])),createLucideIcon=(e,t)=>{let n=(0,o.forwardRef)(({className:n,...i},a)=>(0,o.createElement)(l,{ref:a,iconNode:t,className:mergeClasses(`lucide-${toKebabCase(e)}`,n),...i}));return n.displayName=`${e}`,n}},2442:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},3523:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},9224:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},6141:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7810:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2176:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},2741:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},7972:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2549:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(5531);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,o.Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},622:function(e,t,n){/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var o=n(2265),i=Symbol.for("react.element"),l=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function q(e,t,n){var o,l={},c=null,d=null;for(o in void 0!==n&&(c=""+n),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(d=t.ref),t)a.call(t,o)&&!u.hasOwnProperty(o)&&(l[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===l[o]&&(l[o]=t[o]);return{$$typeof:i,type:e,key:c,ref:d,props:l,_owner:s.current}}t.Fragment=l,t.jsx=q,t.jsxs=q},7437:function(e,t,n){e.exports=n(622)},5322:function(e,t,n){n.d(t,{Av:function(){return a},pF:function(){return o},xv:function(){return l},zi:function(){return i}});var o="right-scroll-bar-position",i="width-before-scroll-bar",l="with-scroll-bars-hidden",a="--removed-body-scroll-bar-size"},2776:function(e,t,n){n.d(t,{jp:function(){return RemoveScrollBar}});var o=n(2265),i=n(8662),l=n(5322),a={left:0,top:0,right:0,gap:0},parse=function(e){return parseInt(e||"",10)||0},getOffset=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],o=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[parse(n),parse(o),parse(i)]},getGapWidth=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return a;var t=getOffset(e),n=document.documentElement.clientWidth,o=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,o-n+t[2]-t[0])}},s=(0,i.Ws)(),u="data-scroll-locked",getStyles=function(e,t,n,o){var i=e.left,a=e.top,s=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat(l.xv," {\n   overflow: hidden ").concat(o,";\n   padding-right: ").concat(c,"px ").concat(o,";\n  }\n  body[").concat(u,"] {\n    overflow: hidden ").concat(o,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(o,";"),"margin"===n&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(s,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(o,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(o,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l.pF," {\n    right: ").concat(c,"px ").concat(o,";\n  }\n  \n  .").concat(l.zi," {\n    margin-right: ").concat(c,"px ").concat(o,";\n  }\n  \n  .").concat(l.pF," .").concat(l.pF," {\n    right: 0 ").concat(o,";\n  }\n  \n  .").concat(l.zi," .").concat(l.zi," {\n    margin-right: 0 ").concat(o,";\n  }\n  \n  body[").concat(u,"] {\n    ").concat(l.Av,": ").concat(c,"px;\n  }\n")},getCurrentUseCounter=function(){var e=parseInt(document.body.getAttribute(u)||"0",10);return isFinite(e)?e:0},useLockAttribute=function(){o.useEffect(function(){return document.body.setAttribute(u,(getCurrentUseCounter()+1).toString()),function(){var e=getCurrentUseCounter()-1;e<=0?document.body.removeAttribute(u):document.body.setAttribute(u,e.toString())}},[])},RemoveScrollBar=function(e){var t=e.noRelative,n=e.noImportant,i=e.gapMode,l=void 0===i?"margin":i;useLockAttribute();var a=o.useMemo(function(){return getGapWidth(l)},[l]);return o.createElement(s,{styles:getStyles(a,!t,l,n?"":"!important")})}},8662:function(e,t,n){n.d(t,{Ws:function(){return styleSingleton}});var o,i=n(2265),stylesheetSingleton=function(){var e=0,t=null;return{add:function(i){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var l,a;(l=t).styleSheet?l.styleSheet.cssText=i:l.appendChild(document.createTextNode(i)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},styleHookSingleton=function(){var e=stylesheetSingleton();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},styleSingleton=function(){var e=styleHookSingleton();return function(t){return e(t.styles,t.dynamic),null}}},5835:function(e,t,n){n.d(t,{q:function(){return useMergeRefs}});var o=n(2265);function assignRef(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var i="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,l=new WeakMap;function useMergeRefs(e,t){var n,a,s,u=(n=t||null,a=function(t){return e.forEach(function(e){return assignRef(e,t)})},(s=(0,o.useState)(function(){return{value:n,callback:a,facade:{get current(){return s.value},set current(value){var e=s.value;e!==value&&(s.value=value,s.callback(value,e))}}}})[0]).callback=a,s.facade);return i(function(){var t=l.get(u);if(t){var n=new Set(t),o=new Set(e),i=u.current;n.forEach(function(e){o.has(e)||assignRef(e,null)}),o.forEach(function(e){n.has(e)||assignRef(e,i)})}l.set(u,e)},[e]),u}},6898:function(e,t,n){n.d(t,{L:function(){return exportSidecar}});var o=n(44),i=n(2265),SideCar=function(e){var t=e.sideCar,n=(0,o._T)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var l=t.read();if(!l)throw Error("Sidecar medium not found");return i.createElement(l,(0,o.pi)({},n))};function exportSidecar(e,t){return e.useMedium(t),SideCar}SideCar.isSideCarExport=!0},8427:function(e,t,n){n.d(t,{_:function(){return createSidecarMedium}});var o=n(44);function ItoI(e){return e}function createSidecarMedium(e){void 0===e&&(e={});var t,n,i,l=(void 0===t&&(t=ItoI),n=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,i);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(i=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){i=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var executeQueue=function(){var n=t;t=[],n.forEach(e)},cycle=function(){return Promise.resolve().then(executeQueue)};cycle(),n={push:function(e){t.push(e),cycle()},filter:function(e){return t=t.filter(e),n}}}});return l.options=(0,o.pi)({async:!0,ssr:!1},e),l}},5744:function(e,t,n){n.d(t,{M:function(){return composeEventHandlers}});function composeEventHandlers(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),!1===n||!o.defaultPrevented)return t?.(o)}}},3651:function(e,t,n){n.d(t,{B:function(){return createCollection}});var o=n(2265),i=n(7437),l=n(2210),a=n(7256);function createCollection(e){let t=e+"CollectionProvider",[n,s]=function(e,t=[]){let n=[],createScope=()=>{let t=n.map(e=>o.createContext(e));return function(n){let i=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return createScope.scopeName=e,[function(t,l){let a=o.createContext(l),s=n.length;function Provider(t){let{scope:n,children:l,...u}=t,c=n?.[e][s]||a,d=o.useMemo(()=>u,Object.values(u));return(0,i.jsx)(c.Provider,{value:d,children:l})}return n=[...n,l],Provider.displayName=t+"Provider",[Provider,function(n,i){let u=i?.[e][s]||a,c=o.useContext(u);if(c)return c;if(void 0!==l)return l;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let createScope=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:o})=>{let i=n(e),l=i[`__scope${o}`];return{...t,...l}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return createScope.scopeName=t.scopeName,createScope}(createScope,...t)]}(t),[u,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),CollectionProvider=e=>{let{scope:t,children:n}=e,l=o.useRef(null),a=o.useRef(new Map).current;return(0,i.jsx)(u,{scope:t,itemMap:a,collectionRef:l,children:n})};CollectionProvider.displayName=t;let d=e+"CollectionSlot",f=o.forwardRef((e,t)=>{let{scope:n,children:o}=e,s=c(d,n),u=(0,l.e)(t,s.collectionRef);return(0,i.jsx)(a.g7,{ref:u,children:o})});f.displayName=d;let p=e+"CollectionItemSlot",m="data-radix-collection-item",g=o.forwardRef((e,t)=>{let{scope:n,children:s,...u}=e,d=o.useRef(null),f=(0,l.e)(t,d),g=c(p,n);return o.useEffect(()=>(g.itemMap.set(d,{ref:d,...u}),()=>void g.itemMap.delete(d))),(0,i.jsx)(a.g7,{[m]:"",ref:f,children:s})});return g.displayName=p,[{Provider:CollectionProvider,Slot:f,ItemSlot:g},function(t){let n=c(e+"CollectionConsumer",t),i=o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${m}]`)),o=Array.from(n.itemMap.values()),i=o.sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current));return i},[n.collectionRef,n.itemMap]);return i},s]}},2210:function(e,t,n){n.d(t,{F:function(){return composeRefs},e:function(){return useComposedRefs}});var o=n(2265);function composeRefs(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function useComposedRefs(...e){return o.useCallback(composeRefs(...e),e)}},6989:function(e,t,n){n.d(t,{b:function(){return createContextScope}});var o=n(2265),i=n(7437);function createContextScope(e,t=[]){let n=[],createScope=()=>{let t=n.map(e=>o.createContext(e));return function(n){let i=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return createScope.scopeName=e,[function(t,l){let a=o.createContext(l),s=n.length;n=[...n,l];let Provider=t=>{let{scope:n,children:l,...u}=t,c=n?.[e]?.[s]||a,d=o.useMemo(()=>u,Object.values(u));return(0,i.jsx)(c.Provider,{value:d,children:l})};return Provider.displayName=t+"Provider",[Provider,function(n,i){let u=i?.[e]?.[s]||a,c=o.useContext(u);if(c)return c;if(void 0!==l)return l;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let createScope=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:o})=>{let i=n(e),l=i[`__scope${o}`];return{...t,...l}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return createScope.scopeName=t.scopeName,createScope}(createScope,...t)]}},7389:function(e,t,n){let o;n.d(t,{x8:function(){return eO},VY:function(){return eP},dk:function(){return eT},aV:function(){return eR},h_:function(){return eA},fC:function(){return eE},Dx:function(){return eN},xz:function(){return eC}});var i,l=n(2265),a=n.t(l,2);function composeEventHandlers(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),!1===n||!o.defaultPrevented)return t?.(o)}}function setRef(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function composeRefs(...e){return t=>{let n=!1,o=e.map(e=>{let o=setRef(e,t);return n||"function"!=typeof o||(n=!0),o});if(n)return()=>{for(let t=0;t<o.length;t++){let n=o[t];"function"==typeof n?n():setRef(e[t],null)}}}}function useComposedRefs(...e){return l.useCallback(composeRefs(...e),e)}var s=n(7437),u=globalThis?.document?l.useLayoutEffect:()=>{},c=a[" useId ".trim().toString()]||(()=>void 0),d=0;function useId(e){let[t,n]=l.useState(c());return u(()=>{e||n(e=>e??String(d++))},[e]),e||(t?`radix-${t}`:"")}var f=a[" useInsertionEffect ".trim().toString()]||u;function dist_composeEventHandlers(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e?.(o),!1===n||!o.defaultPrevented)return t?.(o)}}Symbol("RADIX:SYNC_STATE");var p=n(4887);function dist_setRef(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function dist_composeRefs(...e){return t=>{let n=!1,o=e.map(e=>{let o=dist_setRef(e,t);return n||"function"!=typeof o||(n=!0),o});if(n)return()=>{for(let t=0;t<o.length;t++){let n=o[t];"function"==typeof n?n():dist_setRef(e[t],null)}}}}function dist_useComposedRefs(...e){return l.useCallback(dist_composeRefs(...e),e)}var m=Symbol("radix.slottable");function isSlottable(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===m}var g=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:n,...o}=e;if(l.isValidElement(n)){let e,i;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,s=function(e,t){let n={...t};for(let o in t){let i=e[o],l=t[o],a=/^on[A-Z]/.test(o);a?i&&l?n[o]=(...e)=>{let t=l(...e);return i(...e),t}:i&&(n[o]=i):"style"===o?n[o]={...i,...l}:"className"===o&&(n[o]=[i,l].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==l.Fragment&&(s.ref=t?dist_composeRefs(t,a):a),l.cloneElement(n,s)}return l.Children.count(n)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=l.forwardRef((e,n)=>{let{children:o,...i}=e,a=l.Children.toArray(o),u=a.find(isSlottable);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:n,children:l.isValidElement(e)?l.cloneElement(e,void 0,o):null})}return(0,s.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=l.forwardRef((e,o)=>{let{asChild:i,...l}=e,a=i?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a,{...l,ref:o})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function useCallbackRef(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var h="dismissableLayer.update",v=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:u,onInteractOutside:c,onDismiss:d,...f}=e,p=l.useContext(v),[m,y]=l.useState(null),b=m?.ownerDocument??globalThis?.document,[,w]=l.useState({}),x=dist_useComposedRefs(t,e=>y(e)),S=Array.from(p.layers),[_]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),E=S.indexOf(_),C=m?S.indexOf(m):-1,A=p.layersWithOutsidePointerEventsDisabled.size>0,R=C>=E,P=function(e,t=globalThis?.document){let n=useCallbackRef(e),o=l.useRef(!1),i=l.useRef(()=>{});return l.useEffect(()=>{let handlePointerDown=e=>{if(e.target&&!o.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=handleAndDispatchPointerDownOutsideEvent2,t.addEventListener("click",i.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else t.removeEventListener("click",i.current);o.current=!1},e=window.setTimeout(()=>{t.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),t.removeEventListener("pointerdown",handlePointerDown),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...p.branches].some(e=>e.contains(t));!R||n||(a?.(e),c?.(e),e.defaultPrevented||d?.())},b),N=function(e,t=globalThis?.document){let n=useCallbackRef(e),o=l.useRef(!1);return l.useEffect(()=>{let handleFocus=e=>{e.target&&!o.current&&handleAndDispatchCustomEvent("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",handleFocus),()=>t.removeEventListener("focusin",handleFocus)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target,n=[...p.branches].some(e=>e.contains(t));n||(u?.(e),c?.(e),e.defaultPrevented||d?.())},b);return!function(e,t=globalThis?.document){let n=function(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}(e);l.useEffect(()=>{let handleKeyDown=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>t.removeEventListener("keydown",handleKeyDown,{capture:!0})},[n,t])}(e=>{let t=C===p.layers.size-1;t&&(o?.(e),!e.defaultPrevented&&d&&(e.preventDefault(),d()))},b),l.useEffect(()=>{if(m)return n&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(i=b.body.style.pointerEvents,b.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(m)),p.layers.add(m),dispatchUpdate(),()=>{n&&1===p.layersWithOutsidePointerEventsDisabled.size&&(b.body.style.pointerEvents=i)}},[m,b,n,p]),l.useEffect(()=>()=>{m&&(p.layers.delete(m),p.layersWithOutsidePointerEventsDisabled.delete(m),dispatchUpdate())},[m,p]),l.useEffect(()=>{let handleUpdate=()=>w({});return document.addEventListener(h,handleUpdate),()=>document.removeEventListener(h,handleUpdate)},[]),(0,s.jsx)(g.div,{...f,ref:x,style:{pointerEvents:A?R?"auto":"none":void 0,...e.style},onFocusCapture:dist_composeEventHandlers(e.onFocusCapture,N.onFocusCapture),onBlurCapture:dist_composeEventHandlers(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:dist_composeEventHandlers(e.onPointerDownCapture,P.onPointerDownCapture)})});function dispatchUpdate(){let e=new CustomEvent(h);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,t,n,{discrete:o}){let i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&i.addEventListener(e,t,{once:!0}),o)?i&&p.flushSync(()=>i.dispatchEvent(l)):i.dispatchEvent(l)}function react_compose_refs_dist_setRef(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function react_compose_refs_dist_composeRefs(...e){return t=>{let n=!1,o=e.map(e=>{let o=react_compose_refs_dist_setRef(e,t);return n||"function"!=typeof o||(n=!0),o});if(n)return()=>{for(let t=0;t<o.length;t++){let n=o[t];"function"==typeof n?n():react_compose_refs_dist_setRef(e[t],null)}}}}y.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(v),o=l.useRef(null),i=dist_useComposedRefs(t,o);return l.useEffect(()=>{let e=o.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(g.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var b=Symbol("radix.slottable");function dist_isSlottable(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===b}var w=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:n,...o}=e;if(l.isValidElement(n)){let e,i;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,s=function(e,t){let n={...t};for(let o in t){let i=e[o],l=t[o],a=/^on[A-Z]/.test(o);a?i&&l?n[o]=(...e)=>{let t=l(...e);return i(...e),t}:i&&(n[o]=i):"style"===o?n[o]={...i,...l}:"className"===o&&(n[o]=[i,l].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==l.Fragment&&(s.ref=t?react_compose_refs_dist_composeRefs(t,a):a),l.cloneElement(n,s)}return l.Children.count(n)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=l.forwardRef((e,n)=>{let{children:o,...i}=e,a=l.Children.toArray(o),u=a.find(dist_isSlottable);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:n,children:l.isValidElement(e)?l.cloneElement(e,void 0,o):null})}return(0,s.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=l.forwardRef((e,o)=>{let{asChild:i,...l}=e,a=i?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a,{...l,ref:o})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function react_use_callback_ref_dist_useCallbackRef(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var x="focusScope.autoFocusOnMount",S="focusScope.autoFocusOnUnmount",_={bubbles:!1,cancelable:!0},E=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:o=!1,onMountAutoFocus:i,onUnmountAutoFocus:a,...u}=e,[c,d]=l.useState(null),f=react_use_callback_ref_dist_useCallbackRef(i),p=react_use_callback_ref_dist_useCallbackRef(a),m=l.useRef(null),g=function(...e){return l.useCallback(react_compose_refs_dist_composeRefs(...e),e)}(t,e=>d(e)),h=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(o){let handleFocusIn2=function(e){if(h.paused||!c)return;let t=e.target;c.contains(t)?m.current=t:dist_focus(m.current,{select:!0})},handleFocusOut2=function(e){if(h.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||dist_focus(m.current,{select:!0})};document.addEventListener("focusin",handleFocusIn2),document.addEventListener("focusout",handleFocusOut2);let e=new MutationObserver(function(e){let t=document.activeElement;if(t===document.body)for(let t of e)t.removedNodes.length>0&&dist_focus(c)});return c&&e.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",handleFocusIn2),document.removeEventListener("focusout",handleFocusOut2),e.disconnect()}}},[o,c,h.paused]),l.useEffect(()=>{if(c){C.add(h);let e=document.activeElement,t=c.contains(e);if(!t){let t=new CustomEvent(x,_);c.addEventListener(x,f),c.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let o of e)if(dist_focus(o,{select:t}),document.activeElement!==n)return}(getTabbableCandidates(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&dist_focus(c))}return()=>{c.removeEventListener(x,f),setTimeout(()=>{let t=new CustomEvent(S,_);c.addEventListener(S,p),c.dispatchEvent(t),t.defaultPrevented||dist_focus(e??document.body,{select:!0}),c.removeEventListener(S,p),C.remove(h)},0)}}},[c,f,p,h]);let v=l.useCallback(e=>{if(!n&&!o||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[o,l]=function(e){let t=getTabbableCandidates(e),n=findVisible(t,e),o=findVisible(t.reverse(),e);return[n,o]}(t),a=o&&l;a?e.shiftKey||i!==l?e.shiftKey&&i===o&&(e.preventDefault(),n&&dist_focus(l,{select:!0})):(e.preventDefault(),n&&dist_focus(o,{select:!0})):i===t&&e.preventDefault()}},[n,o,h.paused]);return(0,s.jsx)(w.div,{tabIndex:-1,...u,ref:g,onKeyDown:v})});function getTabbableCandidates(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function findVisible(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function dist_focus(e,{select:t=!1}={}){if(e&&e.focus){var n;let o=document.activeElement;e.focus({preventScroll:!0}),e!==o&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}E.displayName="FocusScope";var C=(o=[],{add(e){let t=o[0];e!==t&&t?.pause(),(o=arrayRemove(o,e)).unshift(e)},remove(e){o=arrayRemove(o,e),o[0]?.resume()}});function arrayRemove(e,t){let n=[...e],o=n.indexOf(t);return -1!==o&&n.splice(o,1),n}function _radix_ui_react_compose_refs_dist_setRef(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var A=Symbol("radix.slottable");function react_slot_dist_isSlottable(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===A}var R=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:n,...o}=e;if(l.isValidElement(n)){let e,i;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,s=function(e,t){let n={...t};for(let o in t){let i=e[o],l=t[o],a=/^on[A-Z]/.test(o);a?i&&l?n[o]=(...e)=>{let t=l(...e);return i(...e),t}:i&&(n[o]=i):"style"===o?n[o]={...i,...l}:"className"===o&&(n[o]=[i,l].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==l.Fragment&&(s.ref=t?function(...e){return t=>{let n=!1,o=e.map(e=>{let o=_radix_ui_react_compose_refs_dist_setRef(e,t);return n||"function"!=typeof o||(n=!0),o});if(n)return()=>{for(let t=0;t<o.length;t++){let n=o[t];"function"==typeof n?n():_radix_ui_react_compose_refs_dist_setRef(e[t],null)}}}}(t,a):a),l.cloneElement(n,s)}return l.Children.count(n)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=l.forwardRef((e,n)=>{let{children:o,...i}=e,a=l.Children.toArray(o),u=a.find(react_slot_dist_isSlottable);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:n,children:l.isValidElement(e)?l.cloneElement(e,void 0,o):null})}return(0,s.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=l.forwardRef((e,o)=>{let{asChild:i,...l}=e,a=i?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a,{...l,ref:o})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),P=globalThis?.document?l.useLayoutEffect:()=>{},N=l.forwardRef((e,t)=>{let{container:n,...o}=e,[i,a]=l.useState(!1);P(()=>a(!0),[]);let u=n||i&&globalThis?.document?.body;return u?p.createPortal((0,s.jsx)(R.div,{...o,ref:t}),u):null});N.displayName="Portal";var Presence=e=>{let t,n;let{present:o,children:i}=e,a=function(e){var t;let[n,o]=l.useState(),i=l.useRef(null),a=l.useRef(e),s=l.useRef("none"),c=e?"mounted":"unmounted",[d,f]=(t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},l.useReducer((e,n)=>{let o=t[e][n];return o??e},c));return l.useEffect(()=>{let e=getAnimationName(i.current);s.current="mounted"===d?e:"none"},[d]),u(()=>{let t=i.current,n=a.current,o=n!==e;if(o){let o=s.current,i=getAnimationName(t);e?f("MOUNT"):"none"===i||t?.display==="none"?f("UNMOUNT"):n&&o!==i?f("ANIMATION_OUT"):f("UNMOUNT"),a.current=e}},[e,f]),u(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,handleAnimationEnd=o=>{let l=getAnimationName(i.current),s=l.includes(o.animationName);if(o.target===n&&s&&(f("ANIMATION_END"),!a.current)){let o=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=o)})}},handleAnimationStart=e=>{e.target===n&&(s.current=getAnimationName(i.current))};return n.addEventListener("animationstart",handleAnimationStart),n.addEventListener("animationcancel",handleAnimationEnd),n.addEventListener("animationend",handleAnimationEnd),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",handleAnimationStart),n.removeEventListener("animationcancel",handleAnimationEnd),n.removeEventListener("animationend",handleAnimationEnd)}}f("ANIMATION_END")},[n,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:l.useCallback(e=>{i.current=e?getComputedStyle(e):null,o(e)},[])}}(o),s="function"==typeof i?i({present:a.isPresent}):l.Children.only(i),c=useComposedRefs(a.ref,(t=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?s.ref:(t=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?s.props.ref:s.props.ref||s.ref),d="function"==typeof i;return d||a.isPresent?l.cloneElement(s,{ref:c}):null};function getAnimationName(e){return e?.animationName||"none"}function _radix_ui_react_slot_dist_createSlot(e){let t=function(e){let t=l.forwardRef((e,t)=>{let{children:n,...o}=e;if(l.isValidElement(n)){let e,i;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref,s=function(e,t){let n={...t};for(let o in t){let i=e[o],l=t[o],a=/^on[A-Z]/.test(o);a?i&&l?n[o]=(...e)=>{let t=l(...e);return i(...e),t}:i&&(n[o]=i):"style"===o?n[o]={...i,...l}:"className"===o&&(n[o]=[i,l].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==l.Fragment&&(s.ref=t?composeRefs(t,a):a),l.cloneElement(n,s)}return l.Children.count(n)>1?l.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=l.forwardRef((e,n)=>{let{children:o,...i}=e,a=l.Children.toArray(o),u=a.find(_radix_ui_react_slot_dist_isSlottable);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:l.Children.count(e)>1?l.Children.only(null):l.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...i,ref:n,children:l.isValidElement(e)?l.cloneElement(e,void 0,o):null})}return(0,s.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}Presence.displayName="Presence";var T=Symbol("radix.slottable");function _radix_ui_react_slot_dist_isSlottable(e){return l.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===T}var O=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=_radix_ui_react_slot_dist_createSlot(`Primitive.${t}`),o=l.forwardRef((e,o)=>{let{asChild:i,...l}=e,a=i?n:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a,{...l,ref:o})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),k=0;function createFocusGuard(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var L=n(44),D=n(5322),M=n(5835),W=(0,n(8427)._)(),nothing=function(){},j=l.forwardRef(function(e,t){var n=l.useRef(null),o=l.useState({onScrollCapture:nothing,onWheelCapture:nothing,onTouchMoveCapture:nothing}),i=o[0],a=o[1],s=e.forwardProps,u=e.children,c=e.className,d=e.removeScrollBar,f=e.enabled,p=e.shards,m=e.sideCar,g=e.noRelative,h=e.noIsolation,v=e.inert,y=e.allowPinchZoom,b=e.as,w=void 0===b?"div":b,x=e.gapMode,S=(0,L._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),_=(0,M.q)([n,t]),E=(0,L.pi)((0,L.pi)({},S),i);return l.createElement(l.Fragment,null,f&&l.createElement(m,{sideCar:W,removeScrollBar:d,shards:p,noRelative:g,noIsolation:h,inert:v,setCallbacks:a,allowPinchZoom:!!y,lockRef:n,gapMode:x}),s?l.cloneElement(l.Children.only(u),(0,L.pi)((0,L.pi)({},E),{ref:_})):l.createElement(w,(0,L.pi)({},E,{className:c,ref:_}),u))});j.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},j.classNames={fullWidth:D.zi,zeroRight:D.pF};var I=n(6898),V=n(2776),F=n(8662),B=!1;if("undefined"!=typeof window)try{var z=Object.defineProperty({},"passive",{get:function(){return B=!0,!0}});window.addEventListener("test",z,z),window.removeEventListener("test",z,z)}catch(e){B=!1}var H=!!B&&{passive:!1},elementCanBeScrolled=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},locationCouldBeScrolled=function(e,t){var n=t.ownerDocument,o=t;do{if("undefined"!=typeof ShadowRoot&&o instanceof ShadowRoot&&(o=o.host),elementCouldBeScrolled(e,o)){var i=getScrollVariables(e,o);if(i[1]>i[2])return!0}o=o.parentNode}while(o&&o!==n.body);return!1},elementCouldBeScrolled=function(e,t){return"v"===e?elementCanBeScrolled(t,"overflowY"):elementCanBeScrolled(t,"overflowX")},getScrollVariables=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},handleScroll=function(e,t,n,o,i){var l,a=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),s=a*o,u=n.target,c=t.contains(u),d=!1,f=s>0,p=0,m=0;do{if(!u)break;var g=getScrollVariables(e,u),h=g[0],v=g[1]-g[2]-a*h;(h||v)&&elementCouldBeScrolled(e,u)&&(p+=v,m+=h);var y=u.parentNode;u=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(i&&1>Math.abs(p)||!i&&s>p)?d=!0:!f&&(i&&1>Math.abs(m)||!i&&-s>m)&&(d=!0),d},getTouchXY=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},getDeltaXY=function(e){return[e.deltaX,e.deltaY]},extractRef=function(e){return e&&"current"in e?e.current:e},$=0,U=[],Z=(0,I.L)(W,function(e){var t=l.useRef([]),n=l.useRef([0,0]),o=l.useRef(),i=l.useState($++)[0],a=l.useState(F.Ws)[0],s=l.useRef(e);l.useEffect(function(){s.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(0,L.ev)([e.lockRef.current],(e.shards||[]).map(extractRef),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var i,l=getTouchXY(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-l[0],c="deltaY"in e?e.deltaY:a[1]-l[1],d=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===d.type)return!1;var p=locationCouldBeScrolled(f,d);if(!p)return!0;if(p?i=f:(i="v"===f?"h":"v",p=locationCouldBeScrolled(f,d)),!p)return!1;if(!o.current&&"changedTouches"in e&&(u||c)&&(o.current=i),!i)return!0;var m=o.current||i;return handleScroll(m,t,e,"h"===m?u:c,!0)},[]),c=l.useCallback(function(e){if(U.length&&U[U.length-1]===a){var n="deltaY"in e?getDeltaXY(e):getTouchXY(e),o=t.current.filter(function(t){var o;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(o=t.delta)[0]===n[0]&&o[1]===n[1]})[0];if(o&&o.should){e.cancelable&&e.preventDefault();return}if(!o){var i=(s.current.shards||[]).map(extractRef).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?u(e,i[0]):!s.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=l.useCallback(function(e,n,o,i){var l={name:e,delta:n,target:o,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(o)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),f=l.useCallback(function(e){n.current=getTouchXY(e),o.current=void 0},[]),p=l.useCallback(function(t){d(t.type,getDeltaXY(t),t.target,u(t,e.lockRef.current))},[]),m=l.useCallback(function(t){d(t.type,getTouchXY(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return U.push(a),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:m}),document.addEventListener("wheel",c,H),document.addEventListener("touchmove",c,H),document.addEventListener("touchstart",f,H),function(){U=U.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,H),document.removeEventListener("touchmove",c,H),document.removeEventListener("touchstart",f,H)}},[]);var g=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(a,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,g?l.createElement(V.jp,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),K=l.forwardRef(function(e,t){return l.createElement(j,(0,L.pi)({},e,{ref:t,sideCar:Z}))});K.classNames=j.classNames;var X=n(5859),Y="Dialog",[G,J]=function(e,t=[]){let n=[],createScope=()=>{let t=n.map(e=>l.createContext(e));return function(n){let o=n?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return createScope.scopeName=e,[function(t,o){let i=l.createContext(o),a=n.length;n=[...n,o];let Provider=t=>{let{scope:n,children:o,...u}=t,c=n?.[e]?.[a]||i,d=l.useMemo(()=>u,Object.values(u));return(0,s.jsx)(c.Provider,{value:d,children:o})};return Provider.displayName=t+"Provider",[Provider,function(n,s){let u=s?.[e]?.[a]||i,c=l.useContext(u);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let createScope=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:o})=>{let i=n(e),l=i[`__scope${o}`];return{...t,...l}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return createScope.scopeName=t.scopeName,createScope}(createScope,...t)]}(Y),[Q,ee]=G(Y),Dialog=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,c=l.useRef(null),d=l.useRef(null),[p,m]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:o}){let[i,a,s]=function({defaultProp:e,onChange:t}){let[n,o]=l.useState(e),i=l.useRef(n),a=l.useRef(t);return f(()=>{a.current=t},[t]),l.useEffect(()=>{i.current!==n&&(a.current?.(n),i.current=n)},[n,i]),[n,o,a]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:i;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${o} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,o])}let d=l.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&s.current?.(n)}else a(t)},[u,e,a,s]);return[c,d]}({prop:o,defaultProp:i??!1,onChange:a,caller:Y});return(0,s.jsx)(Q,{scope:t,triggerRef:c,contentRef:d,contentId:useId(),titleId:useId(),descriptionId:useId(),open:p,onOpenChange:m,onOpenToggle:l.useCallback(()=>m(e=>!e),[m]),modal:u,children:n})};Dialog.displayName=Y;var et="DialogTrigger",er=l.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,i=ee(et,n),l=useComposedRefs(t,i.triggerRef);return(0,s.jsx)(O.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":getState(i.open),...o,ref:l,onClick:composeEventHandlers(e.onClick,i.onOpenToggle)})});er.displayName=et;var en="DialogPortal",[eo,ei]=G(en,{forceMount:void 0}),DialogPortal=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=ee(en,t);return(0,s.jsx)(eo,{scope:t,forceMount:n,children:l.Children.map(o,e=>(0,s.jsx)(Presence,{present:n||a.open,children:(0,s.jsx)(N,{asChild:!0,container:i,children:e})}))})};DialogPortal.displayName=en;var el="DialogOverlay",ea=l.forwardRef((e,t)=>{let n=ei(el,e.__scopeDialog),{forceMount:o=n.forceMount,...i}=e,l=ee(el,e.__scopeDialog);return l.modal?(0,s.jsx)(Presence,{present:o||l.open,children:(0,s.jsx)(eu,{...i,ref:t})}):null});ea.displayName=el;var es=_radix_ui_react_slot_dist_createSlot("DialogOverlay.RemoveScroll"),eu=l.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,i=ee(el,n);return(0,s.jsx)(K,{as:es,allowPinchZoom:!0,shards:[i.contentRef],children:(0,s.jsx)(O.div,{"data-state":getState(i.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),ec="DialogContent",ed=l.forwardRef((e,t)=>{let n=ei(ec,e.__scopeDialog),{forceMount:o=n.forceMount,...i}=e,l=ee(ec,e.__scopeDialog);return(0,s.jsx)(Presence,{present:o||l.open,children:l.modal?(0,s.jsx)(ef,{...i,ref:t}):(0,s.jsx)(ep,{...i,ref:t})})});ed.displayName=ec;var ef=l.forwardRef((e,t)=>{let n=ee(ec,e.__scopeDialog),o=l.useRef(null),i=useComposedRefs(t,n.contentRef,o);return l.useEffect(()=>{let e=o.current;if(e)return(0,X.Ry)(e)},[]),(0,s.jsx)(em,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:composeEventHandlers(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:composeEventHandlers(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,o=2===t.button||n;o&&e.preventDefault()}),onFocusOutside:composeEventHandlers(e.onFocusOutside,e=>e.preventDefault())})}),ep=l.forwardRef((e,t)=>{let n=ee(ec,e.__scopeDialog),o=l.useRef(!1),i=l.useRef(!1);return(0,s.jsx)(em,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||n.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let l=t.target,a=n.triggerRef.current?.contains(l);a&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),em=l.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:a,...u}=e,c=ee(ec,n),d=l.useRef(null),f=useComposedRefs(t,d);return l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??createFocusGuard()),document.body.insertAdjacentElement("beforeend",e[1]??createFocusGuard()),k++,()=>{1===k&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),k--}},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(E,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:a,children:(0,s.jsx)(y,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":getState(c.open),...u,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(TitleWarning,{titleId:c.titleId}),(0,s.jsx)(DescriptionWarning,{contentRef:d,descriptionId:c.descriptionId})]})]})}),eg="DialogTitle",eh=l.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,i=ee(eg,n);return(0,s.jsx)(O.h2,{id:i.titleId,...o,ref:t})});eh.displayName=eg;var ev="DialogDescription",ey=l.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,i=ee(ev,n);return(0,s.jsx)(O.p,{id:i.descriptionId,...o,ref:t})});ey.displayName=ev;var eb="DialogClose",ew=l.forwardRef((e,t)=>{let{__scopeDialog:n,...o}=e,i=ee(eb,n);return(0,s.jsx)(O.button,{type:"button",...o,ref:t,onClick:composeEventHandlers(e.onClick,()=>i.onOpenChange(!1))})});function getState(e){return e?"open":"closed"}ew.displayName=eb;var ex="DialogTitleWarning",[eS,e_]=function(e,t){let n=l.createContext(t),Provider=e=>{let{children:t,...o}=e,i=l.useMemo(()=>o,Object.values(o));return(0,s.jsx)(n.Provider,{value:i,children:t})};return Provider.displayName=e+"Provider",[Provider,function(o){let i=l.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}(ex,{contentName:ec,titleName:eg,docsSlug:"dialog"}),TitleWarning=({titleId:e})=>{let t=e_(ex),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{if(e){let t=document.getElementById(e);t||console.error(n)}},[n,e]),null},DescriptionWarning=({contentRef:e,descriptionId:t})=>{let n=e_("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return l.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");if(t&&n){let e=document.getElementById(t);e||console.warn(o)}},[o,e,t]),null},eE=Dialog,eC=er,eA=DialogPortal,eR=ea,eP=ed,eN=eh,eT=ey,eO=ew},966:function(e,t,n){n.d(t,{M:function(){return useId}});var o,i=n(2265),l=n(1030),a=(o||(o=n.t(i,2)))["useId".toString()]||(()=>void 0),s=0;function useId(e){let[t,n]=i.useState(a());return(0,l.b)(()=>{e||n(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},6743:function(e,t,n){n.d(t,{f:function(){return s}});var o=n(2265),i=n(9790),l=n(7437),a=o.forwardRef((e,t)=>(0,l.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{let n=t.target;n.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var s=a},8452:function(e,t,n){n.d(t,{ee:function(){return J},Eh:function(){return ee},VY:function(){return Q},fC:function(){return G},D7:function(){return I}});var o=n(2265);let i=["top","right","bottom","left"],l=Math.min,a=Math.max,s=Math.round,u=Math.floor,createCoords=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function floating_ui_utils_evaluate(e,t){return"function"==typeof e?e(t):e}function floating_ui_utils_getSide(e){return e.split("-")[0]}function floating_ui_utils_getAlignment(e){return e.split("-")[1]}function getOppositeAxis(e){return"x"===e?"y":"x"}function getAxisLength(e){return"y"===e?"height":"width"}let f=new Set(["top","bottom"]);function floating_ui_utils_getSideAxis(e){return f.has(floating_ui_utils_getSide(e))?"y":"x"}function floating_ui_utils_getOppositeAlignmentPlacement(e){return e.replace(/start|end/g,e=>d[e])}let p=["left","right"],m=["right","left"],g=["top","bottom"],h=["bottom","top"];function getOppositePlacement(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function floating_ui_utils_getPaddingObject(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function floating_ui_utils_rectToClientRect(e){let{x:t,y:n,width:o,height:i}=e;return{width:o,height:i,top:n,left:t,right:t+o,bottom:n+i,x:t,y:n}}function computeCoordsFromPlacement(e,t,n){let o,{reference:i,floating:l}=e,a=floating_ui_utils_getSideAxis(t),s=getOppositeAxis(floating_ui_utils_getSideAxis(t)),u=getAxisLength(s),c=floating_ui_utils_getSide(t),d="y"===a,f=i.x+i.width/2-l.width/2,p=i.y+i.height/2-l.height/2,m=i[u]/2-l[u]/2;switch(c){case"top":o={x:f,y:i.y-l.height};break;case"bottom":o={x:f,y:i.y+i.height};break;case"right":o={x:i.x+i.width,y:p};break;case"left":o={x:i.x-l.width,y:p};break;default:o={x:i.x,y:i.y}}switch(floating_ui_utils_getAlignment(t)){case"start":o[s]-=m*(n&&d?-1:1);break;case"end":o[s]+=m*(n&&d?-1:1)}return o}let computePosition=async(e,t,n)=>{let{placement:o="bottom",strategy:i="absolute",middleware:l=[],platform:a}=n,s=l.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:d,y:f}=computeCoordsFromPlacement(c,o,u),p=o,m={},g=0;for(let n=0;n<s.length;n++){let{name:l,fn:h}=s[n],{x:v,y:y,data:b,reset:w}=await h({x:d,y:f,initialPlacement:o,placement:p,strategy:i,middlewareData:m,rects:c,platform:a,elements:{reference:e,floating:t}});d=null!=v?v:d,f=null!=y?y:f,m={...m,[l]:{...m[l],...b}},w&&g<=50&&(g++,"object"==typeof w&&(w.placement&&(p=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:d,y:f}=computeCoordsFromPlacement(c,p,u)),n=-1)}return{x:d,y:f,placement:p,strategy:i,middlewareData:m}};async function detectOverflow(e,t){var n;void 0===t&&(t={});let{x:o,y:i,platform:l,rects:a,elements:s,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:p=!1,padding:m=0}=floating_ui_utils_evaluate(t,e),g=floating_ui_utils_getPaddingObject(m),h=s[p?"floating"===f?"reference":"floating":f],v=floating_ui_utils_rectToClientRect(await l.getClippingRect({element:null==(n=await (null==l.isElement?void 0:l.isElement(h)))||n?h:h.contextElement||await (null==l.getDocumentElement?void 0:l.getDocumentElement(s.floating)),boundary:c,rootBoundary:d,strategy:u})),y="floating"===f?{x:o,y:i,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==l.getOffsetParent?void 0:l.getOffsetParent(s.floating)),w=await (null==l.isElement?void 0:l.isElement(b))&&await (null==l.getScale?void 0:l.getScale(b))||{x:1,y:1},x=floating_ui_utils_rectToClientRect(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:y,offsetParent:b,strategy:u}):y);return{top:(v.top-x.top+g.top)/w.y,bottom:(x.bottom-v.bottom+g.bottom)/w.y,left:(v.left-x.left+g.left)/w.x,right:(x.right-v.right+g.right)/w.x}}function getSideOffsets(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function isAnySideFullyClipped(e){return i.some(t=>e[t]>=0)}let v=new Set(["left","top"]);async function convertValueToCoords(e,t){let{placement:n,platform:o,elements:i}=e,l=await (null==o.isRTL?void 0:o.isRTL(i.floating)),a=floating_ui_utils_getSide(n),s=floating_ui_utils_getAlignment(n),u="y"===floating_ui_utils_getSideAxis(n),c=v.has(a)?-1:1,d=l&&u?-1:1,f=floating_ui_utils_evaluate(t,e),{mainAxis:p,crossAxis:m,alignmentAxis:g}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return s&&"number"==typeof g&&(m="end"===s?-1*g:g),u?{x:m*d,y:p*c}:{x:p*c,y:m*d}}function hasWindow(){return"undefined"!=typeof window}function getNodeName(e){return isNode(e)?(e.nodeName||"").toLowerCase():"#document"}function getWindow(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function getDocumentElement(e){var t;return null==(t=(isNode(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function isNode(e){return!!hasWindow()&&(e instanceof Node||e instanceof getWindow(e).Node)}function isElement(e){return!!hasWindow()&&(e instanceof Element||e instanceof getWindow(e).Element)}function isHTMLElement(e){return!!hasWindow()&&(e instanceof HTMLElement||e instanceof getWindow(e).HTMLElement)}function isShadowRoot(e){return!!hasWindow()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof getWindow(e).ShadowRoot)}let y=new Set(["inline","contents"]);function isOverflowElement(e){let{overflow:t,overflowX:n,overflowY:o,display:i}=getComputedStyle(e);return/auto|scroll|overlay|hidden|clip/.test(t+o+n)&&!y.has(i)}let b=new Set(["table","td","th"]),w=[":popover-open",":modal"];function isTopLayer(e){return w.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let x=["transform","translate","scale","rotate","perspective"],S=["transform","translate","scale","rotate","perspective","filter"],_=["paint","layout","strict","content"];function isContainingBlock(e){let t=isWebKit(),n=isElement(e)?getComputedStyle(e):e;return x.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||S.some(e=>(n.willChange||"").includes(e))||_.some(e=>(n.contain||"").includes(e))}function isWebKit(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let E=new Set(["html","body","#document"]);function isLastTraversableNode(e){return E.has(getNodeName(e))}function getComputedStyle(e){return getWindow(e).getComputedStyle(e)}function getNodeScroll(e){return isElement(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function getParentNode(e){if("html"===getNodeName(e))return e;let t=e.assignedSlot||e.parentNode||isShadowRoot(e)&&e.host||getDocumentElement(e);return isShadowRoot(t)?t.host:t}function getOverflowAncestors(e,t,n){var o;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function getNearestOverflowAncestor(e){let t=getParentNode(e);return isLastTraversableNode(t)?e.ownerDocument?e.ownerDocument.body:e.body:isHTMLElement(t)&&isOverflowElement(t)?t:getNearestOverflowAncestor(t)}(e),l=i===(null==(o=e.ownerDocument)?void 0:o.body),a=getWindow(i);if(l){let e=getFrameElement(a);return t.concat(a,a.visualViewport||[],isOverflowElement(i)?i:[],e&&n?getOverflowAncestors(e):[])}return t.concat(i,getOverflowAncestors(i,[],n))}function getFrameElement(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function getCssDimensions(e){let t=getComputedStyle(e),n=parseFloat(t.width)||0,o=parseFloat(t.height)||0,i=isHTMLElement(e),l=i?e.offsetWidth:n,a=i?e.offsetHeight:o,u=s(n)!==l||s(o)!==a;return u&&(n=l,o=a),{width:n,height:o,$:u}}function unwrapElement(e){return isElement(e)?e:e.contextElement}function getScale(e){let t=unwrapElement(e);if(!isHTMLElement(t))return createCoords(1);let n=t.getBoundingClientRect(),{width:o,height:i,$:l}=getCssDimensions(t),a=(l?s(n.width):n.width)/o,u=(l?s(n.height):n.height)/i;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let C=createCoords(0);function getVisualOffsets(e){let t=getWindow(e);return isWebKit()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:C}function getBoundingClientRect(e,t,n,o){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let l=e.getBoundingClientRect(),a=unwrapElement(e),s=createCoords(1);t&&(o?isElement(o)&&(s=getScale(o)):s=getScale(e));let u=(void 0===(i=n)&&(i=!1),o&&(!i||o===getWindow(a))&&i)?getVisualOffsets(a):createCoords(0),c=(l.left+u.x)/s.x,d=(l.top+u.y)/s.y,f=l.width/s.x,p=l.height/s.y;if(a){let e=getWindow(a),t=o&&isElement(o)?getWindow(o):o,n=e,i=getFrameElement(n);for(;i&&o&&t!==n;){let e=getScale(i),t=i.getBoundingClientRect(),o=getComputedStyle(i),l=t.left+(i.clientLeft+parseFloat(o.paddingLeft))*e.x,a=t.top+(i.clientTop+parseFloat(o.paddingTop))*e.y;c*=e.x,d*=e.y,f*=e.x,p*=e.y,c+=l,d+=a,i=getFrameElement(n=getWindow(i))}}return floating_ui_utils_rectToClientRect({width:f,height:p,x:c,y:d})}function getWindowScrollBarX(e,t){let n=getNodeScroll(e).scrollLeft;return t?t.left+n:getBoundingClientRect(getDocumentElement(e)).left+n}function getHTMLOffset(e,t,n){void 0===n&&(n=!1);let o=e.getBoundingClientRect(),i=o.left+t.scrollLeft-(n?0:getWindowScrollBarX(e,o)),l=o.top+t.scrollTop;return{x:i,y:l}}let A=new Set(["absolute","fixed"]);function getClientRectFromClippingAncestor(e,t,n){let o;if("viewport"===t)o=function(e,t){let n=getWindow(e),o=getDocumentElement(e),i=n.visualViewport,l=o.clientWidth,a=o.clientHeight,s=0,u=0;if(i){l=i.width,a=i.height;let e=isWebKit();(!e||e&&"fixed"===t)&&(s=i.offsetLeft,u=i.offsetTop)}return{width:l,height:a,x:s,y:u}}(e,n);else if("document"===t)o=function(e){let t=getDocumentElement(e),n=getNodeScroll(e),o=e.ownerDocument.body,i=a(t.scrollWidth,t.clientWidth,o.scrollWidth,o.clientWidth),l=a(t.scrollHeight,t.clientHeight,o.scrollHeight,o.clientHeight),s=-n.scrollLeft+getWindowScrollBarX(e),u=-n.scrollTop;return"rtl"===getComputedStyle(o).direction&&(s+=a(t.clientWidth,o.clientWidth)-i),{width:i,height:l,x:s,y:u}}(getDocumentElement(e));else if(isElement(t))o=function(e,t){let n=getBoundingClientRect(e,!0,"fixed"===t),o=n.top+e.clientTop,i=n.left+e.clientLeft,l=isHTMLElement(e)?getScale(e):createCoords(1),a=e.clientWidth*l.x,s=e.clientHeight*l.y,u=i*l.x,c=o*l.y;return{width:a,height:s,x:u,y:c}}(t,n);else{let n=getVisualOffsets(e);o={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return floating_ui_utils_rectToClientRect(o)}function isStaticPositioned(e){return"static"===getComputedStyle(e).position}function getTrueOffsetParent(e,t){if(!isHTMLElement(e)||"fixed"===getComputedStyle(e).position)return null;if(t)return t(e);let n=e.offsetParent;return getDocumentElement(e)===n&&(n=n.ownerDocument.body),n}function getOffsetParent(e,t){var n;let o=getWindow(e);if(isTopLayer(e))return o;if(!isHTMLElement(e)){let t=getParentNode(e);for(;t&&!isLastTraversableNode(t);){if(isElement(t)&&!isStaticPositioned(t))return t;t=getParentNode(t)}return o}let i=getTrueOffsetParent(e,t);for(;i&&(n=i,b.has(getNodeName(n)))&&isStaticPositioned(i);)i=getTrueOffsetParent(i,t);return i&&isLastTraversableNode(i)&&isStaticPositioned(i)&&!isContainingBlock(i)?o:i||function(e){let t=getParentNode(e);for(;isHTMLElement(t)&&!isLastTraversableNode(t);){if(isContainingBlock(t))return t;if(isTopLayer(t))break;t=getParentNode(t)}return null}(e)||o}let getElementRects=async function(e){let t=this.getOffsetParent||getOffsetParent,n=this.getDimensions,o=await n(e.floating);return{reference:function(e,t,n){let o=isHTMLElement(t),i=getDocumentElement(t),l="fixed"===n,a=getBoundingClientRect(e,!0,l,t),s={scrollLeft:0,scrollTop:0},u=createCoords(0);if(o||!o&&!l){if(("body"!==getNodeName(t)||isOverflowElement(i))&&(s=getNodeScroll(t)),o){let e=getBoundingClientRect(t,!0,l,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else i&&(u.x=getWindowScrollBarX(i))}l&&!o&&i&&(u.x=getWindowScrollBarX(i));let c=!i||o||l?createCoords(0):getHTMLOffset(i,s),d=a.left+s.scrollLeft-u.x-c.x,f=a.top+s.scrollTop-u.y-c.y;return{x:d,y:f,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}},R={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:o,strategy:i}=e,l="fixed"===i,a=getDocumentElement(o),s=!!t&&isTopLayer(t.floating);if(o===a||s&&l)return n;let u={scrollLeft:0,scrollTop:0},c=createCoords(1),d=createCoords(0),f=isHTMLElement(o);if((f||!f&&!l)&&(("body"!==getNodeName(o)||isOverflowElement(a))&&(u=getNodeScroll(o)),isHTMLElement(o))){let e=getBoundingClientRect(o);c=getScale(o),d.x=e.x+o.clientLeft,d.y=e.y+o.clientTop}let p=!a||f||l?createCoords(0):getHTMLOffset(a,u,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-u.scrollLeft*c.x+d.x+p.x,y:n.y*c.y-u.scrollTop*c.y+d.y+p.y}},getDocumentElement:getDocumentElement,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:o,strategy:i}=e,s="clippingAncestors"===n?isTopLayer(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let o=getOverflowAncestors(e,[],!1).filter(e=>isElement(e)&&"body"!==getNodeName(e)),i=null,l="fixed"===getComputedStyle(e).position,a=l?getParentNode(e):e;for(;isElement(a)&&!isLastTraversableNode(a);){let t=getComputedStyle(a),n=isContainingBlock(a);n||"fixed"!==t.position||(i=null);let s=l?!n&&!i:!n&&"static"===t.position&&!!i&&A.has(i.position)||isOverflowElement(a)&&!n&&function hasFixedPositionAncestor(e,t){let n=getParentNode(e);return!(n===t||!isElement(n)||isLastTraversableNode(n))&&("fixed"===getComputedStyle(n).position||hasFixedPositionAncestor(n,t))}(e,a);s?o=o.filter(e=>e!==a):i=t,a=getParentNode(a)}return t.set(e,o),o}(t,this._c):[].concat(n),u=[...s,o],c=u[0],d=u.reduce((e,n)=>{let o=getClientRectFromClippingAncestor(t,n,i);return e.top=a(o.top,e.top),e.right=l(o.right,e.right),e.bottom=l(o.bottom,e.bottom),e.left=a(o.left,e.left),e},getClientRectFromClippingAncestor(t,c,i));return{width:d.right-d.left,height:d.bottom-d.top,x:d.left,y:d.top}},getOffsetParent,getElementRects,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=getCssDimensions(e);return{width:t,height:n}},getScale,isElement:isElement,isRTL:function(e){return"rtl"===getComputedStyle(e).direction}};function rectsAreEqual(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let floating_ui_dom_arrow=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:o,placement:i,rects:s,platform:u,elements:c,middlewareData:d}=t,{element:f,padding:p=0}=floating_ui_utils_evaluate(e,t)||{};if(null==f)return{};let m=floating_ui_utils_getPaddingObject(p),g={x:n,y:o},h=getOppositeAxis(floating_ui_utils_getSideAxis(i)),v=getAxisLength(h),y=await u.getDimensions(f),b="y"===h,w=b?"clientHeight":"clientWidth",x=s.reference[v]+s.reference[h]-g[h]-s.floating[v],S=g[h]-s.reference[h],_=await (null==u.getOffsetParent?void 0:u.getOffsetParent(f)),E=_?_[w]:0;E&&await (null==u.isElement?void 0:u.isElement(_))||(E=c.floating[w]||s.floating[v]);let C=E/2-y[v]/2-1,A=l(m[b?"top":"left"],C),R=l(m[b?"bottom":"right"],C),P=E-y[v]-R,N=E/2-y[v]/2+(x/2-S/2),T=a(A,l(N,P)),O=!d.arrow&&null!=floating_ui_utils_getAlignment(i)&&N!==T&&s.reference[v]/2-(N<A?A:R)-y[v]/2<0,k=O?N<A?N-A:N-P:0;return{[h]:g[h]+k,data:{[h]:T,centerOffset:N-T-k,...O&&{alignmentOffset:k}},reset:O}}}),floating_ui_dom_computePosition=(e,t,n)=>{let o=new Map,i={platform:R,...n},l={...i.platform,_c:o};return computePosition(e,t,{...i,platform:l})};var P=n(4887),N="undefined"!=typeof document?o.useLayoutEffect:function(){};function deepEqual(e,t){let n,o,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(o=n;0!=o--;)if(!deepEqual(e[o],t[o]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(o=n;0!=o--;)if(!({}).hasOwnProperty.call(t,i[o]))return!1;for(o=n;0!=o--;){let n=i[o];if(("_owner"!==n||!e.$$typeof)&&!deepEqual(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function getDPR(e){if("undefined"==typeof window)return 1;let t=e.ownerDocument.defaultView||window;return t.devicePixelRatio||1}function roundByDPR(e,t){let n=getDPR(e);return Math.round(t*n)/n}function useLatestRef(e){let t=o.useRef(e);return N(()=>{t.current=e}),t}let arrow$1=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:o}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?floating_ui_dom_arrow({element:n.current,padding:o}).fn(t):{}:n?floating_ui_dom_arrow({element:n,padding:o}).fn(t):{}}}),floating_ui_react_dom_offset=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,o;let{x:i,y:l,placement:a,middlewareData:s}=e,u=await convertValueToCoords(e,n);return a===(null==(t=s.offset)?void 0:t.placement)&&null!=(o=s.arrow)&&o.alignmentOffset?{}:{x:i+u.x,y:l+u.y,data:{...u,placement:a}}}}),options:[e,t]}},floating_ui_react_dom_shift=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:o,placement:i}=e,{mainAxis:s=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...d}=floating_ui_utils_evaluate(n,e),f={x:t,y:o},p=await detectOverflow(e,d),m=floating_ui_utils_getSideAxis(floating_ui_utils_getSide(i)),g=getOppositeAxis(m),h=f[g],v=f[m];if(s){let e=h+p["y"===g?"top":"left"],t=h-p["y"===g?"bottom":"right"];h=a(e,l(h,t))}if(u){let e="y"===m?"top":"left",t="y"===m?"bottom":"right",n=v+p[e],o=v-p[t];v=a(n,l(v,o))}let y=c.fn({...e,[g]:h,[m]:v});return{...y,data:{x:y.x-t,y:y.y-o,enabled:{[g]:s,[m]:u}}}}}),options:[e,t]}},floating_ui_react_dom_limitShift=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:o,placement:i,rects:l,middlewareData:a}=e,{offset:s=0,mainAxis:u=!0,crossAxis:c=!0}=floating_ui_utils_evaluate(n,e),d={x:t,y:o},f=floating_ui_utils_getSideAxis(i),p=getOppositeAxis(f),m=d[p],g=d[f],h=floating_ui_utils_evaluate(s,e),y="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(u){let e="y"===p?"height":"width",t=l.reference[p]-l.floating[e]+y.mainAxis,n=l.reference[p]+l.reference[e]-y.mainAxis;m<t?m=t:m>n&&(m=n)}if(c){var b,w;let e="y"===p?"width":"height",t=v.has(floating_ui_utils_getSide(i)),n=l.reference[f]-l.floating[e]+(t&&(null==(b=a.offset)?void 0:b[f])||0)+(t?0:y.crossAxis),o=l.reference[f]+l.reference[e]+(t?0:(null==(w=a.offset)?void 0:w[f])||0)-(t?y.crossAxis:0);g<n?g=n:g>o&&(g=o)}return{[p]:m,[f]:g}}}),options:[e,t]}},floating_ui_react_dom_flip=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,o,i,l,a;let{placement:s,middlewareData:u,rects:c,initialPlacement:d,platform:f,elements:v}=e,{mainAxis:y=!0,crossAxis:b=!0,fallbackPlacements:w,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:_=!0,...E}=floating_ui_utils_evaluate(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let C=floating_ui_utils_getSide(s),A=floating_ui_utils_getSideAxis(d),R=floating_ui_utils_getSide(d)===d,P=await (null==f.isRTL?void 0:f.isRTL(v.floating)),N=w||(R||!_?[getOppositePlacement(d)]:function(e){let t=getOppositePlacement(e);return[floating_ui_utils_getOppositeAlignmentPlacement(e),t,floating_ui_utils_getOppositeAlignmentPlacement(t)]}(d)),T="none"!==S;!w&&T&&N.push(...function(e,t,n,o){let i=floating_ui_utils_getAlignment(e),l=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?m:p;return t?p:m;case"left":case"right":return t?g:h;default:return[]}}(floating_ui_utils_getSide(e),"start"===n,o);return i&&(l=l.map(e=>e+"-"+i),t&&(l=l.concat(l.map(floating_ui_utils_getOppositeAlignmentPlacement)))),l}(d,_,S,P));let O=[d,...N],k=await detectOverflow(e,E),L=[],D=(null==(o=u.flip)?void 0:o.overflows)||[];if(y&&L.push(k[C]),b){let e=function(e,t,n){void 0===n&&(n=!1);let o=floating_ui_utils_getAlignment(e),i=getOppositeAxis(floating_ui_utils_getSideAxis(e)),l=getAxisLength(i),a="x"===i?o===(n?"end":"start")?"right":"left":"start"===o?"bottom":"top";return t.reference[l]>t.floating[l]&&(a=getOppositePlacement(a)),[a,getOppositePlacement(a)]}(s,c,P);L.push(k[e[0]],k[e[1]])}if(D=[...D,{placement:s,overflows:L}],!L.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=O[e];if(t){let n="alignment"===b&&A!==floating_ui_utils_getSideAxis(t);if(!n||D.every(e=>e.overflows[0]>0&&floating_ui_utils_getSideAxis(e.placement)===A))return{data:{index:e,overflows:D},reset:{placement:t}}}let n=null==(l=D.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:l.placement;if(!n)switch(x){case"bestFit":{let e=null==(a=D.filter(e=>{if(T){let t=floating_ui_utils_getSideAxis(e.placement);return t===A||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=d}if(s!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},floating_ui_react_dom_size=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,o;let i,s;let{placement:u,rects:c,platform:d,elements:f}=e,{apply:p=()=>{},...m}=floating_ui_utils_evaluate(n,e),g=await detectOverflow(e,m),h=floating_ui_utils_getSide(u),v=floating_ui_utils_getAlignment(u),y="y"===floating_ui_utils_getSideAxis(u),{width:b,height:w}=c.floating;"top"===h||"bottom"===h?(i=h,s=v===(await (null==d.isRTL?void 0:d.isRTL(f.floating))?"start":"end")?"left":"right"):(s=h,i="end"===v?"top":"bottom");let x=w-g.top-g.bottom,S=b-g.left-g.right,_=l(w-g[i],x),E=l(b-g[s],S),C=!e.middlewareData.shift,A=_,R=E;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(R=S),null!=(o=e.middlewareData.shift)&&o.enabled.y&&(A=x),C&&!v){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),o=a(g.bottom,0);y?R=b-2*(0!==e||0!==t?e+t:a(g.left,g.right)):A=w-2*(0!==n||0!==o?n+o:a(g.top,g.bottom))}await p({...e,availableWidth:R,availableHeight:A});let P=await d.getDimensions(f.floating);return b!==P.width||w!==P.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},floating_ui_react_dom_hide=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:o="referenceHidden",...i}=floating_ui_utils_evaluate(n,e);switch(o){case"referenceHidden":{let n=await detectOverflow(e,{...i,elementContext:"reference"}),o=getSideOffsets(n,t.reference);return{data:{referenceHiddenOffsets:o,referenceHidden:isAnySideFullyClipped(o)}}}case"escaped":{let n=await detectOverflow(e,{...i,altBoundary:!0}),o=getSideOffsets(n,t.floating);return{data:{escapedOffsets:o,escaped:isAnySideFullyClipped(o)}}}default:return{}}}}),options:[e,t]}},floating_ui_react_dom_arrow=(e,t)=>({...arrow$1(e),options:[e,t]});var T=n(9790),O=n(7437),k=o.forwardRef((e,t)=>{let{children:n,width:o=10,height:i=5,...l}=e;return(0,O.jsx)(T.WV.svg,{...l,ref:t,width:o,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,O.jsx)("polygon",{points:"0,0 30,0 15,10"})})});k.displayName="Arrow";var L=n(2210),D=n(6459),M=n(1030),W="Popper",[j,I]=function(e,t=[]){let n=[],createScope=()=>{let t=n.map(e=>o.createContext(e));return function(n){let i=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:i}}),[n,i])}};return createScope.scopeName=e,[function(t,i){let l=o.createContext(i),a=n.length;function Provider(t){let{scope:n,children:i,...s}=t,u=n?.[e][a]||l,c=o.useMemo(()=>s,Object.values(s));return(0,O.jsx)(u.Provider,{value:c,children:i})}return n=[...n,i],Provider.displayName=t+"Provider",[Provider,function(n,s){let u=s?.[e][a]||l,c=o.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let createScope=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=n.reduce((t,{useScope:n,scopeName:o})=>{let i=n(e),l=i[`__scope${o}`];return{...t,...l}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return createScope.scopeName=t.scopeName,createScope}(createScope,...t)]}(W),[V,F]=j(W),Popper=e=>{let{__scopePopper:t,children:n}=e,[i,l]=o.useState(null);return(0,O.jsx)(V,{scope:t,anchor:i,onAnchorChange:l,children:n})};Popper.displayName=W;var B="PopperAnchor",z=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...l}=e,a=F(B,n),s=o.useRef(null),u=(0,L.e)(t,s);return o.useEffect(()=>{a.onAnchorChange(i?.current||s.current)}),i?null:(0,O.jsx)(T.WV.div,{...l,ref:u})});z.displayName=B;var H="PopperContent",[$,U]=j(H),Z=o.forwardRef((e,t)=>{let{__scopePopper:n,side:i="bottom",sideOffset:s=0,align:c="center",alignOffset:d=0,arrowPadding:f=0,avoidCollisions:p=!0,collisionBoundary:m=[],collisionPadding:g=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:y="optimized",onPlaced:b,...w}=e,x=F(H,n),[S,_]=o.useState(null),E=(0,L.e)(t,e=>_(e)),[C,A]=o.useState(null),R=function(e){let[t,n]=o.useState(void 0);return(0,M.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let o,i;if(!Array.isArray(t)||!t.length)return;let l=t[0];if("borderBoxSize"in l){let e=l.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else o=e.offsetWidth,i=e.offsetHeight;n({width:o,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(C),k=R?.width??0,W=R?.height??0,j="number"==typeof g?g:{top:0,right:0,bottom:0,left:0,...g},I=Array.isArray(m)?m:[m],V=I.length>0,B={padding:j,boundary:I.filter(isNotNull),altBoundary:V},{refs:z,floatingStyles:U,placement:Z,isPositioned:K,middlewareData:X}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:l,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:d}=e,[f,p]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,g]=o.useState(i);deepEqual(m,i)||g(i);let[h,v]=o.useState(null),[y,b]=o.useState(null),w=o.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),x=o.useCallback(e=>{e!==C.current&&(C.current=e,b(e))},[]),S=a||h,_=s||y,E=o.useRef(null),C=o.useRef(null),A=o.useRef(f),R=null!=c,T=useLatestRef(c),O=useLatestRef(l),k=useLatestRef(d),L=o.useCallback(()=>{if(!E.current||!C.current)return;let e={placement:t,strategy:n,middleware:m};O.current&&(e.platform=O.current),floating_ui_dom_computePosition(E.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};D.current&&!deepEqual(A.current,t)&&(A.current=t,P.flushSync(()=>{p(t)}))})},[m,t,n,O,k]);N(()=>{!1===d&&A.current.isPositioned&&(A.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let D=o.useRef(!1);N(()=>(D.current=!0,()=>{D.current=!1}),[]),N(()=>{if(S&&(E.current=S),_&&(C.current=_),S&&_){if(T.current)return T.current(S,_,L);L()}},[S,_,L,T,R]);let M=o.useMemo(()=>({reference:E,floating:C,setReference:w,setFloating:x}),[w,x]),W=o.useMemo(()=>({reference:S,floating:_}),[S,_]),j=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!W.floating)return e;let t=roundByDPR(W.floating,f.x),o=roundByDPR(W.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+o+"px)",...getDPR(W.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:o}},[n,u,W.floating,f.x,f.y]);return o.useMemo(()=>({...f,update:L,refs:M,elements:W,floatingStyles:j}),[f,L,M,W,j])}({strategy:"fixed",placement:i+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>{let t=function(e,t,n,o){let i;void 0===o&&(o={});let{ancestorScroll:s=!0,ancestorResize:c=!0,elementResize:d="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:p=!1}=o,m=unwrapElement(e),g=s||c?[...m?getOverflowAncestors(m):[],...getOverflowAncestors(t)]:[];g.forEach(e=>{s&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let h=m&&f?function(e,t){let n,o=null,i=getDocumentElement(e);function cleanup(){var e;clearTimeout(n),null==(e=o)||e.disconnect(),o=null}return!function refresh(s,c){void 0===s&&(s=!1),void 0===c&&(c=1),cleanup();let d=e.getBoundingClientRect(),{left:f,top:p,width:m,height:g}=d;if(s||t(),!m||!g)return;let h=u(p),v=u(i.clientWidth-(f+m)),y=u(i.clientHeight-(p+g)),b=u(f),w={rootMargin:-h+"px "+-v+"px "+-y+"px "+-b+"px",threshold:a(0,l(1,c))||1},x=!0;function handleObserve(t){let o=t[0].intersectionRatio;if(o!==c){if(!x)return refresh();o?refresh(!1,o):n=setTimeout(()=>{refresh(!1,1e-7)},1e3)}1!==o||rectsAreEqual(d,e.getBoundingClientRect())||refresh(),x=!1}try{o=new IntersectionObserver(handleObserve,{...w,root:i.ownerDocument})}catch(e){o=new IntersectionObserver(handleObserve,w)}o.observe(e)}(!0),cleanup}(m,n):null,v=-1,y=null;d&&(y=new ResizeObserver(e=>{let[o]=e;o&&o.target===m&&y&&(y.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),m&&!p&&y.observe(m),y.observe(t));let b=p?getBoundingClientRect(e):null;return p&&function frameLoop(){let t=getBoundingClientRect(e);b&&!rectsAreEqual(b,t)&&n(),b=t,i=requestAnimationFrame(frameLoop)}(),n(),()=>{var e;g.forEach(e=>{s&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==h||h(),null==(e=y)||e.disconnect(),y=null,p&&cancelAnimationFrame(i)}}(...e,{animationFrame:"always"===y});return t},elements:{reference:x.anchor},middleware:[floating_ui_react_dom_offset({mainAxis:s+W,alignmentAxis:d}),p&&floating_ui_react_dom_shift({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?floating_ui_react_dom_limitShift():void 0,...B}),p&&floating_ui_react_dom_flip({...B}),floating_ui_react_dom_size({...B,apply:({elements:e,rects:t,availableWidth:n,availableHeight:o})=>{let{width:i,height:l}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${o}px`),a.setProperty("--radix-popper-anchor-width",`${i}px`),a.setProperty("--radix-popper-anchor-height",`${l}px`)}}),C&&floating_ui_react_dom_arrow({element:C,padding:f}),transformOrigin({arrowWidth:k,arrowHeight:W}),v&&floating_ui_react_dom_hide({strategy:"referenceHidden",...B})]}),[Y,G]=getSideAndAlignFromPlacement(Z),J=(0,D.W)(b);(0,M.b)(()=>{K&&J?.()},[K,J]);let Q=X.arrow?.x,ee=X.arrow?.y,et=X.arrow?.centerOffset!==0,[er,en]=o.useState();return(0,M.b)(()=>{S&&en(window.getComputedStyle(S).zIndex)},[S]),(0,O.jsx)("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:K?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[X.transformOrigin?.x,X.transformOrigin?.y].join(" "),...X.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,O.jsx)($,{scope:n,placedSide:Y,onArrowChange:A,arrowX:Q,arrowY:ee,shouldHideArrow:et,children:(0,O.jsx)(T.WV.div,{"data-side":Y,"data-align":G,...w,ref:E,style:{...w.style,animation:K?void 0:"none"}})})})});Z.displayName=H;var K="PopperArrow",X={top:"bottom",right:"left",bottom:"top",left:"right"},Y=o.forwardRef(function(e,t){let{__scopePopper:n,...o}=e,i=U(K,n),l=X[i.placedSide];return(0,O.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[l]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,O.jsx)(k,{...o,ref:t,style:{...o.style,display:"block"}})})});function isNotNull(e){return null!==e}Y.displayName=K;var transformOrigin=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:o,middlewareData:i}=t,l=i.arrow?.centerOffset!==0,a=l?0:e.arrowWidth,s=l?0:e.arrowHeight,[u,c]=getSideAndAlignFromPlacement(n),d={start:"0%",center:"50%",end:"100%"}[c],f=(i.arrow?.x??0)+a/2,p=(i.arrow?.y??0)+s/2,m="",g="";return"bottom"===u?(m=l?d:`${f}px`,g=`${-s}px`):"top"===u?(m=l?d:`${f}px`,g=`${o.floating.height+s}px`):"right"===u?(m=`${-s}px`,g=l?d:`${p}px`):"left"===u&&(m=`${o.floating.width+s}px`,g=l?d:`${p}px`),{data:{x:m,y:g}}}});function getSideAndAlignFromPlacement(e){let[t,n="center"]=e.split("-");return[t,n]}var G=Popper,J=z,Q=Z,ee=Y},9790:function(e,t,n){n.d(t,{WV:function(){return s},jH:function(){return dispatchDiscreteCustomEvent}});var o=n(2265),i=n(4887),l=n(7256),a=n(7437),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=o.forwardRef((e,n)=>{let{asChild:o,...i}=e,s=o?l.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(s,{...i,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function dispatchDiscreteCustomEvent(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},6239:function(e,t,n){let o;n.d(t,{VY:function(){return eq},ZA:function(){return eQ},JO:function(){return eY},ck:function(){return e1},wU:function(){return e5},eT:function(){return e2},__:function(){return e0},h_:function(){return eG},fC:function(){return eZ},$G:function(){return e7},u_:function(){return e6},Z0:function(){return e4},xz:function(){return eK},B4:function(){return eX},l_:function(){return eJ}});var i,l=n(2265),a=n(4887);function clamp(e,[t,n]){return Math.min(n,Math.max(t,e))}var s=n(5744),u=n(3651),c=n(2210),d=n(6989),f=n(7437),p=l.createContext(void 0),m=n(9790),g=n(6459),h="dismissableLayer.update",v=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=l.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:u,onInteractOutside:d,onDismiss:p,...y}=e,b=l.useContext(v),[w,x]=l.useState(null),S=w?.ownerDocument??globalThis?.document,[,_]=l.useState({}),E=(0,c.e)(t,e=>x(e)),C=Array.from(b.layers),[A]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),R=C.indexOf(A),P=w?C.indexOf(w):-1,N=b.layersWithOutsidePointerEventsDisabled.size>0,T=P>=R,O=function(e,t=globalThis?.document){let n=(0,g.W)(e),o=l.useRef(!1),i=l.useRef(()=>{});return l.useEffect(()=>{let handlePointerDown=e=>{if(e.target&&!o.current){let handleAndDispatchPointerDownOutsideEvent2=function(){handleAndDispatchCustomEvent("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=handleAndDispatchPointerDownOutsideEvent2,t.addEventListener("click",i.current,{once:!0})):handleAndDispatchPointerDownOutsideEvent2()}else t.removeEventListener("click",i.current);o.current=!1},e=window.setTimeout(()=>{t.addEventListener("pointerdown",handlePointerDown)},0);return()=>{window.clearTimeout(e),t.removeEventListener("pointerdown",handlePointerDown),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));!T||n||(a?.(e),d?.(e),e.defaultPrevented||p?.())},S),k=function(e,t=globalThis?.document){let n=(0,g.W)(e),o=l.useRef(!1);return l.useEffect(()=>{let handleFocus=e=>{e.target&&!o.current&&handleAndDispatchCustomEvent("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",handleFocus),()=>t.removeEventListener("focusin",handleFocus)},[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target,n=[...b.branches].some(e=>e.contains(t));n||(u?.(e),d?.(e),e.defaultPrevented||p?.())},S);return!function(e,t=globalThis?.document){let n=(0,g.W)(e);l.useEffect(()=>{let handleKeyDown=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",handleKeyDown,{capture:!0}),()=>t.removeEventListener("keydown",handleKeyDown,{capture:!0})},[n,t])}(e=>{let t=P===b.layers.size-1;t&&(o?.(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},S),l.useEffect(()=>{if(w)return n&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(i=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(w)),b.layers.add(w),dispatchUpdate(),()=>{n&&1===b.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=i)}},[w,S,n,b]),l.useEffect(()=>()=>{w&&(b.layers.delete(w),b.layersWithOutsidePointerEventsDisabled.delete(w),dispatchUpdate())},[w,b]),l.useEffect(()=>{let handleUpdate=()=>_({});return document.addEventListener(h,handleUpdate),()=>document.removeEventListener(h,handleUpdate)},[]),(0,f.jsx)(m.WV.div,{...y,ref:E,style:{pointerEvents:N?T?"auto":"none":void 0,...e.style},onFocusCapture:(0,s.M)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,s.M)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,s.M)(e.onPointerDownCapture,O.onPointerDownCapture)})});function dispatchUpdate(){let e=new CustomEvent(h);document.dispatchEvent(e)}function handleAndDispatchCustomEvent(e,t,n,{discrete:o}){let i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,m.jH)(i,l):i.dispatchEvent(l)}y.displayName="DismissableLayer",l.forwardRef((e,t)=>{let n=l.useContext(v),o=l.useRef(null),i=(0,c.e)(t,o);return l.useEffect(()=>{let e=o.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,f.jsx)(m.WV.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var b=0;function createFocusGuard(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var w="focusScope.autoFocusOnMount",x="focusScope.autoFocusOnUnmount",S={bubbles:!1,cancelable:!0},_=l.forwardRef((e,t)=>{let{loop:n=!1,trapped:o=!1,onMountAutoFocus:i,onUnmountAutoFocus:a,...s}=e,[u,d]=l.useState(null),p=(0,g.W)(i),h=(0,g.W)(a),v=l.useRef(null),y=(0,c.e)(t,e=>d(e)),b=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(o){let handleFocusIn2=function(e){if(b.paused||!u)return;let t=e.target;u.contains(t)?v.current=t:dist_focus(v.current,{select:!0})},handleFocusOut2=function(e){if(b.paused||!u)return;let t=e.relatedTarget;null===t||u.contains(t)||dist_focus(v.current,{select:!0})};document.addEventListener("focusin",handleFocusIn2),document.addEventListener("focusout",handleFocusOut2);let e=new MutationObserver(function(e){let t=document.activeElement;if(t===document.body)for(let t of e)t.removedNodes.length>0&&dist_focus(u)});return u&&e.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",handleFocusIn2),document.removeEventListener("focusout",handleFocusOut2),e.disconnect()}}},[o,u,b.paused]),l.useEffect(()=>{if(u){E.add(b);let e=document.activeElement,t=u.contains(e);if(!t){let t=new CustomEvent(w,S);u.addEventListener(w,p),u.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let o of e)if(dist_focus(o,{select:t}),document.activeElement!==n)return}(getTabbableCandidates(u).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&dist_focus(u))}return()=>{u.removeEventListener(w,p),setTimeout(()=>{let t=new CustomEvent(x,S);u.addEventListener(x,h),u.dispatchEvent(t),t.defaultPrevented||dist_focus(e??document.body,{select:!0}),u.removeEventListener(x,h),E.remove(b)},0)}}},[u,p,h,b]);let _=l.useCallback(e=>{if(!n&&!o||b.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[o,l]=function(e){let t=getTabbableCandidates(e),n=findVisible(t,e),o=findVisible(t.reverse(),e);return[n,o]}(t),a=o&&l;a?e.shiftKey||i!==l?e.shiftKey&&i===o&&(e.preventDefault(),n&&dist_focus(l,{select:!0})):(e.preventDefault(),n&&dist_focus(o,{select:!0})):i===t&&e.preventDefault()}},[n,o,b.paused]);return(0,f.jsx)(m.WV.div,{tabIndex:-1,...s,ref:y,onKeyDown:_})});function getTabbableCandidates(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function findVisible(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function dist_focus(e,{select:t=!1}={}){if(e&&e.focus){var n;let o=document.activeElement;e.focus({preventScroll:!0}),e!==o&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}_.displayName="FocusScope";var E=(o=[],{add(e){let t=o[0];e!==t&&t?.pause(),(o=arrayRemove(o,e)).unshift(e)},remove(e){o=arrayRemove(o,e),o[0]?.resume()}});function arrayRemove(e,t){let n=[...e],o=n.indexOf(t);return -1!==o&&n.splice(o,1),n}var C=n(966),A=n(8452),R=n(1030),P=l.forwardRef((e,t)=>{let{container:n,...o}=e,[i,s]=l.useState(!1);(0,R.b)(()=>s(!0),[]);let u=n||i&&globalThis?.document?.body;return u?a.createPortal((0,f.jsx)(m.WV.div,{...o,ref:t}),u):null});P.displayName="Portal";var N=n(7256),T=n(3763),O=n(8281),k=n(5859),L=n(44),D=n(5322),M=n(5835),W=(0,n(8427)._)(),nothing=function(){},j=l.forwardRef(function(e,t){var n=l.useRef(null),o=l.useState({onScrollCapture:nothing,onWheelCapture:nothing,onTouchMoveCapture:nothing}),i=o[0],a=o[1],s=e.forwardProps,u=e.children,c=e.className,d=e.removeScrollBar,f=e.enabled,p=e.shards,m=e.sideCar,g=e.noIsolation,h=e.inert,v=e.allowPinchZoom,y=e.as,b=void 0===y?"div":y,w=e.gapMode,x=(0,L._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(0,M.q)([n,t]),_=(0,L.pi)((0,L.pi)({},x),i);return l.createElement(l.Fragment,null,f&&l.createElement(m,{sideCar:W,removeScrollBar:d,shards:p,noIsolation:g,inert:h,setCallbacks:a,allowPinchZoom:!!v,lockRef:n,gapMode:w}),s?l.cloneElement(l.Children.only(u),(0,L.pi)((0,L.pi)({},_),{ref:S})):l.createElement(b,(0,L.pi)({},_,{className:c,ref:S}),u))});j.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},j.classNames={fullWidth:D.zi,zeroRight:D.pF};var I=n(6898),V=n(2776),F=n(8662),B=!1;if("undefined"!=typeof window)try{var z=Object.defineProperty({},"passive",{get:function(){return B=!0,!0}});window.addEventListener("test",z,z),window.removeEventListener("test",z,z)}catch(e){B=!1}var H=!!B&&{passive:!1},elementCanBeScrolled=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},locationCouldBeScrolled=function(e,t){var n=t.ownerDocument,o=t;do{if("undefined"!=typeof ShadowRoot&&o instanceof ShadowRoot&&(o=o.host),elementCouldBeScrolled(e,o)){var i=getScrollVariables(e,o);if(i[1]>i[2])return!0}o=o.parentNode}while(o&&o!==n.body);return!1},elementCouldBeScrolled=function(e,t){return"v"===e?elementCanBeScrolled(t,"overflowY"):elementCanBeScrolled(t,"overflowX")},getScrollVariables=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},handleScroll=function(e,t,n,o,i){var l,a=(l=window.getComputedStyle(t).direction,"h"===e&&"rtl"===l?-1:1),s=a*o,u=n.target,c=t.contains(u),d=!1,f=s>0,p=0,m=0;do{var g=getScrollVariables(e,u),h=g[0],v=g[1]-g[2]-a*h;(h||v)&&elementCouldBeScrolled(e,u)&&(p+=v,m+=h),u=u instanceof ShadowRoot?u.host:u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return f&&(i&&1>Math.abs(p)||!i&&s>p)?d=!0:!f&&(i&&1>Math.abs(m)||!i&&-s>m)&&(d=!0),d},getTouchXY=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},getDeltaXY=function(e){return[e.deltaX,e.deltaY]},extractRef=function(e){return e&&"current"in e?e.current:e},$=0,U=[],Z=(0,I.L)(W,function(e){var t=l.useRef([]),n=l.useRef([0,0]),o=l.useRef(),i=l.useState($++)[0],a=l.useState(F.Ws)[0],s=l.useRef(e);l.useEffect(function(){s.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(0,L.ev)([e.lockRef.current],(e.shards||[]).map(extractRef),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var i,l=getTouchXY(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-l[0],c="deltaY"in e?e.deltaY:a[1]-l[1],d=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===d.type)return!1;var p=locationCouldBeScrolled(f,d);if(!p)return!0;if(p?i=f:(i="v"===f?"h":"v",p=locationCouldBeScrolled(f,d)),!p)return!1;if(!o.current&&"changedTouches"in e&&(u||c)&&(o.current=i),!i)return!0;var m=o.current||i;return handleScroll(m,t,e,"h"===m?u:c,!0)},[]),c=l.useCallback(function(e){if(U.length&&U[U.length-1]===a){var n="deltaY"in e?getDeltaXY(e):getTouchXY(e),o=t.current.filter(function(t){var o;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(o=t.delta)[0]===n[0]&&o[1]===n[1]})[0];if(o&&o.should){e.cancelable&&e.preventDefault();return}if(!o){var i=(s.current.shards||[]).map(extractRef).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?u(e,i[0]):!s.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=l.useCallback(function(e,n,o,i){var l={name:e,delta:n,target:o,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(o)};t.current.push(l),setTimeout(function(){t.current=t.current.filter(function(e){return e!==l})},1)},[]),f=l.useCallback(function(e){n.current=getTouchXY(e),o.current=void 0},[]),p=l.useCallback(function(t){d(t.type,getDeltaXY(t),t.target,u(t,e.lockRef.current))},[]),m=l.useCallback(function(t){d(t.type,getTouchXY(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return U.push(a),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:m}),document.addEventListener("wheel",c,H),document.addEventListener("touchmove",c,H),document.addEventListener("touchstart",f,H),function(){U=U.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,H),document.removeEventListener("touchmove",c,H),document.removeEventListener("touchstart",f,H)}},[]);var g=e.removeScrollBar,h=e.inert;return l.createElement(l.Fragment,null,h?l.createElement(a,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,g?l.createElement(V.jp,{gapMode:e.gapMode}):null)}),K=l.forwardRef(function(e,t){return l.createElement(j,(0,L.pi)({},e,{ref:t,sideCar:Z}))});K.classNames=j.classNames;var X=[" ","Enter","ArrowUp","ArrowDown"],Y=[" ","Enter"],G="Select",[J,Q,ee]=(0,u.B)(G),[et,er]=(0,d.b)(G,[ee,A.D7]),en=(0,A.D7)(),[eo,ei]=et(G),[el,ea]=et(G),Select=e=>{let{__scopeSelect:t,children:n,open:o,defaultOpen:i,onOpenChange:a,value:s,defaultValue:u,onValueChange:c,dir:d,name:m,autoComplete:g,disabled:h,required:v,form:y}=e,b=en(t),[w,x]=l.useState(null),[S,_]=l.useState(null),[E,R]=l.useState(!1),P=function(e){let t=l.useContext(p);return e||t||"ltr"}(d),[N=!1,O]=(0,T.T)({prop:o,defaultProp:i,onChange:a}),[k,L]=(0,T.T)({prop:s,defaultProp:u,onChange:c}),D=l.useRef(null),M=!w||y||!!w.closest("form"),[W,j]=l.useState(new Set),I=Array.from(W).map(e=>e.props.value).join(";");return(0,f.jsx)(A.fC,{...b,children:(0,f.jsxs)(eo,{required:v,scope:t,trigger:w,onTriggerChange:x,valueNode:S,onValueNodeChange:_,valueNodeHasChildren:E,onValueNodeHasChildrenChange:R,contentId:(0,C.M)(),value:k,onValueChange:L,open:N,onOpenChange:O,dir:P,triggerPointerDownPosRef:D,disabled:h,children:[(0,f.jsx)(J.Provider,{scope:t,children:(0,f.jsx)(el,{scope:e.__scopeSelect,onNativeOptionAdd:l.useCallback(e=>{j(t=>new Set(t).add(e))},[]),onNativeOptionRemove:l.useCallback(e=>{j(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),M?(0,f.jsxs)(eU,{"aria-hidden":!0,required:v,tabIndex:-1,name:m,autoComplete:g,value:k,onChange:e=>L(e.target.value),disabled:h,form:y,children:[void 0===k?(0,f.jsx)("option",{value:""}):null,Array.from(W)]},I):null]})})};Select.displayName=G;var es="SelectTrigger",eu=l.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:o=!1,...i}=e,a=en(n),u=ei(es,n),d=u.disabled||o,p=(0,c.e)(t,u.onTriggerChange),g=Q(n),h=l.useRef("touch"),[v,y,b]=useTypeaheadSearch(e=>{let t=g().filter(e=>!e.disabled),n=t.find(e=>e.value===u.value),o=findNextItem(t,e,n);void 0!==o&&u.onValueChange(o.value)}),handleOpen=e=>{d||(u.onOpenChange(!0),b()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,f.jsx)(A.ee,{asChild:!0,...a,children:(0,f.jsx)(m.WV.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":shouldShowPlaceholder(u.value)?"":void 0,...i,ref:p,onClick:(0,s.M)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&handleOpen(e)}),onPointerDown:(0,s.M)(i.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(handleOpen(e),e.preventDefault())}),onKeyDown:(0,s.M)(i.onKeyDown,e=>{let t=""!==v.current,n=e.ctrlKey||e.altKey||e.metaKey;n||1!==e.key.length||y(e.key),(!t||" "!==e.key)&&X.includes(e.key)&&(handleOpen(),e.preventDefault())})})})});eu.displayName=es;var ec="SelectValue",ed=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:o,style:i,children:l,placeholder:a="",...s}=e,u=ei(ec,n),{onValueNodeHasChildrenChange:d}=u,p=void 0!==l,g=(0,c.e)(t,u.onValueNodeChange);return(0,R.b)(()=>{d(p)},[d,p]),(0,f.jsx)(m.WV.span,{...s,ref:g,style:{pointerEvents:"none"},children:shouldShowPlaceholder(u.value)?(0,f.jsx)(f.Fragment,{children:a}):l})});ed.displayName=ec;var ef=l.forwardRef((e,t)=>{let{__scopeSelect:n,children:o,...i}=e;return(0,f.jsx)(m.WV.span,{"aria-hidden":!0,...i,ref:t,children:o||"▼"})});ef.displayName="SelectIcon";var SelectPortal=e=>(0,f.jsx)(P,{asChild:!0,...e});SelectPortal.displayName="SelectPortal";var ep="SelectContent",em=l.forwardRef((e,t)=>{let n=ei(ep,e.__scopeSelect),[o,i]=l.useState();return((0,R.b)(()=>{i(new DocumentFragment)},[]),n.open)?(0,f.jsx)(ev,{...e,ref:t}):o?a.createPortal((0,f.jsx)(eg,{scope:e.__scopeSelect,children:(0,f.jsx)(J.Slot,{scope:e.__scopeSelect,children:(0,f.jsx)("div",{children:e.children})})}),o):null});em.displayName=ep;var[eg,eh]=et(ep),ev=l.forwardRef((e,t)=>{let{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:a,onPointerDownOutside:u,side:d,sideOffset:p,align:m,alignOffset:g,arrowPadding:h,collisionBoundary:v,collisionPadding:w,sticky:x,hideWhenDetached:S,avoidCollisions:E,...C}=e,A=ei(ep,n),[R,P]=l.useState(null),[T,O]=l.useState(null),L=(0,c.e)(t,e=>P(e)),[D,M]=l.useState(null),[W,j]=l.useState(null),I=Q(n),[V,F]=l.useState(!1),B=l.useRef(!1);l.useEffect(()=>{if(R)return(0,k.Ry)(R)},[R]),l.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??createFocusGuard()),document.body.insertAdjacentElement("beforeend",e[1]??createFocusGuard()),b++,()=>{1===b&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),b--}},[]);let z=l.useCallback(e=>{let[t,...n]=I().map(e=>e.ref.current),[o]=n.slice(-1),i=document.activeElement;for(let n of e)if(n===i||(n?.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===o&&T&&(T.scrollTop=T.scrollHeight),n?.focus(),document.activeElement!==i))return},[I,T]),H=l.useCallback(()=>z([D,R]),[z,D,R]);l.useEffect(()=>{V&&H()},[V,H]);let{onOpenChange:$,triggerPointerDownPosRef:U}=A;l.useEffect(()=>{if(R){let e={x:0,y:0},handlePointerMove=t=>{e={x:Math.abs(Math.round(t.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(U.current?.y??0))}},handlePointerUp=t=>{e.x<=10&&e.y<=10?t.preventDefault():R.contains(t.target)||$(!1),document.removeEventListener("pointermove",handlePointerMove),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",handlePointerMove),document.addEventListener("pointerup",handlePointerUp,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",handlePointerMove),document.removeEventListener("pointerup",handlePointerUp,{capture:!0})}}},[R,$,U]),l.useEffect(()=>{let close=()=>$(!1);return window.addEventListener("blur",close),window.addEventListener("resize",close),()=>{window.removeEventListener("blur",close),window.removeEventListener("resize",close)}},[$]);let[Z,X]=useTypeaheadSearch(e=>{let t=I().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),o=findNextItem(t,e,n);o&&setTimeout(()=>o.ref.current.focus())}),Y=l.useCallback((e,t,n)=>{let o=!B.current&&!n,i=void 0!==A.value&&A.value===t;(i||o)&&(M(e),o&&(B.current=!0))},[A.value]),G=l.useCallback(()=>R?.focus(),[R]),J=l.useCallback((e,t,n)=>{let o=!B.current&&!n,i=void 0!==A.value&&A.value===t;(i||o)&&j(e)},[A.value]),ee="popper"===o?eb:ey,et=ee===eb?{side:d,sideOffset:p,align:m,alignOffset:g,arrowPadding:h,collisionBoundary:v,collisionPadding:w,sticky:x,hideWhenDetached:S,avoidCollisions:E}:{};return(0,f.jsx)(eg,{scope:n,content:R,viewport:T,onViewportChange:O,itemRefCallback:Y,selectedItem:D,onItemLeave:G,itemTextRefCallback:J,focusSelectedItem:H,selectedItemText:W,position:o,isPositioned:V,searchRef:Z,children:(0,f.jsx)(K,{as:N.g7,allowPinchZoom:!0,children:(0,f.jsx)(_,{asChild:!0,trapped:A.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,s.M)(i,e=>{A.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,f.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>A.onOpenChange(!1),children:(0,f.jsx)(ee,{role:"listbox",id:A.contentId,"data-state":A.open?"open":"closed",dir:A.dir,onContextMenu:e=>e.preventDefault(),...C,...et,onPlaced:()=>F(!0),ref:L,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,s.M)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=I().filter(e=>!e.disabled),n=t.map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(n=n.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let t=e.target,o=n.indexOf(t);n=n.slice(o+1)}setTimeout(()=>z(n)),e.preventDefault()}})})})})})})});ev.displayName="SelectContentImpl";var ey=l.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:o,...i}=e,a=ei(ep,n),s=eh(ep,n),[u,d]=l.useState(null),[p,g]=l.useState(null),h=(0,c.e)(t,e=>g(e)),v=Q(n),y=l.useRef(!1),b=l.useRef(!0),{viewport:w,selectedItem:x,selectedItemText:S,focusSelectedItem:_}=s,E=l.useCallback(()=>{if(a.trigger&&a.valueNode&&u&&p&&w&&x&&S){let e=a.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),i=S.getBoundingClientRect();if("rtl"!==a.dir){let o=i.left-t.left,l=n.left-o,a=e.left-l,s=e.width+a,c=Math.max(s,t.width),d=window.innerWidth-10,f=clamp(l,[10,Math.max(10,d-c)]);u.style.minWidth=s+"px",u.style.left=f+"px"}else{let o=t.right-i.right,l=window.innerWidth-n.right-o,a=window.innerWidth-e.right-l,s=e.width+a,c=Math.max(s,t.width),d=window.innerWidth-10,f=clamp(l,[10,Math.max(10,d-c)]);u.style.minWidth=s+"px",u.style.right=f+"px"}let l=v(),s=window.innerHeight-20,c=w.scrollHeight,d=window.getComputedStyle(p),f=parseInt(d.borderTopWidth,10),m=parseInt(d.paddingTop,10),g=parseInt(d.borderBottomWidth,10),h=parseInt(d.paddingBottom,10),b=f+m+c+h+g,_=Math.min(5*x.offsetHeight,b),E=window.getComputedStyle(w),C=parseInt(E.paddingTop,10),A=parseInt(E.paddingBottom,10),R=e.top+e.height/2-10,P=x.offsetHeight/2,N=x.offsetTop+P,T=f+m+N;if(T<=R){let e=l.length>0&&x===l[l.length-1].ref.current;u.style.bottom="0px";let t=p.clientHeight-w.offsetTop-w.offsetHeight;u.style.height=T+Math.max(s-R,P+(e?A:0)+t+g)+"px"}else{let e=l.length>0&&x===l[0].ref.current;u.style.top="0px";let t=Math.max(R,f+w.offsetTop+(e?C:0)+P);u.style.height=t+(b-T)+"px",w.scrollTop=T-R+w.offsetTop}u.style.margin="10px 0",u.style.minHeight=_+"px",u.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>y.current=!0)}},[v,a.trigger,a.valueNode,u,p,w,x,S,a.dir,o]);(0,R.b)(()=>E(),[E]);let[C,A]=l.useState();(0,R.b)(()=>{p&&A(window.getComputedStyle(p).zIndex)},[p]);let P=l.useCallback(e=>{e&&!0===b.current&&(E(),_?.(),b.current=!1)},[E,_]);return(0,f.jsx)(ew,{scope:n,contentWrapper:u,shouldExpandOnScrollRef:y,onScrollButtonChange:P,children:(0,f.jsx)("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:(0,f.jsx)(m.WV.div,{...i,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});ey.displayName="SelectItemAlignedPosition";var eb=l.forwardRef((e,t)=>{let{__scopeSelect:n,align:o="start",collisionPadding:i=10,...l}=e,a=en(n);return(0,f.jsx)(A.VY,{...a,...l,ref:t,align:o,collisionPadding:i,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});eb.displayName="SelectPopperPosition";var[ew,ex]=et(ep,{}),eS="SelectViewport",e_=l.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:o,...i}=e,a=eh(eS,n),u=ex(eS,n),d=(0,c.e)(t,a.onViewportChange),p=l.useRef(0);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,f.jsx)(J.Slot,{scope:n,children:(0,f.jsx)(m.WV.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,s.M)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:o}=u;if(o?.current&&n){let e=Math.abs(p.current-t.scrollTop);if(e>0){let o=window.innerHeight-20,i=parseFloat(n.style.minHeight),l=parseFloat(n.style.height),a=Math.max(i,l);if(a<o){let i=a+e,l=Math.min(o,i),s=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=s>0?s:0,n.style.justifyContent="flex-end")}}}p.current=t.scrollTop})})})]})});e_.displayName=eS;var eE="SelectGroup",[eC,eA]=et(eE),eR=l.forwardRef((e,t)=>{let{__scopeSelect:n,...o}=e,i=(0,C.M)();return(0,f.jsx)(eC,{scope:n,id:i,children:(0,f.jsx)(m.WV.div,{role:"group","aria-labelledby":i,...o,ref:t})})});eR.displayName=eE;var eP="SelectLabel",eN=l.forwardRef((e,t)=>{let{__scopeSelect:n,...o}=e,i=eA(eP,n);return(0,f.jsx)(m.WV.div,{id:i.id,...o,ref:t})});eN.displayName=eP;var eT="SelectItem",[eO,ek]=et(eT),eL=l.forwardRef((e,t)=>{let{__scopeSelect:n,value:o,disabled:i=!1,textValue:a,...u}=e,d=ei(eT,n),p=eh(eT,n),g=d.value===o,[h,v]=l.useState(a??""),[y,b]=l.useState(!1),w=(0,c.e)(t,e=>p.itemRefCallback?.(e,o,i)),x=(0,C.M)(),S=l.useRef("touch"),handleSelect=()=>{i||(d.onValueChange(o),d.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,f.jsx)(eO,{scope:n,value:o,disabled:i,textId:x,isSelected:g,onItemTextChange:l.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,f.jsx)(J.ItemSlot,{scope:n,value:o,disabled:i,textValue:h,children:(0,f.jsx)(m.WV.div,{role:"option","aria-labelledby":x,"data-highlighted":y?"":void 0,"aria-selected":g&&y,"data-state":g?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...u,ref:w,onFocus:(0,s.M)(u.onFocus,()=>b(!0)),onBlur:(0,s.M)(u.onBlur,()=>b(!1)),onClick:(0,s.M)(u.onClick,()=>{"mouse"!==S.current&&handleSelect()}),onPointerUp:(0,s.M)(u.onPointerUp,()=>{"mouse"===S.current&&handleSelect()}),onPointerDown:(0,s.M)(u.onPointerDown,e=>{S.current=e.pointerType}),onPointerMove:(0,s.M)(u.onPointerMove,e=>{S.current=e.pointerType,i?p.onItemLeave?.():"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,s.M)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&p.onItemLeave?.()}),onKeyDown:(0,s.M)(u.onKeyDown,e=>{let t=p.searchRef?.current!=="";t&&" "===e.key||(Y.includes(e.key)&&handleSelect()," "===e.key&&e.preventDefault())})})})})});eL.displayName=eT;var eD="SelectItemText",eM=l.forwardRef((e,t)=>{let{__scopeSelect:n,className:o,style:i,...s}=e,u=ei(eD,n),d=eh(eD,n),p=ek(eD,n),g=ea(eD,n),[h,v]=l.useState(null),y=(0,c.e)(t,e=>v(e),p.onItemTextChange,e=>d.itemTextRefCallback?.(e,p.value,p.disabled)),b=h?.textContent,w=l.useMemo(()=>(0,f.jsx)("option",{value:p.value,disabled:p.disabled,children:b},p.value),[p.disabled,p.value,b]),{onNativeOptionAdd:x,onNativeOptionRemove:S}=g;return(0,R.b)(()=>(x(w),()=>S(w)),[x,S,w]),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(m.WV.span,{id:p.textId,...s,ref:y}),p.isSelected&&u.valueNode&&!u.valueNodeHasChildren?a.createPortal(s.children,u.valueNode):null]})});eM.displayName=eD;var eW="SelectItemIndicator",ej=l.forwardRef((e,t)=>{let{__scopeSelect:n,...o}=e,i=ek(eW,n);return i.isSelected?(0,f.jsx)(m.WV.span,{"aria-hidden":!0,...o,ref:t}):null});ej.displayName=eW;var eI="SelectScrollUpButton",eV=l.forwardRef((e,t)=>{let n=eh(eI,e.__scopeSelect),o=ex(eI,e.__scopeSelect),[i,a]=l.useState(!1),s=(0,c.e)(t,o.onScrollButtonChange);return(0,R.b)(()=>{if(n.viewport&&n.isPositioned){let handleScroll2=function(){let t=e.scrollTop>0;a(t)},e=n.viewport;return handleScroll2(),e.addEventListener("scroll",handleScroll2),()=>e.removeEventListener("scroll",handleScroll2)}},[n.viewport,n.isPositioned]),i?(0,f.jsx)(ez,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eV.displayName=eI;var eF="SelectScrollDownButton",eB=l.forwardRef((e,t)=>{let n=eh(eF,e.__scopeSelect),o=ex(eF,e.__scopeSelect),[i,a]=l.useState(!1),s=(0,c.e)(t,o.onScrollButtonChange);return(0,R.b)(()=>{if(n.viewport&&n.isPositioned){let handleScroll2=function(){let t=e.scrollHeight-e.clientHeight,n=Math.ceil(e.scrollTop)<t;a(n)},e=n.viewport;return handleScroll2(),e.addEventListener("scroll",handleScroll2),()=>e.removeEventListener("scroll",handleScroll2)}},[n.viewport,n.isPositioned]),i?(0,f.jsx)(ez,{...e,ref:s,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eB.displayName=eF;var ez=l.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:o,...i}=e,a=eh("SelectScrollButton",n),u=l.useRef(null),c=Q(n),d=l.useCallback(()=>{null!==u.current&&(window.clearInterval(u.current),u.current=null)},[]);return l.useEffect(()=>()=>d(),[d]),(0,R.b)(()=>{let e=c().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[c]),(0,f.jsx)(m.WV.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,s.M)(i.onPointerDown,()=>{null===u.current&&(u.current=window.setInterval(o,50))}),onPointerMove:(0,s.M)(i.onPointerMove,()=>{a.onItemLeave?.(),null===u.current&&(u.current=window.setInterval(o,50))}),onPointerLeave:(0,s.M)(i.onPointerLeave,()=>{d()})})}),eH=l.forwardRef((e,t)=>{let{__scopeSelect:n,...o}=e;return(0,f.jsx)(m.WV.div,{"aria-hidden":!0,...o,ref:t})});eH.displayName="SelectSeparator";var e$="SelectArrow";function shouldShowPlaceholder(e){return""===e||void 0===e}l.forwardRef((e,t)=>{let{__scopeSelect:n,...o}=e,i=en(n),l=ei(e$,n),a=eh(e$,n);return l.open&&"popper"===a.position?(0,f.jsx)(A.Eh,{...i,...o,ref:t}):null}).displayName=e$;var eU=l.forwardRef((e,t)=>{let{value:n,...o}=e,i=l.useRef(null),a=(0,c.e)(t,i),s=function(e){let t=l.useRef({value:e,previous:e});return l.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(n);return l.useEffect(()=>{let e=i.current,t=window.HTMLSelectElement.prototype,o=Object.getOwnPropertyDescriptor(t,"value"),l=o.set;if(s!==n&&l){let t=new Event("change",{bubbles:!0});l.call(e,n),e.dispatchEvent(t)}},[s,n]),(0,f.jsx)(O.T,{asChild:!0,children:(0,f.jsx)("select",{...o,ref:a,defaultValue:n})})});function useTypeaheadSearch(e){let t=(0,g.W)(e),n=l.useRef(""),o=l.useRef(0),i=l.useCallback(e=>{let i=n.current+e;t(i),function updateSearch(e){n.current=e,window.clearTimeout(o.current),""!==e&&(o.current=window.setTimeout(()=>updateSearch(""),1e3))}(i)},[t]),a=l.useCallback(()=>{n.current="",window.clearTimeout(o.current)},[]);return l.useEffect(()=>()=>window.clearTimeout(o.current),[]),[n,i,a]}function findNextItem(e,t,n){var o;let i=t.length>1&&Array.from(t).every(e=>e===t[0]),l=i?t[0]:t,a=n?e.indexOf(n):-1,s=(o=Math.max(a,0),e.map((t,n)=>e[(o+n)%e.length])),u=1===l.length;u&&(s=s.filter(e=>e!==n));let c=s.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return c!==n?c:void 0}eU.displayName="BubbleSelect";var eZ=Select,eK=eu,eX=ed,eY=ef,eG=SelectPortal,eq=em,eJ=e_,eQ=eR,e0=eN,e1=eL,e2=eM,e5=ej,e6=eV,e7=eB,e4=eH},7256:function(e,t,n){n.d(t,{A4:function(){return Slottable},g7:function(){return a}});var o=n(2265),i=n(2210),l=n(7437),a=o.forwardRef((e,t)=>{let{children:n,...i}=e,a=o.Children.toArray(n),u=a.find(isSlottable);if(u){let e=u.props.children,n=a.map(t=>t!==u?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,l.jsx)(s,{...i,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,l.jsx)(s,{...i,ref:t,children:n})});a.displayName="Slot";var s=o.forwardRef((e,t)=>{let{children:n,...l}=e;if(o.isValidElement(n)){let e,a;let s=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return o.cloneElement(n,{...function(e,t){let n={...t};for(let o in t){let i=e[o],l=t[o],a=/^on[A-Z]/.test(o);a?i&&l?n[o]=(...e)=>{l(...e),i(...e)}:i&&(n[o]=i):"style"===o?n[o]={...i,...l}:"className"===o&&(n[o]=[i,l].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props),ref:t?(0,i.F)(t,s):s})}return o.Children.count(n)>1?o.Children.only(null):null});s.displayName="SlotClone";var Slottable=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function isSlottable(e){return o.isValidElement(e)&&e.type===Slottable}},6459:function(e,t,n){n.d(t,{W:function(){return useCallbackRef}});var o=n(2265);function useCallbackRef(e){let t=o.useRef(e);return o.useEffect(()=>{t.current=e}),o.useMemo(()=>(...e)=>t.current?.(...e),[])}},3763:function(e,t,n){n.d(t,{T:function(){return useControllableState}});var o=n(2265),i=n(6459);function useControllableState({prop:e,defaultProp:t,onChange:n=()=>{}}){let[l,a]=function({defaultProp:e,onChange:t}){let n=o.useState(e),[l]=n,a=o.useRef(l),s=(0,i.W)(t);return o.useEffect(()=>{a.current!==l&&(s(l),a.current=l)},[l,a,s]),n}({defaultProp:t,onChange:n}),s=void 0!==e,u=s?e:l,c=(0,i.W)(n),d=o.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&c(n)}else a(t)},[s,e,a,c]);return[u,d]}},1030:function(e,t,n){n.d(t,{b:function(){return i}});var o=n(2265),i=globalThis?.document?o.useLayoutEffect:()=>{}},8281:function(e,t,n){n.d(t,{T:function(){return a},f:function(){return s}});var o=n(2265),i=n(9790),l=n(7437),a=o.forwardRef((e,t)=>(0,l.jsx)(i.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var s=a},6061:function(e,t,n){n.d(t,{j:function(){return cva}});var o=n(7042);let falsyToString=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=o.W,cva=(e,t)=>n=>{var o;if((null==t?void 0:t.variants)==null)return i(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:a}=t,s=Object.keys(l).map(e=>{let t=null==n?void 0:n[e],o=null==a?void 0:a[e];if(null===t)return null;let i=falsyToString(t)||falsyToString(o);return l[e][i]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,o]=t;return void 0===o||(e[n]=o),e},{}),c=null==t?void 0:null===(o=t.compoundVariants)||void 0===o?void 0:o.reduce((e,t)=>{let{class:n,className:o,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...u}[t]):({...a,...u})[t]===n})?[...e,n,o]:e},[]);return i(e,s,c,null==n?void 0:n.class,null==n?void 0:n.className)}},7042:function(e,t,n){n.d(t,{W:function(){return clsx}});function clsx(){for(var e,t,n=0,o="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e){if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(o&&(o+=" "),o+=t);return o}},4769:function(e,t,n){n.d(t,{m6:function(){return h}});let createClassGroupUtils=e=>{let t=createClassMap(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),getGroupRecursive(n,t)||getGroupIdForArbitraryProperty(e)},getConflictingClassGroupIds:(e,t)=>{let i=n[e]||[];return t&&o[e]?[...i,...o[e]]:i}}},getGroupRecursive=(e,t)=>{if(0===e.length)return t.classGroupId;let n=e[0],o=t.nextPart.get(n),i=o?getGroupRecursive(e.slice(1),o):void 0;if(i)return i;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},o=/^\[(.+)\]$/,getGroupIdForArbitraryProperty=e=>{if(o.test(e)){let t=o.exec(e)[1],n=t?.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},createClassMap=e=>{let{theme:t,prefix:n}=e,o={nextPart:new Map,validators:[]},i=getPrefixedClassGroupEntries(Object.entries(e.classGroups),n);return i.forEach(([e,n])=>{processClassesRecursively(n,o,e,t)}),o},processClassesRecursively=(e,t,n,o)=>{e.forEach(e=>{if("string"==typeof e){let o=""===e?t:getPart(t,e);o.classGroupId=n;return}if("function"==typeof e){if(isThemeGetter(e)){processClassesRecursively(e(o),t,n,o);return}t.validators.push({validator:e,classGroupId:n});return}Object.entries(e).forEach(([e,i])=>{processClassesRecursively(i,getPart(t,e),n,o)})})},getPart=(e,t)=>{let n=e;return t.split("-").forEach(e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)}),n},isThemeGetter=e=>e.isThemeGetter,getPrefixedClassGroupEntries=(e,t)=>t?e.map(([e,n])=>{let o=n.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,n])=>[t+e,n])):e);return[e,o]}):e,createLruCache=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,n=new Map,o=new Map,update=(i,l)=>{n.set(i,l),++t>e&&(t=0,o=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(update(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):update(e,t)}}},createParseClassName=e=>{let{separator:t,experimentalParseClassName:n}=e,o=1===t.length,i=t[0],l=t.length,parseClassName=e=>{let n;let a=[],s=0,u=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===s){if(d===i&&(o||e.slice(c,c+l)===t)){a.push(e.slice(u,c)),u=c+l;continue}if("/"===d){n=c;continue}}"["===d?s++:"]"===d&&s--}let c=0===a.length?e:e.substring(u),d=c.startsWith("!"),f=d?c.substring(1):c,p=n&&n>u?n-u:void 0;return{modifiers:a,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}};return n?e=>n({className:e,parseClassName}):parseClassName},sortModifiers=e=>{if(e.length<=1)return e;let t=[],n=[];return e.forEach(e=>{let o="["===e[0];o?(t.push(...n.sort(),e),n=[]):n.push(e)}),t.push(...n.sort()),t},createConfigUtils=e=>({cache:createLruCache(e.cacheSize),parseClassName:createParseClassName(e),...createClassGroupUtils(e)}),i=/\s+/,mergeClassList=(e,t)=>{let{parseClassName:n,getClassGroupId:o,getConflictingClassGroupIds:l}=t,a=[],s=e.trim().split(i),u="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{modifiers:i,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:f}=n(t),p=!!f,m=o(p?d.substring(0,f):d);if(!m){if(!p||!(m=o(d))){u=t+(u.length>0?" "+u:u);continue}p=!1}let g=sortModifiers(i).join(":"),h=c?g+"!":g,v=h+m;if(a.includes(v))continue;a.push(v);let y=l(m,p);for(let e=0;e<y.length;++e){let t=y[e];a.push(h+t)}u=t+(u.length>0?" "+u:u)}return u};function twJoin(){let e,t,n=0,o="";for(;n<arguments.length;)(e=arguments[n++])&&(t=toValue(e))&&(o&&(o+=" "),o+=t);return o}let toValue=e=>{let t;if("string"==typeof e)return e;let n="";for(let o=0;o<e.length;o++)e[o]&&(t=toValue(e[o]))&&(n&&(n+=" "),n+=t);return n},fromTheme=e=>{let themeGetter=t=>t[e]||[];return themeGetter.isThemeGetter=!0,themeGetter},l=/^\[(?:([a-z-]+):)?(.+)\]$/i,a=/^\d+\/\d+$/,s=new Set(["px","full","screen"]),u=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,c=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,d=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,f=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,p=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,isLength=e=>isNumber(e)||s.has(e)||a.test(e),isArbitraryLength=e=>getIsArbitraryValue(e,"length",isLengthOnly),isNumber=e=>!!e&&!Number.isNaN(Number(e)),isArbitraryNumber=e=>getIsArbitraryValue(e,"number",isNumber),isInteger=e=>!!e&&Number.isInteger(Number(e)),isPercent=e=>e.endsWith("%")&&isNumber(e.slice(0,-1)),isArbitraryValue=e=>l.test(e),isTshirtSize=e=>u.test(e),m=new Set(["length","size","percentage"]),isArbitrarySize=e=>getIsArbitraryValue(e,m,isNever),isArbitraryPosition=e=>getIsArbitraryValue(e,"position",isNever),g=new Set(["image","url"]),isArbitraryImage=e=>getIsArbitraryValue(e,g,isImage),isArbitraryShadow=e=>getIsArbitraryValue(e,"",isShadow),isAny=()=>!0,getIsArbitraryValue=(e,t,n)=>{let o=l.exec(e);return!!o&&(o[1]?"string"==typeof t?o[1]===t:t.has(o[1]):n(o[2]))},isLengthOnly=e=>c.test(e)&&!d.test(e),isNever=()=>!1,isShadow=e=>f.test(e),isImage=e=>p.test(e),h=function(e){let t,n,o;let functionToCall=function(i){let l=[].reduce((e,t)=>t(e),e());return n=(t=createConfigUtils(l)).cache.get,o=t.cache.set,functionToCall=tailwindMerge,tailwindMerge(i)};function tailwindMerge(e){let i=n(e);if(i)return i;let l=mergeClassList(e,t);return o(e,l),l}return function(){return functionToCall(twJoin.apply(null,arguments))}}(()=>{let e=fromTheme("colors"),t=fromTheme("spacing"),n=fromTheme("blur"),o=fromTheme("brightness"),i=fromTheme("borderColor"),l=fromTheme("borderRadius"),a=fromTheme("borderSpacing"),s=fromTheme("borderWidth"),u=fromTheme("contrast"),c=fromTheme("grayscale"),d=fromTheme("hueRotate"),f=fromTheme("invert"),p=fromTheme("gap"),m=fromTheme("gradientColorStops"),g=fromTheme("gradientColorStopPositions"),h=fromTheme("inset"),v=fromTheme("margin"),y=fromTheme("opacity"),b=fromTheme("padding"),w=fromTheme("saturate"),x=fromTheme("scale"),S=fromTheme("sepia"),_=fromTheme("skew"),E=fromTheme("space"),C=fromTheme("translate"),getOverscroll=()=>["auto","contain","none"],getOverflow=()=>["auto","hidden","clip","visible","scroll"],getSpacingWithAutoAndArbitrary=()=>["auto",isArbitraryValue,t],getSpacingWithArbitrary=()=>[isArbitraryValue,t],getLengthWithEmptyAndArbitrary=()=>["",isLength,isArbitraryLength],getNumberWithAutoAndArbitrary=()=>["auto",isNumber,isArbitraryValue],getPositions=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],getLineStyles=()=>["solid","dashed","dotted","double","none"],getBlendModes=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],getAlign=()=>["start","end","center","between","around","evenly","stretch"],getZeroAndEmpty=()=>["","0",isArbitraryValue],getBreaks=()=>["auto","avoid","all","avoid-page","page","left","right","column"],getNumberAndArbitrary=()=>[isNumber,isArbitraryValue];return{cacheSize:500,separator:":",theme:{colors:[isAny],spacing:[isLength,isArbitraryLength],blur:["none","",isTshirtSize,isArbitraryValue],brightness:getNumberAndArbitrary(),borderColor:[e],borderRadius:["none","","full",isTshirtSize,isArbitraryValue],borderSpacing:getSpacingWithArbitrary(),borderWidth:getLengthWithEmptyAndArbitrary(),contrast:getNumberAndArbitrary(),grayscale:getZeroAndEmpty(),hueRotate:getNumberAndArbitrary(),invert:getZeroAndEmpty(),gap:getSpacingWithArbitrary(),gradientColorStops:[e],gradientColorStopPositions:[isPercent,isArbitraryLength],inset:getSpacingWithAutoAndArbitrary(),margin:getSpacingWithAutoAndArbitrary(),opacity:getNumberAndArbitrary(),padding:getSpacingWithArbitrary(),saturate:getNumberAndArbitrary(),scale:getNumberAndArbitrary(),sepia:getZeroAndEmpty(),skew:getNumberAndArbitrary(),space:getSpacingWithArbitrary(),translate:getSpacingWithArbitrary()},classGroups:{aspect:[{aspect:["auto","square","video",isArbitraryValue]}],container:["container"],columns:[{columns:[isTshirtSize]}],"break-after":[{"break-after":getBreaks()}],"break-before":[{"break-before":getBreaks()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...getPositions(),isArbitraryValue]}],overflow:[{overflow:getOverflow()}],"overflow-x":[{"overflow-x":getOverflow()}],"overflow-y":[{"overflow-y":getOverflow()}],overscroll:[{overscroll:getOverscroll()}],"overscroll-x":[{"overscroll-x":getOverscroll()}],"overscroll-y":[{"overscroll-y":getOverscroll()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[h]}],"inset-x":[{"inset-x":[h]}],"inset-y":[{"inset-y":[h]}],start:[{start:[h]}],end:[{end:[h]}],top:[{top:[h]}],right:[{right:[h]}],bottom:[{bottom:[h]}],left:[{left:[h]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",isInteger,isArbitraryValue]}],basis:[{basis:getSpacingWithAutoAndArbitrary()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",isArbitraryValue]}],grow:[{grow:getZeroAndEmpty()}],shrink:[{shrink:getZeroAndEmpty()}],order:[{order:["first","last","none",isInteger,isArbitraryValue]}],"grid-cols":[{"grid-cols":[isAny]}],"col-start-end":[{col:["auto",{span:["full",isInteger,isArbitraryValue]},isArbitraryValue]}],"col-start":[{"col-start":getNumberWithAutoAndArbitrary()}],"col-end":[{"col-end":getNumberWithAutoAndArbitrary()}],"grid-rows":[{"grid-rows":[isAny]}],"row-start-end":[{row:["auto",{span:[isInteger,isArbitraryValue]},isArbitraryValue]}],"row-start":[{"row-start":getNumberWithAutoAndArbitrary()}],"row-end":[{"row-end":getNumberWithAutoAndArbitrary()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",isArbitraryValue]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",isArbitraryValue]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...getAlign()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...getAlign(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...getAlign(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[v]}],mx:[{mx:[v]}],my:[{my:[v]}],ms:[{ms:[v]}],me:[{me:[v]}],mt:[{mt:[v]}],mr:[{mr:[v]}],mb:[{mb:[v]}],ml:[{ml:[v]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",isArbitraryValue,t]}],"min-w":[{"min-w":[isArbitraryValue,t,"min","max","fit"]}],"max-w":[{"max-w":[isArbitraryValue,t,"none","full","min","max","fit","prose",{screen:[isTshirtSize]},isTshirtSize]}],h:[{h:[isArbitraryValue,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[isArbitraryValue,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[isArbitraryValue,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[isArbitraryValue,t,"auto","min","max","fit"]}],"font-size":[{text:["base",isTshirtSize,isArbitraryLength]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",isArbitraryNumber]}],"font-family":[{font:[isAny]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",isArbitraryValue]}],"line-clamp":[{"line-clamp":["none",isNumber,isArbitraryNumber]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",isLength,isArbitraryValue]}],"list-image":[{"list-image":["none",isArbitraryValue]}],"list-style-type":[{list:["none","disc","decimal",isArbitraryValue]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...getLineStyles(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",isLength,isArbitraryLength]}],"underline-offset":[{"underline-offset":["auto",isLength,isArbitraryValue]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:getSpacingWithArbitrary()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",isArbitraryValue]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",isArbitraryValue]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...getPositions(),isArbitraryPosition]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",isArbitrarySize]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},isArbitraryImage]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[l]}],"rounded-s":[{"rounded-s":[l]}],"rounded-e":[{"rounded-e":[l]}],"rounded-t":[{"rounded-t":[l]}],"rounded-r":[{"rounded-r":[l]}],"rounded-b":[{"rounded-b":[l]}],"rounded-l":[{"rounded-l":[l]}],"rounded-ss":[{"rounded-ss":[l]}],"rounded-se":[{"rounded-se":[l]}],"rounded-ee":[{"rounded-ee":[l]}],"rounded-es":[{"rounded-es":[l]}],"rounded-tl":[{"rounded-tl":[l]}],"rounded-tr":[{"rounded-tr":[l]}],"rounded-br":[{"rounded-br":[l]}],"rounded-bl":[{"rounded-bl":[l]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...getLineStyles(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:getLineStyles()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-s":[{"border-s":[i]}],"border-color-e":[{"border-e":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["",...getLineStyles()]}],"outline-offset":[{"outline-offset":[isLength,isArbitraryValue]}],"outline-w":[{outline:[isLength,isArbitraryLength]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:getLengthWithEmptyAndArbitrary()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[isLength,isArbitraryLength]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",isTshirtSize,isArbitraryShadow]}],"shadow-color":[{shadow:[isAny]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...getBlendModes(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":getBlendModes()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[o]}],contrast:[{contrast:[u]}],"drop-shadow":[{"drop-shadow":["","none",isTshirtSize,isArbitraryValue]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[f]}],saturate:[{saturate:[w]}],sepia:[{sepia:[S]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[u]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[w]}],"backdrop-sepia":[{"backdrop-sepia":[S]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",isArbitraryValue]}],duration:[{duration:getNumberAndArbitrary()}],ease:[{ease:["linear","in","out","in-out",isArbitraryValue]}],delay:[{delay:getNumberAndArbitrary()}],animate:[{animate:["none","spin","ping","pulse","bounce",isArbitraryValue]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[isInteger,isArbitraryValue]}],"translate-x":[{"translate-x":[C]}],"translate-y":[{"translate-y":[C]}],"skew-x":[{"skew-x":[_]}],"skew-y":[{"skew-y":[_]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",isArbitraryValue]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",isArbitraryValue]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":getSpacingWithArbitrary()}],"scroll-mx":[{"scroll-mx":getSpacingWithArbitrary()}],"scroll-my":[{"scroll-my":getSpacingWithArbitrary()}],"scroll-ms":[{"scroll-ms":getSpacingWithArbitrary()}],"scroll-me":[{"scroll-me":getSpacingWithArbitrary()}],"scroll-mt":[{"scroll-mt":getSpacingWithArbitrary()}],"scroll-mr":[{"scroll-mr":getSpacingWithArbitrary()}],"scroll-mb":[{"scroll-mb":getSpacingWithArbitrary()}],"scroll-ml":[{"scroll-ml":getSpacingWithArbitrary()}],"scroll-p":[{"scroll-p":getSpacingWithArbitrary()}],"scroll-px":[{"scroll-px":getSpacingWithArbitrary()}],"scroll-py":[{"scroll-py":getSpacingWithArbitrary()}],"scroll-ps":[{"scroll-ps":getSpacingWithArbitrary()}],"scroll-pe":[{"scroll-pe":getSpacingWithArbitrary()}],"scroll-pt":[{"scroll-pt":getSpacingWithArbitrary()}],"scroll-pr":[{"scroll-pr":getSpacingWithArbitrary()}],"scroll-pb":[{"scroll-pb":getSpacingWithArbitrary()}],"scroll-pl":[{"scroll-pl":getSpacingWithArbitrary()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",isArbitraryValue]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[isLength,isArbitraryLength,isArbitraryNumber]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})},44:function(e,t,n){n.d(t,{_T:function(){return __rest},ev:function(){return __spreadArray},pi:function(){return __assign}});var __assign=function(){return(__assign=Object.assign||function(e){for(var t,n=1,o=arguments.length;n<o;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function __rest(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,o=Object.getOwnPropertySymbols(e);i<o.length;i++)0>t.indexOf(o[i])&&Object.prototype.propertyIsEnumerable.call(e,o[i])&&(n[o[i]]=e[o[i]]);return n}function __spreadArray(e,t,n){if(n||2==arguments.length)for(var o,i=0,l=t.length;i<l;i++)!o&&i in t||(o||(o=Array.prototype.slice.call(t,0,i)),o[i]=t[i]);return e.concat(o||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError}}]);