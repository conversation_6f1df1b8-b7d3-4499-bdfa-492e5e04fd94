"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[747],{8266:function(e,s,t){t.d(s,{Z:function(){return components_Footer}});var a=t(7437),r=t(2265),l=t(1396),n=t.n(l),i=t(2741),c=t(2176),o=t(6141),d=t(1628),x=t(2031),m=t(3611),h=t(5831),u=t(446),components_AdminLogin=e=>{let{open:s,onOpenChange:t}=e,[l,n]=(0,r.useState)(""),[i,c]=(0,r.useState)(""),[o,d]=(0,r.useState)(!1),{toast:p}=(0,u.pm)(),handleLogin=async e=>{e.preventDefault(),d(!0),"admin"===l&&"cvets123"===i?(localStorage.setItem("cvets_admin","true"),p({title:"Login successful",description:"Welcome back, admin!"}),t(!1),n(""),c("")):p({title:"Login failed",description:"Invalid credentials. Please try again.",variant:"destructive"}),d(!1)},j="true"===localStorage.getItem("cvets_admin");return(0,a.jsx)(x.Vq,{open:s,onOpenChange:t,children:(0,a.jsxs)(x.cZ,{className:"sm:max-w-md",children:[(0,a.jsx)(x.fK,{children:(0,a.jsx)(x.$N,{children:j?"Admin Panel":"Admin Login"})}),j?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You are logged in as administrator."}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(m.z,{onClick:()=>window.location.href="/manage-blogs",className:"flex-1",children:"Manage Blogs"}),(0,a.jsx)(m.z,{onClick:()=>window.location.href="/manage-products",className:"flex-1",variant:"outline",children:"Manage Products"})]}),(0,a.jsx)(m.z,{onClick:()=>{localStorage.removeItem("cvets_admin"),p({title:"Logged out",description:"You have been logged out successfully."}),t(!1)},variant:"destructive",className:"w-full",children:"Logout"})]}):(0,a.jsxs)("form",{onSubmit:handleLogin,className:"space-y-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(h.I,{type:"text",placeholder:"Username",value:l,onChange:e=>n(e.target.value),required:!0})}),(0,a.jsx)("div",{children:(0,a.jsx)(h.I,{type:"password",placeholder:"Password",value:i,onChange:e=>c(e.target.value),required:!0})}),(0,a.jsx)(m.z,{type:"submit",className:"w-full",disabled:o,children:o?"Logging in...":"Login"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground text-center",children:"Demo: admin / cvets123"})]})]})})},components_Footer=()=>{let[e,s]=(0,r.useState)(0),[t,l]=(0,r.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("footer",{className:"bg-slate-900 text-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center cursor-pointer transition-transform hover:scale-105",onClick:()=>{s(e=>{let s=e+1;return 4===s?(l(!0),0):s})},children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-bold",children:"CVETS Veterinary Services"}),(0,a.jsx)("p",{className:"text-sm text-slate-300",children:"Professional Pet Care"})]})]}),(0,a.jsx)("p",{className:"text-slate-300 mb-4 max-w-md",children:"Providing compassionate, professional veterinary care for your beloved pets. Your pet's health and wellbeing are our top priorities."}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-slate-300",children:[(0,a.jsx)(i.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"+254 718 376 311"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-slate-300",children:[(0,a.jsx)(c.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"WhatsApp: +254 718 376 311"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold mb-4",children:"Quick Links"}),(0,a.jsxs)("ul",{className:"space-y-2",children:[(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/services",className:"text-slate-300 hover:text-white transition-colors",children:"Our Services"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/about",className:"text-slate-300 hover:text-white transition-colors",children:"About Us"})}),(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:"/blog",className:"text-slate-300 hover:text-white transition-colors",children:"Pet Care Tips"})}),(0,a.jsx)("li",{children:(0,a.jsx)("button",{onClick:d.fW,className:"text-slate-300 hover:text-white transition-colors cursor-pointer",children:"Book Appointment"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold mb-4",children:"Service Hours"}),(0,a.jsxs)("div",{className:"space-y-2 text-slate-300",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Open 7 Days a Week"})]}),(0,a.jsx)("p",{children:"Monday - Sunday: 8AM - 6PM"}),(0,a.jsx)("p",{className:"text-red-300 font-medium",children:"Emergency Services Available"}),(0,a.jsxs)("a",{href:"https://wa.me/254718376311",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-2 text-green-400 hover:text-green-300 transition-colors mt-2",children:[(0,a.jsx)(c.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"WhatsApp Us"})]})]})]})]}),(0,a.jsx)("div",{className:"border-t border-slate-700 mt-8 pt-8 text-center text-slate-400",children:(0,a.jsx)("p",{children:"\xa9 2024 CVETS Veterinary Services. All rights reserved."})})]})}),(0,a.jsx)(components_AdminLogin,{open:t,onOpenChange:l})]})}},3827:function(e,s,t){var a=t(7437),r=t(2265),l=t(1396),n=t.n(l),i=t(4033),c=t(2741),o=t(2549),d=t(8004),x=t(3611),m=t(5925),h=t(1628);s.Z=()=>{let[e,s]=(0,r.useState)(!1),t=(0,i.usePathname)(),{openAppointmentModal:l}=(0,m.useAppointment)(),u=[{name:"Home",href:"/"},{name:"Services",href:"/services"},{name:"About",href:"/about"},{name:"Products",href:"/products"},{name:"Blog",href:"/blog"},{name:"Contact",href:"/contact"}],isActive=e=>t===e;(0,r.useEffect)(()=>{let e=window.location.hash;if(e){let s=e.substring(1);setTimeout(()=>{(0,h.BK)(s)},100)}},[t]);let handleNavClick=(e,t)=>{e.preventDefault(),s(!1),(0,h.QR)(t)};return(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-border sticky top-0 z-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl font-bold text-foreground",children:"CVETS Veterinary Services"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Professional Pet Care"})]})]})}),(0,a.jsx)("nav",{className:"hidden md:flex space-x-8",children:u.map(e=>(0,a.jsx)("a",{href:e.href,onClick:s=>handleNavClick(s,e.href),className:"px-3 py-2 text-sm font-medium transition-colors duration-200 cursor-pointer ".concat(isActive(e.href)?"text-primary border-b-2 border-primary":"text-muted-foreground hover:text-primary"),children:e.name},e.name))}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-muted-foreground",children:[(0,a.jsx)(c.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"0718376311"})]}),(0,a.jsx)(x.z,{onClick:l,className:"bg-blue-600 hover:bg-blue-700",children:"Book Now"})]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)("button",{onClick:()=>s(!e),className:"p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-muted",children:e?(0,a.jsx)(o.Z,{className:"w-6 h-6"}):(0,a.jsx)(d.Z,{className:"w-6 h-6"})})})]}),e&&(0,a.jsx)("div",{className:"md:hidden py-4 border-t border-border",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[u.map(e=>(0,a.jsx)("a",{href:e.href,onClick:s=>handleNavClick(s,e.href),className:"px-3 py-2 text-base font-medium transition-colors duration-200 cursor-pointer ".concat(isActive(e.href)?"text-primary bg-muted":"text-muted-foreground hover:text-primary hover:bg-muted"),children:e.name},e.name)),(0,a.jsxs)("div",{className:"px-3 py-2 text-sm text-muted-foreground flex items-center space-x-2",children:[(0,a.jsx)(c.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"0718376311"})]}),(0,a.jsx)("div",{className:"px-3 py-2",children:(0,a.jsx)(x.z,{onClick:()=>{s(!1),l()},className:"w-full bg-blue-600 hover:bg-blue-700",children:"Book Now"})})]})})]})})}}}]);