"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[581],{5925:function(e,t,a){a.r(t),a.d(t,{AppointmentProvider:function(){return AppointmentProvider},useAppointment:function(){return useAppointment}});var s=a(7437),r=a(2265),n=a(3611),o=a(5831),i=a(6743),l=a(6061),d=a(1628);let c=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(i.f,{ref:t,className:(0,d.cn)(c(),a),...r})});m.displayName=i.f.displayName;let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("textarea",{className:(0,d.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...r})});u.displayName="Textarea";var p=a(6239),f=a(3523),h=a(9224),g=a(2442);let x=p.fC;p.ZA;let b=p.B4,y=r.forwardRef((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsxs)(p.xz,{ref:t,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...n,children:[r,(0,s.jsx)(p.JO,{asChild:!0,children:(0,s.jsx)(f.Z,{className:"h-4 w-4 opacity-50"})})]})});y.displayName=p.xz.displayName;let N=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(p.u_,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,s.jsx)(h.Z,{className:"h-4 w-4"})})});N.displayName=p.u_.displayName;let v=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(p.$G,{ref:t,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",a),...r,children:(0,s.jsx)(f.Z,{className:"h-4 w-4"})})});v.displayName=p.$G.displayName;let j=r.forwardRef((e,t)=>{let{className:a,children:r,position:n="popper",...o}=e;return(0,s.jsx)(p.h_,{children:(0,s.jsxs)(p.VY,{ref:t,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:n,...o,children:[(0,s.jsx)(N,{}),(0,s.jsx)(p.l_,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,s.jsx)(v,{})]})})});j.displayName=p.VY.displayName;let w=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(p.__,{ref:t,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...r})});w.displayName=p.__.displayName;let A=r.forwardRef((e,t)=>{let{className:a,children:r,...n}=e;return(0,s.jsxs)(p.ck,{ref:t,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(p.wU,{children:(0,s.jsx)(g.Z,{className:"h-4 w-4"})})}),(0,s.jsx)(p.eT,{children:r})]})});A.displayName=p.ck.displayName;let T=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(p.Z0,{ref:t,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",a),...r})});T.displayName=p.Z0.displayName;var D=a(2031),C=a(7972),S=a(7810),I=a(6141),P=a(2176),k=a(2741);let M=["Vaccinations","Health check-ups","Surgical procedures","Dental care","Diagnostic testing","Emergency care","Preventative care","Parasite control","Local and international travel","Wash & Grooming"],E=["8:00 AM","9:00 AM","10:00 AM","11:00 AM","12:00 PM","1:00 PM","2:00 PM","3:00 PM","4:00 PM","5:00 PM","6:00 PM"];function AppointmentModal(e){let{isOpen:t,onClose:a}=e,[i,l]=(0,r.useState)({ownerName:"",phone:"",email:"",petName:"",petType:"",petAge:"",service:"",preferredDate:"",preferredTime:"",message:""}),[d,c]=(0,r.useState)(!1),handleInputChange=(e,t)=>{l(a=>({...a,[e]:t}))},formatWhatsAppMessage=e=>{let t="\n\uD83D\uDC3E *CVETS Appointment Request* \uD83D\uDC3E\n\n\uD83D\uDC64 *Owner Details:*\nName: ".concat(e.ownerName,"\nPhone: ").concat(e.phone,"\nEmail: ").concat(e.email,"\n\n\uD83D\uDC15 *Pet Details:*\nPet Name: ").concat(e.petName,"\nPet Type: ").concat(e.petType,"\nPet Age: ").concat(e.petAge,"\n\n\uD83C\uDFE5 *Appointment Details:*\nService: ").concat(e.service,"\nPreferred Date: ").concat(e.preferredDate,"\nPreferred Time: ").concat(e.preferredTime,"\n\n\uD83D\uDCAC *Additional Message:*\n").concat(e.message||"No additional message","\n\n---\nPlease confirm this appointment or suggest an alternative time. Thank you!\n    ").trim();return encodeURIComponent(t)},handleSubmit=async e=>{e.preventDefault(),c(!0);try{let e=["ownerName","phone","petName","service","preferredDate"].filter(e=>!i[e]);if(e.length>0){alert("Please fill in all required fields"),c(!1);return}let t=formatWhatsAppMessage(i),s="https://wa.me/".concat("254718376311","?text=").concat(t);window.open(s,"_blank"),l({ownerName:"",phone:"",email:"",petName:"",petType:"",petAge:"",service:"",preferredDate:"",preferredTime:"",message:""}),a(),alert("Appointment request sent! You will be redirected to WhatsApp to complete the booking.")}catch(e){console.error("Error submitting appointment:",e),alert("There was an error submitting your appointment. Please try again.")}finally{c(!1)}};return(0,s.jsx)(D.Vq,{open:t,onOpenChange:a,children:(0,s.jsxs)(D.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto bg-gradient-to-br from-blue-50/95 via-pink-50/95 to-gray-50/95 backdrop-blur-sm border-0",children:[(0,s.jsxs)(D.fK,{className:"pb-6",children:[(0,s.jsx)(D.$N,{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-gray-900 bg-clip-text text-transparent text-center",children:"Book Your Pet's Appointment"}),(0,s.jsx)("p",{className:"text-center text-gray-600 mt-2",children:"Fill out the form below and we'll contact you via WhatsApp to confirm your appointment"})]}),(0,s.jsxs)("form",{onSubmit:handleSubmit,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center space-x-2",children:[(0,s.jsx)(C.Z,{className:"w-5 h-5 text-blue-600"}),(0,s.jsx)("span",{children:"Owner Information"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"ownerName",className:"text-gray-700 font-medium",children:"Full Name *"}),(0,s.jsx)(o.I,{id:"ownerName",type:"text",value:i.ownerName,onChange:e=>handleInputChange("ownerName",e.target.value),placeholder:"Enter your full name",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"phone",className:"text-gray-700 font-medium",children:"Phone Number *"}),(0,s.jsx)(o.I,{id:"phone",type:"tel",value:i.phone,onChange:e=>handleInputChange("phone",e.target.value),placeholder:"0718376311",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,s.jsx)(m,{htmlFor:"email",className:"text-gray-700 font-medium",children:"Email Address"}),(0,s.jsx)(o.I,{id:"email",type:"email",value:i.email,onChange:e=>handleInputChange("email",e.target.value),placeholder:"<EMAIL>",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center space-x-2",children:[(0,s.jsx)(S.Z,{className:"w-5 h-5 text-pink-600"}),(0,s.jsx)("span",{children:"Pet Information"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"petName",className:"text-gray-700 font-medium",children:"Pet Name *"}),(0,s.jsx)(o.I,{id:"petName",type:"text",value:i.petName,onChange:e=>handleInputChange("petName",e.target.value),placeholder:"Enter pet's name",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"petType",className:"text-gray-700 font-medium",children:"Pet Type"}),(0,s.jsx)(o.I,{id:"petType",type:"text",value:i.petType,onChange:e=>handleInputChange("petType",e.target.value),placeholder:"Dog, Cat, Bird, etc.",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"petAge",className:"text-gray-700 font-medium",children:"Pet Age"}),(0,s.jsx)(o.I,{id:"petAge",type:"text",value:i.petAge,onChange:e=>handleInputChange("petAge",e.target.value),placeholder:"2 years, 6 months, etc.",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center space-x-2",children:[(0,s.jsx)(I.Z,{className:"w-5 h-5 text-blue-600"}),(0,s.jsx)("span",{children:"Appointment Details"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"service",className:"text-gray-700 font-medium",children:"Service Required *"}),(0,s.jsxs)(x,{value:i.service,onValueChange:e=>handleInputChange("service",e),children:[(0,s.jsx)(y,{className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",children:(0,s.jsx)(b,{placeholder:"Select a service"})}),(0,s.jsx)(j,{children:M.map(e=>(0,s.jsx)(A,{value:e,children:e},e))})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"preferredDate",className:"text-gray-700 font-medium",children:"Preferred Date *"}),(0,s.jsx)(o.I,{id:"preferredDate",type:"date",value:i.preferredDate,onChange:e=>handleInputChange("preferredDate",e.target.value),className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",min:new Date().toISOString().split("T")[0],required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"preferredTime",className:"text-gray-700 font-medium",children:"Preferred Time"}),(0,s.jsxs)(x,{value:i.preferredTime,onValueChange:e=>handleInputChange("preferredTime",e),children:[(0,s.jsx)(y,{className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80",children:(0,s.jsx)(b,{placeholder:"Select time"})}),(0,s.jsx)(j,{children:E.map(e=>(0,s.jsx)(A,{value:e,children:e},e))})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 flex items-center space-x-2",children:[(0,s.jsx)(P.Z,{className:"w-5 h-5 text-pink-600"}),(0,s.jsx)("span",{children:"Additional Information"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(m,{htmlFor:"message",className:"text-gray-700 font-medium",children:"Additional Message or Special Requirements"}),(0,s.jsx)(u,{id:"message",value:i.message,onChange:e=>handleInputChange("message",e.target.value),placeholder:"Please describe any specific concerns, symptoms, or special requirements for your pet...",className:"border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80 min-h-[100px]",rows:4})]})]}),(0,s.jsxs)("div",{className:"pt-4 flex gap-4",children:[(0,s.jsx)(n.z,{type:"button",variant:"outline",onClick:a,className:"flex-1 border-gray-200 text-gray-700 hover:bg-gray-50",children:"Cancel"}),(0,s.jsx)(n.z,{type:"submit",disabled:d,className:"flex-1 bg-gradient-to-r from-blue-600 to-pink-600 hover:from-blue-700 hover:to-pink-700 text-white shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300",children:d?(0,s.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),(0,s.jsx)("span",{children:"Sending..."})]}):(0,s.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,s.jsx)(k.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Send to WhatsApp"})]})})]})]})]})})}let _=(0,r.createContext)(void 0);function useAppointment(){let e=(0,r.useContext)(_);if(void 0===e)throw Error("useAppointment must be used within an AppointmentProvider");return e}function AppointmentProvider(e){let{children:t}=e,[a,n]=(0,r.useState)(!1),closeAppointmentModal=()=>n(!1);return(0,s.jsxs)(_.Provider,{value:{openAppointmentModal:()=>n(!0),closeAppointmentModal,isModalOpen:a},children:[t,(0,s.jsx)(AppointmentModal,{isOpen:a,onClose:closeAppointmentModal})]})}},3611:function(e,t,a){a.d(t,{z:function(){return d}});var s=a(7437),r=a(2265),n=a(7256),o=a(6061),i=a(1628);let l=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:a,variant:r,size:o,asChild:d=!1,...c}=e,m=d?n.g7:"button";return(0,s.jsx)(m,{className:(0,i.cn)(l({variant:r,size:o,className:a})),ref:t,...c})});d.displayName="Button"},2031:function(e,t,a){a.d(t,{$N:function(){return u},Vq:function(){return l},cZ:function(){return m},fK:function(){return DialogHeader}});var s=a(7437),r=a(2265),n=a(7389),o=a(2549),i=a(1628);let l=n.fC;n.xz;let d=n.h_;n.x8;let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.aV,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r})});c.displayName=n.aV.displayName;let m=r.forwardRef((e,t)=>{let{className:a,children:r,...l}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(c,{}),(0,s.jsxs)(n.VY,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...l,children:[r,(0,s.jsxs)(n.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(o.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=n.VY.displayName;let DialogHeader=e=>{let{className:t,...a}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};DialogHeader.displayName="DialogHeader";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",a),...r})});u.displayName=n.Dx.displayName;let p=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(n.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...r})});p.displayName=n.dk.displayName},5831:function(e,t,a){a.d(t,{I:function(){return o}});var s=a(7437),r=a(2265),n=a(1628);let o=r.forwardRef((e,t)=>{let{className:a,type:r,...o}=e;return(0,s.jsx)("input",{type:r,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:t,...o})});o.displayName="Input"},446:function(e,t,a){a.d(t,{pm:function(){return useToast}});var s=a(2265);let r=0,n=new Map,addToRemoveQueue=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),dispatch({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},reducer=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?addToRemoveQueue(a):e.toasts.forEach(e=>{addToRemoveQueue(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],i={toasts:[]};function dispatch(e){i=reducer(i,e),o.forEach(e=>{e(i)})}function toast(e){let{...t}=e,a=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),dismiss=()=>dispatch({type:"DISMISS_TOAST",toastId:a});return dispatch({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||dismiss()}}}),{id:a,dismiss,update:e=>dispatch({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function useToast(){let[e,t]=s.useState(i);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast,dismiss:e=>dispatch({type:"DISMISS_TOAST",toastId:e})}}},1628:function(e,t,a){a.d(t,{BK:function(){return smoothScrollToElement},QR:function(){return handleMenuNavigation},cn:function(){return cn},fW:function(){return smoothScrollToAppointmentForm}});var s=a(7042),r=a(4769);function cn(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.m6)((0,s.W)(t))}function smoothScrollToElement(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:80,a=document.getElementById(e);if(a){let e=a.getBoundingClientRect().top+window.pageYOffset;window.scrollTo({top:e-t,behavior:"smooth"})}}function smoothScrollToAppointmentForm(){smoothScrollToElement("appointment-form")}function handleMenuNavigation(e){window.location.href=e}}}]);