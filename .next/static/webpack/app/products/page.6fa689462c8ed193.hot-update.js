"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/products/page",{

/***/ "(app-pages-browser)/./components/AppointmentProvider.tsx":
/*!********************************************!*\
  !*** ./components/AppointmentProvider.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppointmentProvider: function() { return /* binding */ AppointmentProvider; },\n/* harmony export */   useAppointment: function() { return /* binding */ useAppointment; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AppointmentModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AppointmentModal */ \"(app-pages-browser)/./components/AppointmentModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ useAppointment,AppointmentProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AppointmentContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAppointment() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppointmentContext);\n    if (context === undefined) {\n        throw new Error(\"useAppointment must be used within an AppointmentProvider\");\n    }\n    return context;\n}\n_s(useAppointment, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AppointmentProvider(param) {\n    let { children } = param;\n    _s1();\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const openAppointmentModal = ()=>setIsModalOpen(true);\n    const closeAppointmentModal = ()=>setIsModalOpen(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppointmentContext.Provider, {\n        value: {\n            openAppointmentModal,\n            closeAppointmentModal,\n            isModalOpen\n        },\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AppointmentModal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isOpen: isModalOpen,\n                onClose: closeAppointmentModal\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/components/AppointmentProvider.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Templates/Frontend/ReactJs/cvet-veterinary/components/AppointmentProvider.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_s1(AppointmentProvider, \"mLsII5HRP5G63IA/8vjZ5YHXWr8=\");\n_c = AppointmentProvider;\nvar _c;\n$RefreshReg$(_c, \"AppointmentProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/AppointmentProvider.tsx\n"));

/***/ })

});