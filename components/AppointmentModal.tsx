'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Phone, User, Heart, Clock, MessageCircle, X } from 'lucide-react';

interface AppointmentFormData {
  ownerName: string;
  phone: string;
  email: string;
  petName: string;
  petType: string;
  petAge: string;
  service: string;
  preferredDate: string;
  preferredTime: string;
  message: string;
}

interface AppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const services = [
  'Vaccinations',
  'Health check-ups',
  'Surgical procedures',
  'Dental care',
  'Diagnostic testing',
  'Emergency care',
  'Preventative care',
  'Parasite control',
  'Local and international travel',
  'Wash & Grooming'
];

const timeSlots = [
  '8:00 AM', '9:00 AM', '10:00 AM', '11:00 AM',
  '12:00 PM', '1:00 PM', '2:00 PM', '3:00 PM',
  '4:00 PM', '5:00 PM', '6:00 PM'
];

export default function AppointmentModal({ isOpen, onClose }: AppointmentModalProps) {
  const [formData, setFormData] = useState<AppointmentFormData>({
    ownerName: '',
    phone: '',
    email: '',
    petName: '',
    petType: '',
    petAge: '',
    service: '',
    preferredDate: '',
    preferredTime: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof AppointmentFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatWhatsAppMessage = (data: AppointmentFormData) => {
    const message = `
🐾 *CVETS Appointment Request* 🐾

👤 *Owner Details:*
Name: ${data.ownerName}
Phone: ${data.phone}
Email: ${data.email}

🐕 *Pet Details:*
Pet Name: ${data.petName}
Pet Type: ${data.petType}
Pet Age: ${data.petAge}

🏥 *Appointment Details:*
Service: ${data.service}
Preferred Date: ${data.preferredDate}
Preferred Time: ${data.preferredTime}

💬 *Additional Message:*
${data.message || 'No additional message'}

---
Please confirm this appointment or suggest an alternative time. Thank you!
    `.trim();

    return encodeURIComponent(message);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      const requiredFields = ['ownerName', 'phone', 'petName', 'service', 'preferredDate'];
      const missingFields = requiredFields.filter(field => !formData[field as keyof AppointmentFormData]);
      
      if (missingFields.length > 0) {
        alert('Please fill in all required fields');
        setIsSubmitting(false);
        return;
      }

      // Format message for WhatsApp
      const whatsappMessage = formatWhatsAppMessage(formData);
      const whatsappNumber = '254718376311'; // Kenya format
      const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${whatsappMessage}`;

      // Open WhatsApp
      window.open(whatsappUrl, '_blank');

      // Reset form and close modal
      setFormData({
        ownerName: '',
        phone: '',
        email: '',
        petName: '',
        petType: '',
        petAge: '',
        service: '',
        preferredDate: '',
        preferredTime: '',
        message: ''
      });

      onClose();
      alert('Appointment request sent! You will be redirected to WhatsApp to complete the booking.');
    } catch (error) {
      console.error('Error submitting appointment:', error);
      alert('There was an error submitting your appointment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-gray-900">Book Your Pet's Appointment</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X className="w-6 h-6" />
            </button>
          </div>
          <p className="text-gray-600 mt-2">Fill out the form below and we'll contact you via WhatsApp</p>
        </div>

        <div className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
          {/* Owner Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
              <User className="w-5 h-5 text-blue-600" />
              <span>Owner Information</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ownerName" className="text-gray-700 font-medium">
                  Full Name *
                </Label>
                <Input
                  id="ownerName"
                  type="text"
                  value={formData.ownerName}
                  onChange={(e) => handleInputChange('ownerName', e.target.value)}
                  placeholder="Enter your full name"
                  className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone" className="text-gray-700 font-medium">
                  Phone Number *
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="0718376311"
                  className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"
                  required
                />
              </div>
              
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="email" className="text-gray-700 font-medium">
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"
                />
              </div>
            </div>
          </div>

          {/* Pet Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
              <Heart className="w-5 h-5 text-pink-600" />
              <span>Pet Information</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="petName" className="text-gray-700 font-medium">
                  Pet Name *
                </Label>
                <Input
                  id="petName"
                  type="text"
                  value={formData.petName}
                  onChange={(e) => handleInputChange('petName', e.target.value)}
                  placeholder="Enter pet's name"
                  className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="petType" className="text-gray-700 font-medium">
                  Pet Type
                </Label>
                <Input
                  id="petType"
                  type="text"
                  value={formData.petType}
                  onChange={(e) => handleInputChange('petType', e.target.value)}
                  placeholder="Dog, Cat, Bird, etc."
                  className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="petAge" className="text-gray-700 font-medium">
                  Pet Age
                </Label>
                <Input
                  id="petAge"
                  type="text"
                  value={formData.petAge}
                  onChange={(e) => handleInputChange('petAge', e.target.value)}
                  placeholder="2 years, 6 months, etc."
                  className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"
                />
              </div>
            </div>
          </div>

          {/* Appointment Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <span>Appointment Details</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="service" className="text-gray-700 font-medium">
                  Service Required *
                </Label>
                <Select value={formData.service} onValueChange={(value) => handleInputChange('service', value)}>
                  <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80">
                    <SelectValue placeholder="Select a service" />
                  </SelectTrigger>
                  <SelectContent>
                    {services.map((service) => (
                      <SelectItem key={service} value={service}>
                        {service}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="preferredDate" className="text-gray-700 font-medium">
                  Preferred Date *
                </Label>
                <Input
                  id="preferredDate"
                  type="date"
                  value={formData.preferredDate}
                  onChange={(e) => handleInputChange('preferredDate', e.target.value)}
                  className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80"
                  min={new Date().toISOString().split('T')[0]}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="preferredTime" className="text-gray-700 font-medium">
                  Preferred Time
                </Label>
                <Select value={formData.preferredTime} onValueChange={(value) => handleInputChange('preferredTime', value)}>
                  <SelectTrigger className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80">
                    <SelectValue placeholder="Select time" />
                  </SelectTrigger>
                  <SelectContent>
                    {timeSlots.map((time) => (
                      <SelectItem key={time} value={time}>
                        {time}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Additional Message */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
              <MessageCircle className="w-5 h-5 text-pink-600" />
              <span>Additional Information</span>
            </h3>
            
            <div className="space-y-2">
              <Label htmlFor="message" className="text-gray-700 font-medium">
                Additional Message or Special Requirements
              </Label>
              <Textarea
                id="message"
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                placeholder="Please describe any specific concerns, symptoms, or special requirements for your pet..."
                className="border-gray-200 focus:border-blue-400 focus:ring-blue-400 bg-white/80 min-h-[100px]"
                rows={4}
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="pt-4 flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 border-gray-200 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-gradient-to-r from-blue-600 to-pink-600 hover:from-blue-700 hover:to-pink-700 text-white shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300"
            >
              {isSubmitting ? (
                <span className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Sending...</span>
                </span>
              ) : (
                <span className="flex items-center space-x-2">
                  <Phone className="w-4 h-4" />
                  <span>Send to WhatsApp</span>
                </span>
              )}
            </Button>
          </div>
          </form>
        </div>
      </div>
    </div>
  );
}
